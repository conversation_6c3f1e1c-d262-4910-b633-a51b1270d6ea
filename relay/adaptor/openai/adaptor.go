package openai

import (
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/songquanpeng/one-api/relay/adaptor"
	"github.com/songquanpeng/one-api/relay/adaptor/doubao"
	"github.com/songquanpeng/one-api/relay/adaptor/minimax"
	"github.com/songquanpeng/one-api/relay/adaptor/novita"
	"github.com/songquanpeng/one-api/relay/channeltype"
	"github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/relaymode"
)

type Adaptor struct {
	ChannelType int
}

func (a *Adaptor) Init(meta *meta.Meta) {
	a.ChannelType = meta.ChannelType
}

func (a *Adaptor) GetRequestURL(meta *meta.Meta) (string, error) {
	// 处理模型名称后缀，获取实际的模型名称
	actualModelName := meta.ActualModelName
	// 移除reasoning effort后缀
	if strings.HasSuffix(actualModelName, "-high") {
		actualModelName = strings.TrimSuffix(actualModelName, "-high")
	} else if strings.HasSuffix(actualModelName, "-low") {
		actualModelName = strings.TrimSuffix(actualModelName, "-low")
	} else if strings.HasSuffix(actualModelName, "-medium") {
		actualModelName = strings.TrimSuffix(actualModelName, "-medium")
	}

	switch meta.ChannelType {
	case channeltype.Azure:
		if meta.Mode == relaymode.ImagesGenerations {
			// https://learn.microsoft.com/en-us/azure/ai-services/openai/dall-e-quickstart?tabs=dalle3%2Ccommand-line&pivots=rest-api
			// https://{resource_name}.openai.azure.com/openai/deployments/dall-e-3/images/generations?api-version=2024-03-01-preview
			fullRequestURL := fmt.Sprintf("%s/openai/deployments/%s/images/generations?api-version=%s", meta.BaseURL, actualModelName, meta.Config.APIVersion)
			return fullRequestURL, nil
		}

		// 特殊处理 responses API
		if meta.Mode == constant.RelayModeResponses {
			requestURL := fmt.Sprintf("/openai/v1/responses?api-version=preview")
			return GetFullRequestURL(meta.BaseURL, requestURL, meta.ChannelType), nil
		}

		// https://learn.microsoft.com/en-us/azure/cognitive-services/openai/chatgpt-quickstart?pivots=rest-api&tabs=command-line#rest-api
		requestURL := strings.Split(meta.RequestURLPath, "?")[0]
		requestURL = fmt.Sprintf("%s?api-version=%s", requestURL, meta.Config.APIVersion)
		task := strings.TrimPrefix(requestURL, "/v1/")
		model_ := actualModelName
		model_ = strings.Replace(model_, ".", "", -1)
		//https://github.com/songquanpeng/one-api/issues/1191
		// {your endpoint}/openai/deployments/{your azure_model}/chat/completions?api-version={api_version}
		requestURL = fmt.Sprintf("/openai/deployments/%s/%s", model_, task)
		return GetFullRequestURL(meta.BaseURL, requestURL, meta.ChannelType), nil
	case channeltype.Minimax:
		return minimax.GetRequestURL(meta)
	case channeltype.Doubao:
		return doubao.GetRequestURL(meta)
	case channeltype.Novita:
		return novita.GetRequestURL(meta)
	default:
		return GetFullRequestURL(meta.BaseURL, meta.RequestURLPath, meta.ChannelType), nil
	}
}

func (a *Adaptor) SetupRequestHeader(c *gin.Context, req *http.Request, meta *meta.Meta) error {
	adaptor.SetupCommonRequestHeader(c, req, meta)
	if meta.ChannelType == channeltype.Azure {
		req.Header.Set("api-key", meta.APIKey)
		return nil
	}
	req.Header.Set("Authorization", "Bearer "+meta.APIKey)
	if meta.ChannelType == channeltype.OpenRouter {
		req.Header.Set("HTTP-Referer", "https://github.com/songquanpeng/one-api")
		req.Header.Set("X-Title", "One API")
	}
	return nil
}

func (a *Adaptor) ConvertRequest(c *gin.Context, relayMode int, request *model.GeneralOpenAIRequest) (any, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}

	// 处理 responses 模式
	if relayMode == constant.RelayModeResponses {
		// 处理模型后缀转换 reasoning effort
		if strings.HasSuffix(request.Model, "-high") {
			if request.Reasoning == nil {
				request.Reasoning = &model.Reasoning{}
			}
			request.Reasoning.Effort = "high"
			request.Model = strings.TrimSuffix(request.Model, "-high")
		} else if strings.HasSuffix(request.Model, "-low") {
			if request.Reasoning == nil {
				request.Reasoning = &model.Reasoning{}
			}
			request.Reasoning.Effort = "low"
			request.Model = strings.TrimSuffix(request.Model, "-low")
		} else if strings.HasSuffix(request.Model, "-medium") {
			if request.Reasoning == nil {
				request.Reasoning = &model.Reasoning{}
			}
			request.Reasoning.Effort = "medium"
			request.Model = strings.TrimSuffix(request.Model, "-medium")
		}
		return request, nil
	}

	if request.Stream {
		// always return usage in stream mode
		if request.StreamOptions == nil {
			request.StreamOptions = &model.StreamOptions{}
		}
		request.StreamOptions.IncludeUsage = true
	}

	// 处理o系列模型的reasoning effort后缀
	if strings.HasPrefix(request.Model, "o") {
		// 将max_tokens转换为max_completion_tokens
		if request.MaxCompletionTokens == nil && request.MaxTokens != 0 {
			request.MaxCompletionTokens = &request.MaxTokens
			request.MaxTokens = 0
		}

		// 清空temperature，o系列模型不支持temperature参数
		request.Temperature = nil

		// 处理reasoning effort后缀
		if strings.HasSuffix(request.Model, "-high") {
			if request.Reasoning == nil {
				request.Reasoning = &model.Reasoning{}
			}
			request.Reasoning.Effort = "high"
			request.Model = strings.TrimSuffix(request.Model, "-high")
		} else if strings.HasSuffix(request.Model, "-low") {
			if request.Reasoning == nil {
				request.Reasoning = &model.Reasoning{}
			}
			request.Reasoning.Effort = "low"
			request.Model = strings.TrimSuffix(request.Model, "-low")
		} else if strings.HasSuffix(request.Model, "-medium") {
			if request.Reasoning == nil {
				request.Reasoning = &model.Reasoning{}
			}
			request.Reasoning.Effort = "medium"
			request.Model = strings.TrimSuffix(request.Model, "-medium")
		}

		// o系列模型developer角色适配（o1-mini除外）
		if !strings.HasPrefix(request.Model, "o1-mini") && !strings.HasPrefix(request.Model, "o1-preview") {
			// 修改第一个Message的内容，将system改为developer
			if len(request.Messages) > 0 && request.Messages[0].Role == "system" {
				request.Messages[0].Role = "developer"
			}
		}
	}

	return request, nil
}

func (a *Adaptor) ConvertImageRequest(request *model.ImageRequest) (any, error) {
	if request == nil {
		return nil, errors.New("request is nil")
	}
	return request, nil
}

func (a *Adaptor) DoRequest(c *gin.Context, meta *meta.Meta, requestBody io.Reader) (*http.Response, error) {
	return adaptor.DoRequestHelper(a, c, meta, requestBody)
}

func (a *Adaptor) DoResponse(c *gin.Context, resp *http.Response, meta *meta.Meta) (usage *model.Usage, err *model.ErrorWithStatusCode) {
	// 处理 responses 模式
	if meta.Mode == constant.RelayModeResponses {
		if meta.IsStream {
			usage, err = ResponsesStreamHandler(c, resp, meta)
		} else {
			usage, err = ResponsesHandler(c, resp, meta)
		}
		return
	}

	if meta.IsStream {
		var responseText string
		var responseFunctionCallName string
		var responseFunctionCallArguments string
		err, responseText, responseFunctionCallName, responseFunctionCallArguments, _, usage = StreamHandler(c, resp, meta, meta.Mode)
		if usage == nil || usage.TotalTokens == 0 {
			usage = ResponseText2Usage(responseText, responseFunctionCallName, responseFunctionCallArguments, meta.ActualModelName, meta.PromptTokens, meta.UserId)
		}
		if usage.TotalTokens != 0 && usage.PromptTokens == 0 { // some channels don't return prompt tokens & completion tokens
			usage.PromptTokens = meta.PromptTokens
			usage.CompletionTokens = usage.TotalTokens - meta.PromptTokens
		}
	} else {
		switch meta.Mode {
		case relaymode.ImagesGenerations:
			err, _ = ImageHandler(c, resp)
		default:
			err, usage = Handler(c, resp, meta.PromptTokens, meta.ActualModelName)
		}
	}
	return
}

func (a *Adaptor) GetModelList() []string {
	_, modelList := GetCompatibleChannelMeta(a.ChannelType)
	return modelList
}

func (a *Adaptor) GetChannelName() string {
	channelName, _ := GetCompatibleChannelMeta(a.ChannelType)
	return channelName
}
