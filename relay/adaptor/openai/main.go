package openai

import (
	"bufio"
	"bytes"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"regexp"
	"strings"

	"github.com/songquanpeng/one-api/common/conv"
	"github.com/songquanpeng/one-api/common/openai"
	"github.com/songquanpeng/one-api/common/render"
	"github.com/songquanpeng/one-api/relay/adaptor/openai/anthropic"

	"math/rand"
	"sync"
	"sync/atomic"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"github.com/songquanpeng/one-api/common"
	"github.com/songquanpeng/one-api/common/config"
	"github.com/songquanpeng/one-api/common/ctxkey"
	"github.com/songquanpeng/one-api/common/helper"
	"github.com/songquanpeng/one-api/common/logger"
	"github.com/songquanpeng/one-api/common/random"
	"github.com/songquanpeng/one-api/middleware"
	outModel "github.com/songquanpeng/one-api/model"
	"github.com/songquanpeng/one-api/relay/constant"
	"github.com/songquanpeng/one-api/relay/meta"
	"github.com/songquanpeng/one-api/relay/model"
	"github.com/songquanpeng/one-api/relay/relaymode"
)

const (
	dataPrefix       = "data: "
	done             = "[DONE]"
	dataPrefixLength = len(dataPrefix)
)

func StreamHandler(c *gin.Context, resp *http.Response, meta *meta.Meta, relayMode int) (*model.ErrorWithStatusCode, string, string, string, string, *model.Usage) {
	// Check if this is a gpt-image-1 chat completion request
	if isGptImageChatMode, exists := c.Get("gpt_image_chat_mode"); exists && isGptImageChatMode.(bool) {
		// For gpt-image-1 streaming, we need to collect all stream data and store it
		var streamDataBuilder strings.Builder
		scanner := bufio.NewScanner(resp.Body)
		scanner.Split(bufio.ScanLines)

		for scanner.Scan() {
			data := scanner.Text()
			streamDataBuilder.WriteString(data + "\n")
		}

		// Store the collected stream data in context for later conversion
		c.Set("image_response_data", []byte(streamDataBuilder.String()))
		return nil, "", "", "", "", nil
	}

	hasTokenAd := c.GetInt("has_token_ad")
	advertisement := c.GetString("token_advertisement")
	adPosition := c.GetInt("token_ad_position")
	filterStreamAd := c.GetBool("filter_stream_ad")
	filterStreamAdMinSize := c.GetInt("filter_stream_ad_min_size")
	negativeOptimizationEnabled := c.GetBool("negative_optimization_enabled")
	negativeOptimizationTime := c.GetInt64("negative_optimization_time")
	negativeRandomOffset := c.GetInt64("negative_random_offset")
	originalModelFakeRespEnabled := c.GetBool("original_model_fake_resp_enabled")
	fakeCompletionIdEnabled := c.GetBool("fake_completion_id_enabled")
	claudeStreamEnabled := c.GetBool("claude_stream_enabled")
	thinkTagProcessingEnabled := c.GetBool("think_tag_processing_enabled")
	if !c.GetBool(ctxkey.IsV1MessagesPath) {
		claudeStreamEnabled = false
	}
	shouldIncludeUsage := meta.ShouldIncludeUsage

	var fakeCompletionId string
	if originalModelFakeRespEnabled {
		fakeCompletionId = helper.GenerateFakeCompletionIdProMax(meta.OriginModelName)
	}

	var responseTextBuilder strings.Builder
	var responseFunctionCallNameBuilder strings.Builder
	var responseFunctionCallArgumentsBuilder strings.Builder
	var reasoningContentBuilder strings.Builder
	var thinkingContentBuilder strings.Builder
	// 为think标签处理添加状态跟踪
	var thinkTagState *ThinkTagState
	if thinkTagProcessingEnabled {
		thinkTagState = &ThinkTagState{}
	}
	completionId := ""
	scanner := bufio.NewScanner(resp.Body)
	scanner.Split(bufio.ScanLines)
	var usage *model.Usage

	common.SetEventStreamHeaders(c)

	sensitiveWordsForCheck := ""
	isSensitive := false
	var adInserted int32
	isFirst := true
	isLast := false
	doneRendered := false
	continueScanning := true // 该变量用于控制是否终止扫描 因为我用break不生效

	var upstreamResponseBuilder strings.Builder
	var fullResponseBuilder strings.Builder

	// 只有在需要记录响应时才进行构建（考虑用户级别配置）
	shouldBuildUpstreamResponse := outModel.ShouldLogUpstreamResponse(meta.UserId)
	shouldBuildFullResponse := outModel.ShouldLogFullResponse(meta.UserId)

	// 是否已经返回usage
	var usageReturned bool

	// 标记是否已经从API获取到token计数 (Claude格式)
	var inputTokensFromAPI bool
	var outputTokensFromAPI bool

	var hasReturnedRoleMessage bool = false // 添加标记，记录是否已返回包含role的消息

	for continueScanning && scanner.Scan() {
		data := scanner.Text()
		// 只在需要时记录上游响应
		if shouldBuildUpstreamResponse {
			upstreamResponseBuilder.WriteString(data + "\n")
		}
		if len(data) < dataPrefixLength { // ignore blank line or wrong format
			continue
		}
		if data[:dataPrefixLength] != dataPrefix && data[:dataPrefixLength] != done {
			continue
		}

		// 处理负优化逻辑
		handleNegativeOptimization(negativeOptimizationEnabled, negativeOptimizationTime, negativeRandomOffset)

		// 伪造响应模型为原始模型名
		if originalModelFakeRespEnabled {
			originalModelFakeRespEnabled, data, completionId = doModelFakeResp(originalModelFakeRespEnabled, fakeCompletionIdEnabled, data, meta, fakeCompletionId)
		}

		// 如果启用了模拟OpenAI官方响应格式，则检查处理模型名映射
		if meta.GetMockOpenAICompleteFormatEnabled() {
			// 尝试解析响应
			var streamResponse ChatCompletionsStreamResponse
			if err := json.Unmarshal([]byte(data[dataPrefixLength:]), &streamResponse); err == nil {
				// 如果有Model字段，且在映射表中存在，则替换
				if streamResponse.Model != "" {
					if mappedModel := openai.GetMappedModel(streamResponse.Model); mappedModel != streamResponse.Model {
						streamResponse.Model = mappedModel
						// 重新封装响应
						jsonData, err := json.Marshal(streamResponse)
						if err == nil {
							data = dataPrefix + string(jsonData)
						}
					}
				}
			}
		}

		// 过滤广告
		if isAdFiltered, _ := FilterStreamAdvertisement(c, filterStreamAd, filterStreamAdMinSize, data); isAdFiltered {
			continue
		}

		// 处理广告插入
		handleAdInsertion(c, hasTokenAd, adPosition, &adInserted, advertisement)

		if strings.HasPrefix(data[dataPrefixLength:], done) {
			if claudeStreamEnabled {
				data = doClaudeResp(claudeStreamEnabled, c, data, meta, fakeCompletionId, isFirst, true)
				doneRendered = true
				continueScanning = false
				break
			} else {
				// 判断usageReturned,如果没有返回过usage,则返回一个空的usage
				if !usageReturned {
					if !meta.GetTrustUpstreamStreamUsageEnabled() || usage == nil || usage.TotalTokens == 0 || usage.PromptTokens == 0 || usage.CompletionTokens == 0 {
						// 计算usage时考虑thinking内容
						var fullContent string
						if thinkingContentBuilder.Len() > 0 {
							fullContent = responseTextBuilder.String() + thinkingContentBuilder.String()
						} else {
							fullContent = responseTextBuilder.String()
						}
						usage = ResponseText2Usage(fullContent, responseFunctionCallNameBuilder.String(), responseFunctionCallArgumentsBuilder.String(), meta.ActualModelName, meta.PromptTokens, meta.UserId)
					}
					if shouldIncludeUsage {
						chatCompletionsStreamResponseWithUsage := &ChatCompletionsStreamResponse{
							Id:      completionId,
							Object:  "chat.completion.chunk",
							Created: time.Now().Unix(),
							Model:   meta.RequestModel,
							Choices: []ChatCompletionsStreamResponseChoice{{}},
							Usage:   usage,
						}
						_ = render.ObjectData(c, chatCompletionsStreamResponseWithUsage)
					}
					usageReturned = true
				}
				if hasTokenAd == 1 && adPosition == 2 {
					ConstructStreamAdvertisement(c, advertisement)
				}
				render.StringData(c, data[:12])
				doneRendered = true
				continue
			}
		}

		// Claude响应处理
		if claudeStreamEnabled {
			data = doClaudeResp(claudeStreamEnabled, c, data, meta, fakeCompletionId, isFirst, isLast)
			fullResponseBuilder.WriteString(data)
			isFirst = false

			// 处理Claude v1/messages格式的thinking内容
			if c.GetBool(ctxkey.IsV1MessagesPath) {
				// 尝试解析响应以检测thinking内容
				var claudeResp anthropic.StreamResponse
				if err := json.Unmarshal([]byte(data[dataPrefixLength:]), &claudeResp); err == nil {
					// 处理message_start事件中的token计数
					if claudeResp.Type == "message_start" && claudeResp.Message != nil {
						if claudeResp.Message.Usage.InputTokens > 0 {
							if usage == nil {
								usage = &model.Usage{}
							}
							usage.PromptTokens = claudeResp.Message.Usage.InputTokens
							usage.InputTokens = claudeResp.Message.Usage.InputTokens
							usage.CacheCreationInputTokens = claudeResp.Message.Usage.CacheCreationInputTokens
							usage.CacheReadInputTokens = claudeResp.Message.Usage.CacheReadInputTokens
							usage.ServiceTier = claudeResp.Message.Usage.ServiceTier
							inputTokensFromAPI = true
						}
					}

					// 处理thinking内容
					if claudeResp.Type == "content_block_delta" && claudeResp.Delta != nil {
						if claudeResp.Delta.Type == "thinking_delta" && claudeResp.Delta.Thinking != "" {
							thinkingContentBuilder.WriteString(claudeResp.Delta.Thinking)
						}
					}

					// 处理message_delta中的最终token计数
					if claudeResp.Type == "message_delta" && claudeResp.Usage != nil {
						if claudeResp.Usage.OutputTokens > 0 {
							if usage == nil {
								usage = &model.Usage{}
							}
							usage.CompletionTokens = claudeResp.Usage.OutputTokens
							usage.OutputTokens = claudeResp.Usage.OutputTokens
							outputTokensFromAPI = true

							// 计算总tokens
							usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens
						}
					}
				}
			}
		} else {
			// 普通格式的响应
			fullResponseBuilder.WriteString(data + "\n")
		}

		// 检查是否是第一条消息，如果是且未返回过role消息，则构造并发送一个空的role消息
		if !hasReturnedRoleMessage {
			// 尝试解析为OpenAI流式响应格式
			var streamResponse ChatCompletionsStreamResponse
			err := json.Unmarshal([]byte(data[dataPrefixLength:]), &streamResponse)

			// 如果成功解析且有ID，则检查是否需要插入空role消息
			if err == nil && streamResponse.Id != "" {
				// 检查当前消息是否已经是一个包含role且content为空的消息
				isEmptyRoleMessage := false
				if len(streamResponse.Choices) > 0 &&
					streamResponse.Choices[0].Delta.Role == "assistant" &&
					(streamResponse.Choices[0].Delta.Content == "" || streamResponse.Choices[0].Delta.Content == nil) {
					isEmptyRoleMessage = true
				}

				// 如果当前消息已经是一个包含role且content为空的消息，则直接标记已返回role消息
				if isEmptyRoleMessage {
					hasReturnedRoleMessage = true
				} else {
					// 否则，插入一个包含role且content为空的消息
					hasReturnedRoleMessage = true

					// 构造空role消息
					roleMessage := ChatCompletionsStreamResponse{
						Id:      streamResponse.Id,
						Object:  "chat.completion.chunk",
						Created: helper.GetTimestamp(),
						Model:   streamResponse.Model,
						Choices: []ChatCompletionsStreamResponseChoice{
							{
								Index: 0,
								Delta: model.Message{
									Role:    "assistant",
									Content: "",
								},
								FinishReason: nil,
							},
						},
					}

					roleMessageBytes, _ := json.Marshal(roleMessage)
					roleMessageData := "data: " + string(roleMessageBytes)

					if !claudeStreamEnabled {
						render.StringData(c, roleMessageData)
					}
				}
			}
		}

		switch relayMode {
		case relaymode.ChatCompletions:
			var streamResponse ChatCompletionsStreamResponse
			err := json.Unmarshal([]byte(data[dataPrefixLength:]), &streamResponse)
			if err != nil {
				logger.SysError("error unmarshalling stream response: " + err.Error())
				if !claudeStreamEnabled {
					render.StringData(c, data)
				}
				continue
			}

			// 如果这条消息包含role，标记已返回role消息
			if len(streamResponse.Choices) > 0 && streamResponse.Choices[0].Delta.Role != "" {
				hasReturnedRoleMessage = true
			}

			if len(streamResponse.Choices) == 0 {
				// 分类讨论 如果是返回usage的情况下,则不continue
				if streamResponse.Usage != nil {
					usage = streamResponse.Usage
				}
				continue
			}

			// Think标签处理逻辑
			if thinkTagProcessingEnabled {
				for i, choice := range streamResponse.Choices {
					if choice.Delta.Content != nil {
						contentStr := conv.AsString(choice.Delta.Content)
						if contentStr != "" {
							// 处理think标签
							reasoning, content, hasUpdate := processStreamThinkFragment(contentStr, thinkTagState)
							if hasUpdate {
								// 如果有推理内容，添加到reasoning content并进行trim处理
								if reasoning != "" {
									trimmedReasoning := strings.TrimSpace(reasoning)
									if trimmedReasoning != "" {
										reasoningContentBuilder.WriteString(trimmedReasoning)
										// 为这个chunk创建reasoning_content字段
										streamResponse.Choices[i].Delta.ReasoningContent = trimmedReasoning
									}
								}
								// 更新普通内容
								if content != "" {
									streamResponse.Choices[i].Delta.Content = content
									responseTextBuilder.WriteString(content)
								} else {
									streamResponse.Choices[i].Delta.Content = ""
								}
							}
						}
					}
					updateFunctionCallBuilders(&responseFunctionCallNameBuilder, &responseFunctionCallArgumentsBuilder, choice.Delta)
				}

				// 如果进行了think标签处理，需要重新序列化响应
				if len(streamResponse.Choices) > 0 {
					processedData, err := json.Marshal(streamResponse)
					if err == nil {
						data = dataPrefix + string(processedData)
					}
				}
			} else {
				// 原有逻辑保持不变
				for _, choice := range streamResponse.Choices {
					// 先处理 reasoning_content
					if choice.Delta.ReasoningContent != "" {
						reasoningContentBuilder.WriteString(choice.Delta.ReasoningContent)
						// 将思考过程插入到实际内容之前
						responseTextBuilder.WriteString(choice.Delta.ReasoningContent)
					}
					// 再处理普通 content
					responseTextBuilder.WriteString(conv.AsString(choice.Delta.Content))
					updateFunctionCallBuilders(&responseFunctionCallNameBuilder, &responseFunctionCallArgumentsBuilder, choice.Delta)
				}
			}

			// 处理base64图片转换（在敏感词检查之前）
			if meta.Config.GetConvertBase64ToUrlEnabled() {
				data = processStreamBase64Images(data, meta)
			}

			// 校验敏感词
			if config.SensitiveWordsEnabled {
				isSensitive, sensitiveWordsForCheck, data = checkSensitiveWords(data, sensitiveWordsForCheck, meta)
				if isSensitive {
					render.StringData(c, data)
					continueScanning = false
					break
				}
			}

			if !claudeStreamEnabled {
				render.StringData(c, data)
			}
			if streamResponse.Usage != nil && streamResponse.Usage.PromptTokens > 0 && streamResponse.Usage.CompletionTokens > 0 {
				// 这里要判断只有usage有效的时候 才判定为真实返回
				usageReturned = true
				usage = streamResponse.Usage
			}
			if completionId == "" && streamResponse.Id != "" {
				completionId = streamResponse.Id
			}
		case relaymode.Completions:
			var streamResponse CompletionsStreamResponse
			err := json.Unmarshal(helper.StringToByteSlice(data[dataPrefixLength:]), &streamResponse)
			if err != nil {
				logger.SysError("error unmarshalling stream response: " + err.Error())
				continue
			}
			for _, choice := range streamResponse.Choices {
				responseTextBuilder.WriteString(choice.Text)
			}

			// 处理base64图片转换（在敏感词检查之前）
			if meta.Config.GetConvertBase64ToUrlEnabled() {
				data = processStreamBase64Images(data, meta)
			}

			// 校验敏感词
			if config.SensitiveWordsEnabled {
				isSensitive, sensitiveWordsForCheck, data = checkSensitiveWords(data, sensitiveWordsForCheck, meta)
				if isSensitive {
					render.StringData(c, data)
					continueScanning = false
					break
				}
			}
			if !claudeStreamEnabled {
				render.StringData(c, data)
			}
		}

		if meta.ResponseFirstByteTime_ == 0 {
			meta.ResponseFirstByteTime_ = helper.GetTimestamp()
			// 通知首字节已收到（用于首字节超时检查）
			if wrapper, exists := c.Get("timeout_wrapper"); exists {
				if timeoutWrapper, ok := wrapper.(*middleware.TimeoutWrapper); ok {
					timeoutWrapper.OnFirstByteReceived()
				}
			}
		}
	}

	if !continueScanning {
		// 向上游发送取消信号
		if scanner.Err() == nil {
			// 如果扫描过程中没有发生错误，说明是主动取消的
			// 关闭响应体，通知上游取消发送内容
			err := resp.Body.Close()
			if err != nil {
				logger.SysError("error closing response body: " + err.Error())
			}
		}
	}

	if err := scanner.Err(); err != nil {
		logger.SysError("error reading stream: " + err.Error())
	}

	if !doneRendered || !continueScanning {
		if !claudeStreamEnabled {
			render.Done(c)
		}
	}

	err := resp.Body.Close()
	if err != nil {
		return ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), "", "", "", "", nil
	}

	if !originalModelFakeRespEnabled {
		if strings.Contains(completionId, "msg_vrtx_") {
			// 切分 保留msg_vrtx_后面的部分
			config.TempClaudeChatCompletionID = "msg_" + strings.Split(completionId, "msg_vrtx_")[1]
		} else if strings.Contains(completionId, "msg_") {
			// 切分 保留msg_后面的部分
			config.TempClaudeChatCompletionID = "msg_" + strings.Split(completionId, "msg_")[1]
		} else if strings.HasPrefix(completionId, "chatcmpl-") {
			config.TempOpenAIChatCompletionID = completionId
		}
	}

	// 只在需要时保存响应
	if shouldBuildUpstreamResponse {
		meta.UpstreamResponse = upstreamResponseBuilder.String()
	}
	if shouldBuildFullResponse {
		meta.FullResponse = fullResponseBuilder.String()
	}

	// 如果是Claude v1/messages但没有获取到API提供的计数，执行自主计算
	if c.GetBool(ctxkey.IsV1MessagesPath) && (usage == nil || (!inputTokensFromAPI || !outputTokensFromAPI)) {
		if usage == nil {
			usage = &model.Usage{}
		}

		// 处理input_tokens
		if !inputTokensFromAPI {
			if meta.PromptTokens > 0 {
				usage.PromptTokens = meta.PromptTokens
			}
		}

		// 处理output_tokens
		if !outputTokensFromAPI {
			// 计算文本输出的token数（包括thinking内容）
			fullText := responseTextBuilder.String()
			if thinkingContentBuilder.Len() > 0 {
				fullText += thinkingContentBuilder.String()
			}

			if len(fullText) > 0 {
				usage.CompletionTokens = lo.If(outModel.GetNewTikTokenBilling(meta.UserId),
					CountTokenTextNew(fullText, meta.ActualModelName)).
					Else(CountTokenText(fullText, meta.ActualModelName))
			}
		}

		// 计算总tokens
		usage.TotalTokens = usage.PromptTokens + usage.CompletionTokens
	}

	return nil, responseTextBuilder.String(), responseFunctionCallNameBuilder.String(), responseFunctionCallArgumentsBuilder.String(), completionId, usage
}

func StreamHandlerNew(c *gin.Context, resp *http.Response, meta *meta.Meta, relayMode int) (*model.ErrorWithStatusCode, string, string, string, string, *model.Usage) {
	// Check if this is a gpt-image-1 chat completion request
	if isGptImageChatMode, exists := c.Get("gpt_image_chat_mode"); exists && isGptImageChatMode.(bool) {
		// For gpt-image-1 streaming, we need to collect all stream data and store it
		var streamDataBuilder strings.Builder
		scanner := bufio.NewScanner(resp.Body)
		scanner.Split(bufio.ScanLines)

		for scanner.Scan() {
			data := scanner.Text()
			streamDataBuilder.WriteString(data + "\n")
		}

		// Store the collected stream data in context for later conversion
		c.Set("image_response_data", []byte(streamDataBuilder.String()))
		return nil, "", "", "", "", nil
	}

	hasTokenAd := c.GetInt("has_token_ad")
	advertisement := c.GetString("token_advertisement")
	adPosition := c.GetInt("token_ad_position")
	filterStreamAd := c.GetBool("filter_stream_ad")
	filterStreamAdMinSize := c.GetInt("filter_stream_ad_min_size")
	negativeOptimizationEnabled := c.GetBool("negative_optimization_enabled")
	negativeOptimizationTime := c.GetInt64("negative_optimization_time")
	negativeRandomOffset := c.GetInt64("negative_random_offset")
	originalModelFakeRespEnabled := c.GetBool("original_model_fake_resp_enabled")
	fakeCompletionIdEnabled := c.GetBool("fake_completion_id_enabled")

	// 是强制返回claude格式的流式响应用于在claude中使用
	claudeStreamEnabled := c.GetBool("claude_stream_enabled")
	if !c.GetBool(ctxkey.IsV1MessagesPath) {
		// 如果不是是v1/messages路径,则不返回claude格式的流式响应
		claudeStreamEnabled = false
	}
	var fakeCompletionId string
	// 有广告语,则插入
	if hasTokenAd == 1 && adPosition == 1 {
		ConstructStreamAdvertisement(c, advertisement)
	}
	// 开启了伪装则先生成id
	if originalModelFakeRespEnabled {
		if config.TempOpenAIChatCompletionID == "" || !strings.HasPrefix(config.TempOpenAIChatCompletionID, "chatcmpl-") {
			fakeCompletionId = fmt.Sprintf("chatcmpl-%s", random.GetRandomString(29))
		} else {
			// 保留TempOpenAIChatCompletionID的前13位,拼接后面的随机字符串
			// 需判断长度是否足够
			if len(config.TempOpenAIChatCompletionID) < 13 {
				fakeCompletionId = fmt.Sprintf("chatcmpl-%s", random.GetRandomString(29))
			} else {
				fakeCompletionId = fmt.Sprintf("%s%s", config.TempOpenAIChatCompletionID[:13], random.GetRandomString(25))
			}
		}
	}
	var responseTextBuilder strings.Builder
	var responseFunctionCallNameBuilder strings.Builder
	var responseFunctionCallArgumentsBuilder strings.Builder
	completionId := ""
	scanner := bufio.NewScanner(resp.Body)
	scanner.Split(func(data []byte, atEOF bool) (advance int, token []byte, err error) {
		if atEOF && len(data) == 0 {
			return 0, nil, nil
		}
		if i := strings.Index(string(data), "\n"); i >= 0 {
			return i + 1, data[0:i], nil
		}
		if atEOF {
			return len(data), data, nil
		}
		return 0, nil, nil
	})
	var dataChan chan string
	var stopChan chan bool
	var usage *model.Usage
	if config.OpenAIStreamStringBufferEnabled {
		if config.OpenAIStreamStringBufferSize != 0 {
			// 取配置大小
			dataChan = make(chan string, config.OpenAIStreamStringBufferSize)
		} else {
			// 默认5
			dataChan = make(chan string, 5)
		}
		stopChan = make(chan bool, 2)
	} else {
		dataChan = make(chan string)
		stopChan = make(chan bool)
	}
	defer close(stopChan)
	defer close(dataChan)
	sensitiveWordsForCheck := ""
	isSensitive := false
	loopDataMax := 50 // 防止openai抽风
	lastData := ""
	// 记录上一轮开始时间
	lastLoopStartTime := helper.GetTimestamp()
	// 告警阈值
	var wg sync.WaitGroup
	wg.Add(1)
	helper.SafeGoroutine(func() {
		defer wg.Done()
		var streamItems []string
		if config.StreamChunkTimeout > 0 {
			timeout := time.After(time.Duration(config.StreamChunkTimeout) * time.Second) // 设置超时时间，这里假设为 5 秒
		outerLoop:
			for {
				select {
				case <-timeout:
					// 超时处理
					logger.SysError(fmt.Sprintf("stream scan timeout and the interrupted stream is %v", streamItems))
					break outerLoop
				default:
					if !scanner.Scan() {
						break outerLoop
					}
					if config.StreamChunkTimeoutWarningLogThreshold > 0 {
						// 打印超时告警日志
						duration := helper.GetTimestamp() - lastLoopStartTime
						if duration > config.StreamChunkTimeoutWarningLogThreshold {
							logger.SysError(fmt.Sprintf("requestId [%s] channel [%d] model [%s] stream chunk scan duration is too long:%vs", meta.RequestId, meta.ChannelId, meta.ActualModelName, duration))
						}
						lastLoopStartTime = helper.GetTimestamp()
					}
					data := scanner.Text()
					// 防止openai抽风 start
					if loopDataMax <= 0 {
						break outerLoop
					}
					if data == lastData && data != "" {
						loopDataMax--
					} else {
						loopDataMax = 50
					}
					lastData = data
					// 防止openai抽风 end
					if len(data) < dataPrefixLength { // ignore blank line or wrong format
						continue outerLoop
					}
					if data[:dataPrefixLength] != dataPrefix && data[:dataPrefixLength] != done {
						continue outerLoop
					}
					dataChan <- data
					data = data[dataPrefixLength:]
					if !strings.HasPrefix(data, done) {
						streamItems = append(streamItems, data)
					}
					timeout = time.After(time.Duration(config.StreamChunkTimeout) * time.Second) // 重置超时时间
				}
			}
		} else {
			for scanner.Scan() {
				if config.StreamChunkTimeoutWarningLogThreshold > 0 {
					// 打印超时告警日志
					duration := helper.GetTimestamp() - lastLoopStartTime
					if duration > config.StreamChunkTimeoutWarningLogThreshold {
						logger.SysError(fmt.Sprintf("requestId [%s] channel [%d] model [%s] stream chunk scan duration is too long:%vs", meta.RequestId, meta.ChannelId, meta.ActualModelName, duration))
					}
					lastLoopStartTime = helper.GetTimestamp()
				}
				data := scanner.Text()
				// 防止openai抽风 start
				if loopDataMax <= 0 {
					break
				}
				if data == lastData && data != "" {
					loopDataMax--
				} else {
					loopDataMax = 50
				}
				lastData = data
				// 防止openai抽风 end
				if len(data) < dataPrefixLength { // ignore blank line or wrong format
					continue
				}
				if data[:dataPrefixLength] != dataPrefix && data[:dataPrefixLength] != done {
					continue
				}
				dataChan <- data
				data = data[dataPrefixLength:]
				if !strings.HasPrefix(data, done) {
					streamItems = append(streamItems, data)
				}
			}
		}
		streamResp := "[" + strings.Join(streamItems, ",") + "]"
		switch relayMode {
		case relaymode.ChatCompletions:
			var streamResponses []ChatCompletionsStreamResponse
			err := json.Unmarshal(helper.StringToByteSlice(streamResp), &streamResponses)
			if err != nil {
				logger.SysError("error unmarshalling stream response: " + err.Error())
				stopChan <- true
				return // just ignore the error
			}
			for _, streamResponse := range streamResponses {
				if streamResponse.Usage != nil {
					usage = streamResponse.Usage
				}
				if streamResponse.Id != "" {
					completionId = streamResponse.Id
				}
				for _, choice := range streamResponse.Choices {
					responseTextBuilder.WriteString(choice.Delta.StringContent())
					if choice.Delta.GetFunctionCallName() != "" {
						responseFunctionCallNameBuilder.WriteString(choice.Delta.GetFunctionCallName())
					}
					if choice.Delta.GetFunctionCallArguments() != "" {
						responseFunctionCallArgumentsBuilder.WriteString(choice.Delta.GetFunctionCallArguments())
					}
					if choice.Delta.ToolCalls != nil && len(choice.Delta.ToolCalls) > 0 {
						for _, toolCall := range choice.Delta.ToolCalls {
							if toolCall.Function.Name != "" {
								responseFunctionCallNameBuilder.WriteString(toolCall.Function.Name)
							}
							if toolCall.Function.Arguments != nil {
								// 将 Arguments 转换为字符串
								argsStr := model.GetArgumentsString(toolCall.Function.Arguments)
								responseFunctionCallArgumentsBuilder.WriteString(argsStr)
							}
						}
					}
				}
			}
		case relaymode.Completions:
			var streamResponses []CompletionsStreamResponse
			err := json.Unmarshal(helper.StringToByteSlice(streamResp), &streamResponses)
			if err != nil {
				logger.SysError("error unmarshalling stream response: " + err.Error())
				stopChan <- true
				return // just ignore the error
			}
			for _, streamResponse := range streamResponses {
				for _, choice := range streamResponse.Choices {
					responseTextBuilder.WriteString(choice.Text)
				}
			}
		}
		stopChan <- true
	})
	common.SetEventStreamHeaders(c)
	// Track the current position.
	currentPosition := 5
	// We can use a flag to track if the ad has been inserted.
	var adInserted int32
	var isFirst = true // 转换claude响应的时候用,用来保存第一次特殊处理
	var isLast = false // 转换claude响应的时候用,用来保存最后一次特殊处理
	c.Stream(func(w io.Writer) bool {
		select {
		case data := <-dataChan:
			// 判断渠道负优化逻辑
			if negativeOptimizationEnabled {
				if negativeRandomOffset > 0 && negativeOptimizationTime > negativeRandomOffset {
					// 生成一个 -rangeInt 到 rangeInt 之间的随机数
					randomSeconds := rand.Int63n(2*negativeRandomOffset+1) - negativeRandomOffset
					negativeOptimizationTime = negativeOptimizationTime + randomSeconds
				}
				time.Sleep(time.Duration(negativeOptimizationTime) * time.Millisecond)
			}
			// 校验敏感词
			if config.SensitiveWordsEnabled {
				var streamResponses []ChatCompletionsStreamResponse
				trimData := strings.TrimPrefix(data, "data: ")
				streamResp := "[" + trimData + "]"
				_ = json.Unmarshal(helper.StringToByteSlice(streamResp), &streamResponses)
				for _, streamResponse := range streamResponses {
					for _, choice := range streamResponse.Choices {
						sensitiveWordsForCheck += choice.Delta.StringContent()
					}
				}
				isSens, word, tips := outModel.CheckSensitiveWord(sensitiveWordsForCheck)
				if isSens || isSensitive {
					isSensitive = true
					logger.SysError("sensitive word detected: " + word)
					ConstructStreamAdvertisement(c, tips)
					return false
				}
			}
			// 伪造响应模型为原始模型名
			originalModelFakeRespEnabled, data, _ = doModelFakeResp(originalModelFakeRespEnabled, fakeCompletionIdEnabled, data, meta, fakeCompletionId)
			// 过滤广告并返回是否有广告语的bool类型
			isAdFiltered, adFilterErr := FilterStreamAdvertisement(c, filterStreamAd, filterStreamAdMinSize, data)
			if adFilterErr != nil {
				logger.SysError("error filter stream advertisement: " + adFilterErr.Error())
			}
			if isAdFiltered {
				return true
			}
			if hasTokenAd == 1 && adPosition == 3 && atomic.LoadInt32(&adInserted) == 0 {
				currentPosition++
				// Generate a random number between 0 and currentPosition (inclusive).
				randNumber := rand.Intn(currentPosition + 1)
				// If the random number is 0, insert the ad.
				// This means the probability of inserting the ad is 1/(currentPosition+1).
				if randNumber == 0 {
					atomic.StoreInt32(&adInserted, 1)
					ConstructStreamAdvertisement(c, advertisement)
				}
			}
			if strings.HasPrefix(data, "data: [DONE]") {
				if claudeStreamEnabled {
					data = doClaudeResp(claudeStreamEnabled, c, data, meta, fakeCompletionId, isFirst, true)
					return false
				}
				if hasTokenAd == 1 && adPosition == 2 {
					ConstructStreamAdvertisement(c, advertisement)
				}
				data = data[:12]
			}
			// some implementations may add \r at the end of data
			data = strings.TrimSuffix(data, "\r")
			// claude响应
			if claudeStreamEnabled {
				// 第一次特殊处理
				if isFirst {
					isFirst = false
				}
				data = doClaudeResp(claudeStreamEnabled, c, data, meta, fakeCompletionId, isFirst, isLast)
				return true
			}
			c.Render(-1, common.CustomEvent{Data: data})
			if meta.ResponseFirstByteTime_ == 0 {
				meta.ResponseFirstByteTime_ = helper.GetTimestamp()
				// 通知首字节已收到（用于首字节超时检查）
				if wrapper, exists := c.Get("timeout_wrapper"); exists {
					if timeoutWrapper, ok := wrapper.(*middleware.TimeoutWrapper); ok {
						timeoutWrapper.OnFirstByteReceived()
					}
				}
			}
			return true
		case <-stopChan:
			// Process all remaining items in dataChan
			for len(dataChan) > 0 {
				data := <-dataChan
				// 过滤广告并返回是否有广告语的bool类型
				isAdFiltered, adFilterErr := FilterStreamAdvertisement(c, filterStreamAd, filterStreamAdMinSize, data)
				if adFilterErr != nil {
					logger.SysError("error filter stream advertisement: " + adFilterErr.Error())
				}
				if isAdFiltered {
					return false
				}
				if strings.HasPrefix(data, "data: [DONE]") {
					if hasTokenAd == 1 && adPosition == 2 {
						ConstructStreamAdvertisement(c, advertisement)
					}
					data = data[:12]
				}
				// some implementations may add \r at the end of data
				data = strings.TrimSuffix(data, "\r")
				// 伪造响应模型为原始模型名
				originalModelFakeRespEnabled, data, _ = doModelFakeResp(originalModelFakeRespEnabled, fakeCompletionIdEnabled, data, meta, fakeCompletionId)
				c.Render(-1, common.CustomEvent{Data: data})
			}
			return false
		}
	})
	err := resp.Body.Close()
	if err != nil {
		return ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), "", "", "", "", nil
	}
	wg.Wait()
	if !originalModelFakeRespEnabled && strings.HasPrefix(completionId, "chatcmpl-") {
		config.TempOpenAIChatCompletionID = completionId
	}

	return nil, responseTextBuilder.String(), responseFunctionCallNameBuilder.String(), responseFunctionCallArgumentsBuilder.String(), completionId, usage
}

func doModelFakeResp(originalModelFakeRespEnabled bool, fakeCompletionIdEnabled bool, data string, meta *meta.Meta, fakeCompletionId string) (bool, string, string) {
	originalCompletionId := ""
	if originalModelFakeRespEnabled {
		var streamResponses []ChatCompletionsStreamResponse
		trimData := strings.TrimPrefix(data, "data: ")
		streamResp := "[" + trimData + "]"
		_ = json.Unmarshal(helper.StringToByteSlice(streamResp), &streamResponses)
		for i := 0; i < len(streamResponses); i++ {
			streamResponses[i].Model = meta.OriginModelName
			if len(streamResponses[i].Id) > 0 {
				originalCompletionId = streamResponses[i].Id
				if fakeCompletionIdEnabled {
					streamResponses[i].Id = fakeCompletionId
				}
				if meta.IsAdminPass {
					lastContent := streamResponses[i].Choices[0].Delta.StringContent()
					streamResponses[i].Choices[0].Delta.Content = lastContent + " " + meta.ActualModelName
				}
			}
		}
		if len(streamResponses) > 0 {
			dataBytes, err := json.Marshal(streamResponses[0])
			if err == nil {
				data = "data: " + string(dataBytes)
			}
		}
	}
	return originalModelFakeRespEnabled, data, originalCompletionId
}

func doClaudeResp(claudeRespEnabled bool, c *gin.Context, data string, meta *meta.Meta, fakeCompletionId string, isFirst bool, isLast bool) string {
	if claudeRespEnabled {
		if isLast {
			data = "event: content_block_stop\n" +
				"data: {\"type\":\"content_block_stop\",\"index\":0}\n\n" +
				"event: message_delta\n" +
				"data: {\"type\":\"message_delta\",\"delta\":{\"stop_reason\":\"tool_use\",\"stop_sequence\":null},\"usage\":{\"output_tokens\":1}}\n\n" +
				"event: message_stop\n" +
				"data: {\"type\":\"message_stop\"}\n\n"
			c.Render(-1, common.CustomClaudeEvent{Data: data})
			return data
		}
		var streamResponses []ChatCompletionsStreamResponse
		trimData := strings.TrimPrefix(data, "data: ")
		streamResp := "[" + trimData + "]"
		_ = json.Unmarshal(helper.StringToByteSlice(streamResp), &streamResponses)
		for i := 0; i < len(streamResponses); i++ {
			content := streamResponses[i].GetFirstChoiceDeltaContent()
			if isFirst {
				data = "event: message_start\n" +
					"data: {\"type\":\"message_start\",\"message\":{\"id\":\"" + fakeCompletionId + "\",\"type\":\"message\",\"role\":\"assistant\",\"model\":\"" + meta.ActualModelName + "\",\"stop_sequence\":null,\"usage\":{\"input_tokens\":1,\"output_tokens\":1},\"content\":[],\"stop_reason\":null}}\n\n" +
					"event: content_block_start\n" +
					"data: {\"type\":\"content_block_start\",\"index\":0,\"content_block\":{\"type\":\"text\",\"text\":\"\"}}\n\n" +
					"event: ping\n" +
					"data: {\"type\": \"ping\"}\n\n" +
					"event: content_block_delta\n" +
					"data: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\"" + content + "\"}}\n\n"
				c.Render(-1, common.CustomClaudeEvent{Data: data})
			} else {
				data = "event: content_block_delta\n" +
					"data: {\"type\":\"content_block_delta\",\"index\":0,\"delta\":{\"type\":\"text_delta\",\"text\":\"" + content + "\"}}\n\n"
				c.Render(-1, common.CustomClaudeEvent{Data: data})
			}
		}
		if len(streamResponses) > 0 {
			return data
		}
	}
	return data
}

func Handler(c *gin.Context, resp *http.Response, promptTokens int, modelName string) (*model.ErrorWithStatusCode, *model.Usage) {
	userId := c.GetInt(ctxkey.Id)
	hasTokenAd := c.GetInt("has_token_ad")
	advertisement := c.GetString("token_advertisement")
	adPosition := c.GetInt("token_ad_position")
	negativeOptimizationEnabled := c.GetBool("negative_optimization_enabled")
	negativeOptimizationTime := c.GetInt64("negative_optimization_time")
	negativeRandomOffset := c.GetInt64("negative_random_offset")
	originalModelFakeRespEnabled := c.GetBool("original_model_fake_resp_enabled")
	fakeCompletionIdEnabled := c.GetBool("fake_completion_id_enabled")

	var fakeCompletionId string
	var textResponse SlimTextResponse
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return ErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError), nil
	}
	err = resp.Body.Close()
	if err != nil {
		return ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), nil
	}

	// Check if this is a gpt-image-1 chat completion request
	if isGptImageChatMode, exists := c.Get("gpt_image_chat_mode"); exists && isGptImageChatMode.(bool) {
		// Store the response data in context for later conversion
		c.Set("image_response_data", responseBody)

		// Try to parse as image response to get usage information
		var imageResponse ImageResponse
		if err := json.Unmarshal(responseBody, &imageResponse); err == nil && imageResponse.Usage.TotalTokens > 0 {
			// Return the image response usage
			return nil, &imageResponse.Usage
		}

		// If parsing fails or no usage, calculate basic usage based on prompt tokens
		usage := &model.Usage{
			PromptTokens:     promptTokens,
			CompletionTokens: 1, // Minimal completion tokens for image generation
			TotalTokens:      promptTokens + 1,
		}
		return nil, usage
	}

	err = json.Unmarshal(responseBody, &textResponse)
	if err != nil {
		return ErrorWrapper(err, "unmarshal_response_body_failed", http.StatusInternalServerError), nil
	}
	if textResponse.Error != nil && textResponse.Error.Type != "" {
		return &model.ErrorWithStatusCode{
			Error:      *textResponse.Error,
			StatusCode: resp.StatusCode,
		}, nil
	}
	// 开启了伪装则先生成id
	if originalModelFakeRespEnabled {
		if config.TempOpenAIChatCompletionID == "" || !strings.HasPrefix(config.TempOpenAIChatCompletionID, "chatcmpl-") {
			fakeCompletionId = fmt.Sprintf("chatcmpl-%s", random.GetRandomString(29))
		} else {
			// 保留TempOpenAIChatCompletionID的前13位,拼接后面的随机字符串
			// 需判断长度是否足够
			if len(config.TempOpenAIChatCompletionID) < 13 {
				fakeCompletionId = fmt.Sprintf("chatcmpl-%s", random.GetRandomString(29))
			} else {
				fakeCompletionId = fmt.Sprintf("%s%s", config.TempOpenAIChatCompletionID[:13], random.GetRandomString(25))
			}
		}
	} else if strings.HasPrefix(textResponse.Id, "chatcmpl-") {
		config.TempOpenAIChatCompletionID = textResponse.Id
	}
	// 有广告语,则插入
	if hasTokenAd == 1 {
		ConstructAdvertisement(&textResponse, advertisement, adPosition)
		// 将含有广告的textResponse转化为responseBody
		responseBody, err = json.Marshal(textResponse)
		if err != nil {
			return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil
		}
	}
	if originalModelFakeRespEnabled {
		textResponse.Model = modelName
		if len(textResponse.Id) > 0 && fakeCompletionIdEnabled {
			textResponse.Id = fakeCompletionId
		}
		responseBody, err = json.Marshal(textResponse)
		if err != nil {
			return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil
		}
	}
	// 判断是否需要负优化
	if negativeOptimizationEnabled {
		if negativeRandomOffset > 0 && negativeOptimizationTime > negativeRandomOffset {
			// 生成一个 -rangeInt 到 rangeInt 之间的随机数
			randomSeconds := rand.Int63n(2*negativeRandomOffset+1) - negativeRandomOffset
			negativeOptimizationTime = negativeOptimizationTime + randomSeconds
		}
		time.Sleep(time.Duration(negativeOptimizationTime) * time.Millisecond)
	}
	// Reset response body
	resp.Body = io.NopCloser(bytes.NewBuffer(responseBody))
	// We shouldn't set the header before we parse the response body, because the parse part may fail.
	// And then we will have to send an error response, but in this case, the header has already been set.
	// So the HTTPClient will be confused by the response.
	// For example, Postman will report error, and we cannot check the response at all.
	// For example, Postman will report error, and we cannot check the response at all.
	for k, v := range resp.Header {
		// 排除这个请求头X-Served-By 避免暴露上游域名
		if strings.ToLower(k) == "x-served-by" {
			continue
		}
		c.Writer.Header().Set(k, v[0])
	}
	if advertisement != "" {
		// 重新计算Content-Length避免追加广告后Content-Length超限报错导致非流式无法返回
		if len(responseBody) > 0 {
			c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", len(responseBody)))
		} else {
			c.Writer.Header().Del("Content-Length")
		}
	}
	c.Writer.WriteHeader(resp.StatusCode)
	_, err = io.Copy(c.Writer, resp.Body)
	if err != nil {
		return ErrorWrapper(err, "copy_response_body_failed", http.StatusInternalServerError), nil
	}
	err = resp.Body.Close()
	if err != nil {
		return ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), nil
	}

	if textResponse.Usage.TotalTokens == 0 || (textResponse.Usage.PromptTokens == 0 && textResponse.Usage.CompletionTokens == 0) {
		completionTokens := 0
		for _, choice := range textResponse.Choices {
			completionTokens += lo.If(outModel.GetNewTikTokenBilling(userId), CountTokenTextNew(choice.Message.StringContent(), modelName)).Else(CountTokenText(choice.Message.StringContent(), modelName))
		}
		textResponse.Usage = model.Usage{
			PromptTokens:     promptTokens,
			CompletionTokens: completionTokens,
			TotalTokens:      promptTokens + completionTokens,
		}
	}
	return nil, &textResponse.Usage
}

func HandlerWithResponse(c *gin.Context, resp *http.Response, meta *meta.Meta, promptTokens int, requestModel string) (*model.ErrorWithStatusCode, *model.Usage, string, string) {
	hasTokenAd := c.GetInt("has_token_ad")
	advertisement := c.GetString("token_advertisement")
	adPosition := c.GetInt("token_ad_position")
	negativeOptimizationEnabled := c.GetBool("negative_optimization_enabled")
	negativeOptimizationTime := c.GetInt64("negative_optimization_time")
	negativeRandomOffset := c.GetInt64("negative_random_offset")
	originalModelFakeRespEnabled := c.GetBool("original_model_fake_resp_enabled")
	fakeCompletionIdEnabled := c.GetBool("fake_completion_id_enabled")

	usageRecalculationEnabled := c.GetBool("usage_recalculation_enabled")
	claudeStreamEnabled := c.GetBool("claude_stream_enabled")
	excludedResponseFields := c.GetString(ctxkey.ExcludedResponseFields)
	thinkTagProcessingEnabled := c.GetBool("think_tag_processing_enabled")
	respBodyChanged := false
	if !c.GetBool(ctxkey.IsV1MessagesPath) {
		claudeStreamEnabled = false
	}
	var embeddingResponse EmbeddingResponse
	var imageResponse ImageResponse
	var textResponse SlimTextResponse
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return ErrorWrapper(err, "read_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), ""
	}

	// 只在需要时记录上游响应
	if outModel.ShouldLogDetail(meta.UserId) && config.LogUpstreamResponseEnabled {
		meta.UpstreamResponse = string(responseBody)
	}

	err = resp.Body.Close()
	if err != nil {
		return ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), ""
	}

	// Check if this is a gpt-image-1 chat completion request
	if isGptImageChatMode, exists := c.Get("gpt_image_chat_mode"); exists && isGptImageChatMode.(bool) {
		// Store the response data in context for later conversion
		c.Set("image_response_data", responseBody)
		// 替换meta当中的full response
		meta.FullResponse = c.GetString("gpt_image_downstream_response")
		// Try to parse as image response to get usage information
		var imageResponse ImageResponse
		if err := json.Unmarshal(responseBody, &imageResponse); err == nil && imageResponse.Usage.TotalTokens > 0 {
			// Return the image response usage
			return nil, &imageResponse.Usage, string(responseBody), ""
		}

		// If parsing fails or no usage, calculate basic usage based on prompt tokens
		usage := &model.Usage{
			PromptTokens:     promptTokens,
			CompletionTokens: 1, // Minimal completion tokens for image generation
			TotalTokens:      promptTokens + 1,
		}
		return nil, usage, string(responseBody), ""
	}

	if strings.Contains(meta.OriginModelName, "embedding") {
		err = json.Unmarshal(responseBody, &embeddingResponse)
	} else if meta.IsImageModeNeedReturnUsage() {
		err = json.Unmarshal(responseBody, &imageResponse)
	} else {
		err = json.Unmarshal(responseBody, &textResponse)
	}
	if err != nil {
		return ErrorWrapper(errors.New(fmt.Sprintf("解析响应体失败,错误为: %s,原始响应为: %s", err.Error(), string(responseBody))), "unmarshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), ""
	}
	if textResponse.Error != nil && textResponse.Error.Type != "" {
		return &model.ErrorWithStatusCode{
			Error:      *textResponse.Error,
			StatusCode: resp.StatusCode,
		}, nil, textResponse.GetFirstChoiceMessage(), textResponse.Id
	}

	// Think标签处理逻辑 - 针对非流式响应
	if thinkTagProcessingEnabled && len(textResponse.Choices) > 0 {
		for i, choice := range textResponse.Choices {
			if choice.Message.Content != "" {
				contentStr := conv.AsString(choice.Message.Content)
				if contentStr != "" {
					// 提取思考内容和清理后的普通内容
					extractedThinking, cleanedContent, hasThinking := processThinkTags(contentStr)
					if hasThinking {
						// 将思考内容设置到reasoning_content字段，并进行trim处理
						textResponse.Choices[i].Message.ReasoningContent = strings.TrimSpace(extractedThinking)
						// 更新content为清理后的内容
						textResponse.Choices[i].Message.Content = cleanedContent
						// 标记响应体已改变
						respBodyChanged = true
					}
				}
			}
		}
	}

	// 处理 reasoning_content
	if len(textResponse.Choices) > 0 && textResponse.Choices[0].Message.ReasoningContent != "" {
		// 如果未启用think标签处理，将 reasoning_content 添加到 content 之前（保持向后兼容）
		if !thinkTagProcessingEnabled {
			combinedContent := textResponse.Choices[0].Message.ReasoningContent + textResponse.Choices[0].Message.StringContent()
			textResponse.Choices[0].Message.Content = combinedContent
		}
		// 如果启用了think标签处理，reasoning_content 和 content 保持分离
	}

	haveSensitiveWord, tips := filterNonStreamSensitiveWords(meta, config.SensitiveWordsEnabled, textResponse.GetFirstChoiceMessage())
	if haveSensitiveWord {
		return ErrorWrapper(errors.New(config.SensitiveWordsTips), "sensitive_words_in_request", http.StatusBadRequest), nil, tips, textResponse.Id
	}
	haveNoStreamAdvertisement, filteredData, err := FilterNonStreamAdvertisement(c, c.GetBool("filter_non_stream_ad"), c.GetString("filter_non_stream_ad_regex"), textResponse.GetFirstChoiceMessage())
	if err != nil {
		logger.SysError("error filter non stream advertisement: " + err.Error())
	}
	if haveNoStreamAdvertisement {
		// 先判空
		if len(textResponse.Choices) > 0 {
			textResponse.SetFirstChoiceMessage(filteredData)
			responseBody, err = json.Marshal(textResponse)
			if err != nil {
				return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), textResponse.Id
			}
			respBodyChanged = true
		}
	}
	var fakeCompletionId string
	// 开启了伪装则先生成id
	if originalModelFakeRespEnabled {
		fakeCompletionId = helper.GenerateFakeCompletionIdProMax(meta.OriginModelName)
	} else if strings.Contains(textResponse.Id, "msg_vrtx_") {
		// 切分 保留msg_vrtx_后面的部分
		config.TempClaudeChatCompletionID = "msg_" + strings.Split(textResponse.Id, "msg_vrtx_")[1]
	} else if strings.Contains(textResponse.Id, "msg_") {
		// 切分 保留msg_后面的部分
		config.TempClaudeChatCompletionID = "msg_" + strings.Split(textResponse.Id, "msg_")[1]
	} else if strings.HasPrefix(textResponse.Id, "chatcmpl-") {
		config.TempOpenAIChatCompletionID = textResponse.Id
	}

	// 有广告语,则插入
	if hasTokenAd == 1 {
		ConstructAdvertisement(&textResponse, advertisement, adPosition)
		// 将含有广告的textResponse转化为responseBody
		responseBody, err = json.Marshal(textResponse)
		if err != nil {
			return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), textResponse.Id
		}
		respBodyChanged = true
	}
	if originalModelFakeRespEnabled {
		if strings.Contains(meta.OriginModelName, "embedding") {
			embeddingResponse.Model = meta.OriginModelName
			responseBody, err = json.Marshal(embeddingResponse)
		} else if meta.IsImageModeNeedReturnUsage() {
		} else {
			textResponse.Model = meta.OriginModelName
			if fakeCompletionIdEnabled {
				textResponse.Id = fakeCompletionId
			}
			textResponse.Object = "chat.completion"
			textResponse.Created = helper.GetTimestamp()
			responseBody, err = json.Marshal(textResponse)
		}
		if err != nil {
			return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), fakeCompletionId
		}
		respBodyChanged = true
	}
	// 判断是否需要负优化
	if negativeOptimizationEnabled {
		if negativeRandomOffset > 0 && negativeOptimizationTime > negativeRandomOffset {
			// 生成一个 -rangeInt 到 rangeInt 之间的随机数
			randomSeconds := rand.Int63n(2*negativeRandomOffset+1) - negativeRandomOffset
			negativeOptimizationTime = negativeOptimizationTime + randomSeconds
		}
		time.Sleep(time.Duration(negativeOptimizationTime) * time.Millisecond)
	}
	// 判断是否启用模拟OpenAI官方响应格式
	if meta.GetMockOpenAICompleteFormatEnabled() && !strings.Contains(requestModel, "embedding") {
		// 检查是否需要使用映射的模型名称
		if mappedModel := openai.GetMappedModel(textResponse.Model); mappedModel != textResponse.Model {
			textResponse.Model = mappedModel
			// 重新序列化响应
			responseBody, err = json.Marshal(textResponse)
			if err != nil {
				return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), textResponse.Id
			}
			respBodyChanged = true
		}
	}
	// 判断是否需要usage重算,embedding模型不需要重新计算 不走这个逻辑
	if !strings.Contains(requestModel, "embedding") && !meta.IsImageModeNeedReturnUsage() {
		if meta.UsageRecalculationEnabled ||
			textResponse.Usage.PromptTokens+textResponse.Usage.CompletionTokens+textResponse.Usage.InputTokens+textResponse.Usage.OutputTokens == 0 {
			textResponse.Usage.CompletionTokens = lo.If(outModel.GetNewTikTokenBilling(meta.UserId), CountTokenTextNew(textResponse.GetFirstChoiceMessage(), requestModel)).Else(CountTokenText(textResponse.GetFirstChoiceMessage(), requestModel))
			textResponse.Usage.PromptTokens = meta.PromptTokens
			textResponse.Usage.TotalTokens = textResponse.Usage.PromptTokens + textResponse.Usage.CompletionTokens
			// 补全PromptTokensDetails中的CachedTokens,CachedCreationTokens,AudioTokens,以及CompletionTokensDetails中的ReasoningTokens,AudioTokens,CachedTokens,AcceptedPredictionTokens,RejectedPredictionTokens
			zero := 0
			textResponse.Usage.PromptTokensDetails = model.TokenDetails{
				CachedTokens:         &zero, // 缓存的token数
				CachedCreationTokens: &zero, // 缓存创建的token数
				AudioTokens:          &zero, // 音频token数
			}
			textResponse.Usage.CompletionTokensDetails = model.TokenDetails{
				ReasoningTokens:          &zero, // 推理token数
				AudioTokens:              &zero, // 音频token数
				AcceptedPredictionTokens: &zero, // 接受的预测token数
				RejectedPredictionTokens: &zero, // 拒绝的预测token数
			}
			responseBody, err = json.Marshal(textResponse)
			if err != nil {
				return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), textResponse.Id
			}
			respBodyChanged = true
		}
	}
	// 判断是否需要ExcludeCustomPromptCostEnabled或者RequestTruncationEnabled
	if (meta.ExcludeCustomPromptCostEnabled || config.RequestTruncationEnabled) && !meta.IsImageModeNeedReturnUsage() &&
		!strings.Contains(meta.OriginModelName, "embedding") {
		textResponse.Usage.PromptTokens = meta.PromptTokens
		textResponse.Usage.TotalTokens = textResponse.Usage.PromptTokens + textResponse.Usage.CompletionTokens
		responseBody, err = json.Marshal(textResponse)
		if err != nil {
			return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), textResponse.Id
		}
		respBodyChanged = true
	}
	// 查找当前用户分组下的所有模型映射
	if meta.IsAdminPass {
		firstMsgForReset := textResponse.GetFirstChoiceMessage()
		modelMapping, modelMappingErr := outModel.GetChannelModelMappingsBySudoGroup(meta.Group)
		if modelMappingErr != nil {
		}
		mappingStr := ""
		for _, mapping := range modelMapping {
			mappingStr += fmt.Sprintf("%v", mapping.GetModelMapping())
		}
		textResponse.SetFirstChoiceMessage(firstMsgForReset + mappingStr)
		responseBody, _ = json.Marshal(textResponse)
		textResponse.SetFirstChoiceMessage(firstMsgForReset)
		respBodyChanged = true
	}

	// 判断是否claude格式返回
	var anthropicResponse *anthropic.Response
	if claudeStreamEnabled {
		anthropicResponse = ResponseOpenAI2Claude(&textResponse)
	}

	// 判断excludedResponseFields
	if excludedResponseFields != "" {
		// 将textResponse转为map[string]interface{}
		responseMap := make(map[string]interface{})
		responseBytes, _ := json.Marshal(textResponse)
		json.Unmarshal(responseBytes, &responseMap)

		// 解析excludedResponseFields为数组
		excludedFields := strings.Split(excludedResponseFields, ",")
		// 遍历excludedFields,删除responseMap中对应的字段
		excludeFieldsFromJSON(responseMap, excludedFields)

		// 将responseMap转为json
		responseBody, err = json.Marshal(responseMap)
		if err != nil {
			return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), textResponse.Id
		}
		respBodyChanged = true
	}

	// 如果think标签处理改变了响应，需要重新序列化
	if thinkTagProcessingEnabled && respBodyChanged {
		responseBody, err = json.Marshal(textResponse)
		if err != nil {
			return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), textResponse.Id
		}
	}
	// 处理响应中的base64图片转换
	if meta.Config.GetConvertBase64ToUrlEnabled() && !meta.IsStream {
		// 获取首选择消息内容
		firstChoiceMessage := textResponse.GetFirstChoiceMessage()
		if firstChoiceMessage != "" {
			// 处理base64图片
			processedMessage, processErr := common.ProcessMarkdownBase64Images(
				firstChoiceMessage,
				meta.UserId,
				meta.Config.GetImageServerUrl(),
			)
			if processErr != nil {
				logger.SysError(fmt.Sprintf("Failed to process base64 images: %v", processErr))
			} else if processedMessage != firstChoiceMessage {
				// 如果有改变，更新响应
				textResponse.SetFirstChoiceMessage(processedMessage)
				responseBody, err = json.Marshal(textResponse)
				if err != nil {
					return ErrorWrapper(err, "marshal_response_body_failed", http.StatusInternalServerError), &textResponse.Usage, textResponse.GetFirstChoiceMessage(), textResponse.Id
				}
				respBodyChanged = true
			}
		}
	}
	// Reset response body
	resp.Body = io.NopCloser(bytes.NewBuffer(responseBody))
	// We shouldn't set the header before we parse the response body, because the parse part may fail.
	// And then we will have to send an error response, but in this case, the header has already been set.
	// So the HTTPClient will be confused by the response.
	// For example, Postman will report error, and we cannot check the response at all.
	for k, v := range resp.Header {
		// 排除这个请求头X-Served-By 避免暴露上游域名
		if strings.ToLower(k) == "x-served-by" {
			continue
		}
		c.Writer.Header().Set(k, v[0])
	}
	if advertisement != "" || haveNoStreamAdvertisement || originalModelFakeRespEnabled || fakeCompletionIdEnabled ||
		meta.IsAdminPass || usageRecalculationEnabled || meta.ExcludeCustomPromptCostEnabled || config.RequestTruncationEnabled ||
		meta.GetMockOpenAICompleteFormatEnabled() || excludedResponseFields != "" || respBodyChanged {
		// 确保 responseBody 不为空，并使用实际长度
		if len(responseBody) > 0 {
			c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", len(responseBody)))
		} else {
			// 如果 responseBody 为空，删除 Content-Length 头
			c.Writer.Header().Del("Content-Length")
		}
	}
	if claudeStreamEnabled {
		anthropicResponseBytes, err := json.Marshal(anthropicResponse)
		if err == nil && len(anthropicResponseBytes) > 0 {
			c.Writer.Header().Set("Content-Length", fmt.Sprintf("%d", len(anthropicResponseBytes)))
		} else {
			c.Writer.Header().Del("Content-Length")
		}
	}
	c.Writer.WriteHeader(resp.StatusCode)
	// 计算非流式的响应的Usage提到前面来,避免err后没有计算
	if textResponse.Usage.TotalTokens == 0 {
		completionTokens := 0
		for _, choice := range textResponse.Choices {
			// 计算 token 时需要包含 reasoning_content
			fullContent := ""
			if choice.Message.ReasoningContent != "" {
				fullContent += choice.Message.ReasoningContent
			}
			fullContent += choice.Message.StringContent()
			completionTokens += lo.If(outModel.GetNewTikTokenBilling(meta.UserId),
				CountTokenTextNew(fullContent, requestModel)).
				Else(CountTokenText(fullContent, requestModel))
		}
		textResponse.Usage = model.Usage{
			PromptTokens:     promptTokens,
			CompletionTokens: completionTokens,
			TotalTokens:      promptTokens + completionTokens,
		}
	}
	// 先判断是否开启了空返报错
	if meta.EmptyResponseErrorEnabled && !strings.Contains(meta.OriginModelName, "embedding") && !meta.IsImageModeNeedReturnUsage() {
		completionTokens := 0
		if meta.InputHasFunctionCall {
			completionTokens = textResponse.Usage.CompletionTokens
		} else {
			for _, choice := range textResponse.Choices {
				completionTokens += lo.If(outModel.GetNewTikTokenBilling(meta.UserId), CountTokenTextNew(choice.Message.StringContent(), requestModel)).Else(CountTokenText(choice.Message.StringContent(), requestModel))
			}
		}
		if completionTokens == 0 {
			return ErrorWrapper(errors.New("the response is empty"), "empty_response", http.StatusInternalServerError), nil, textResponse.GetFirstChoiceMessage(), textResponse.Id
		}
	}
	if claudeStreamEnabled {
		c.JSON(http.StatusOK, anthropicResponse)
	} else {
		// 如果强制取消请求,这里可能会报错http: wrote more than the declared Content-Length,这时候需要判断
		_, err = io.Copy(c.Writer, resp.Body)
	}
	meta.ResponseFirstByteTime_ = helper.GetTimestamp()

	// 通知首字节已收到（用于首字节超时检查）
	if wrapper, exists := c.Get("timeout_wrapper"); exists {
		if timeoutWrapper, ok := wrapper.(*middleware.TimeoutWrapper); ok {
			timeoutWrapper.OnFirstByteReceived()
		}
	}

	if err != nil && !strings.Contains(err.Error(), "wrote more than the declared Content-Length") {
		return ErrorWrapper(err, "copy_response_body_failed", http.StatusInternalServerError), &textResponse.Usage, textResponse.GetFirstChoiceMessage(), textResponse.Id
	}
	err = resp.Body.Close()
	if err != nil {
		return ErrorWrapper(err, "close_response_body_failed", http.StatusInternalServerError), &textResponse.Usage, textResponse.GetFirstChoiceMessage(), textResponse.Id
	}

	if outModel.ShouldLogFullResponse(meta.UserId) {
		// 在所有处理完成后，保存最终要返回给客户端的响应（考虑用户级别配置）
		if claudeStreamEnabled {
			// 如果是 Claude 格式，保存转换后的响应
			anthropicResponseBytes, _ := json.Marshal(anthropicResponse)
			meta.FullResponse = string(anthropicResponseBytes)
		} else {
			// 普通格式直接保存处理后的 responseBody
			meta.FullResponse = string(responseBody)
		}
	}

	if c.GetBool(ctxkey.IsV1MessagesPath) {
		// 解析 Claude 格式响应
		var claudeResp ClaudeResponse
		err = json.Unmarshal(responseBody, &claudeResp)
		if err != nil {
			return ErrorWrapper(err, "unmarshal_response_body_failed", http.StatusInternalServerError), nil, string(responseBody), ""
		}

		// 提取文本内容
		var textContent string
		for _, content := range claudeResp.Content {
			if content.Type == "text" {
				textContent += content.Text
			}
		}

		// 计算 usage
		usage := model.Usage{
			PromptTokens:             claudeResp.Usage.InputTokens + claudeResp.Usage.CacheCreationInputTokens + claudeResp.Usage.CacheReadInputTokens,
			CompletionTokens:         claudeResp.Usage.OutputTokens,
			TotalTokens:              claudeResp.Usage.InputTokens + claudeResp.Usage.CacheCreationInputTokens + claudeResp.Usage.CacheReadInputTokens + claudeResp.Usage.OutputTokens,
			InputTokens:              claudeResp.Usage.InputTokens,
			OutputTokens:             claudeResp.Usage.OutputTokens,
			CacheCreationInputTokens: claudeResp.Usage.CacheCreationInputTokens,
			CacheReadInputTokens:     claudeResp.Usage.CacheReadInputTokens,
			ServiceTier:              claudeResp.Usage.ServiceTier,
		}

		return nil, &usage, textContent, claudeResp.Id
	}

	// 如果 usage 为空且模型名包含 Claude，尝试解析为 Claude 格式
	if (textResponse.Usage.PromptTokens == 0 || textResponse.Usage.CompletionTokens == 0) &&
		strings.Contains(strings.ToLower(requestModel), "claude") {
		var claudeResp ClaudeResponse
		if err := json.Unmarshal(responseBody, &claudeResp); err == nil {
			textResponse.Usage.PromptTokens = claudeResp.Usage.InputTokens + claudeResp.Usage.CacheCreationInputTokens + claudeResp.Usage.CacheReadInputTokens
			textResponse.Usage.CompletionTokens = claudeResp.Usage.OutputTokens
			textResponse.Usage.TotalTokens = textResponse.Usage.PromptTokens + textResponse.Usage.CompletionTokens
			textResponse.Usage.InputTokens = claudeResp.Usage.InputTokens
			textResponse.Usage.OutputTokens = claudeResp.Usage.OutputTokens
			textResponse.Usage.CacheCreationInputTokens = claudeResp.Usage.CacheCreationInputTokens
			textResponse.Usage.CacheReadInputTokens = claudeResp.Usage.CacheReadInputTokens
			textResponse.Usage.ServiceTier = claudeResp.Usage.ServiceTier
		}
	}

	return nil, lo.If(strings.Contains(meta.OriginModelName, "embedding"), &embeddingResponse.Usage).
		ElseIf(meta.IsImageModeNeedReturnUsage(), &imageResponse.Usage).Else(&textResponse.Usage), textResponse.GetFirstChoiceMessage(), textResponse.Id
}

func handleNegativeOptimization(enabled bool, optimizationTime int64, randomOffset int64) {
	if enabled {
		if randomOffset > 0 && optimizationTime > randomOffset {
			randomSeconds := rand.Int63n(2*randomOffset+1) - randomOffset
			optimizationTime = optimizationTime + randomSeconds
		}
		time.Sleep(time.Duration(optimizationTime) * time.Millisecond)
	}
}

func checkSensitiveWords(data string, sensitiveWordsForCheck string, meta *meta.Meta) (bool, string, string) {
	var streamResponses []ChatCompletionsStreamResponse
	trimData := strings.TrimPrefix(data, "data: ")
	streamResp := "[" + trimData + "]"
	_ = json.Unmarshal(helper.StringToByteSlice(streamResp), &streamResponses)
	for _, streamResponse := range streamResponses {
		for _, choice := range streamResponse.Choices {
			sensitiveWordsForCheck += choice.Delta.StringContent()
		}
	}
	isSens, word, tips := outModel.CheckSensitiveWord(sensitiveWordsForCheck)
	if isSens && len(streamResponses) > 0 {
		// 记录敏感词命中
		err := outModel.RecordSensitiveWordHit(meta.DeepCopyToLogMeta(), word, meta.DetailPrompt, streamResponses[0].GetFirstChoiceDeltaContent())
		if err != nil {
			logger.SysError("record sensitive word hit failed: " + err.Error())
		}
		streamResponses[0].Choices[0].Delta.Content = tips
		dataBytes, err := json.Marshal(streamResponses[0])
		if err != nil {
			return isSens, tips, data
		}
		return isSens, sensitiveWordsForCheck, string(dataBytes)
	}
	return false, "", data
}

// ResponsesHandler 处理 /v1/responses 非流式响应
func ResponsesHandler(c *gin.Context, resp *http.Response, meta *meta.Meta) (usage *model.Usage, err *model.ErrorWithStatusCode) {
	defer resp.Body.Close()

	// 读取响应体
	responseBody, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		return nil, ErrorWrapper(readErr, "read_response_body_failed", http.StatusInternalServerError)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, ErrorWrapper(fmt.Errorf(string(responseBody)), "upstream_error", resp.StatusCode)
	}

	// 解析响应
	var responsesResponse model.ResponsesResponse
	unmarshalErr := json.Unmarshal(responseBody, &responsesResponse)
	if unmarshalErr != nil {
		return nil, ErrorWrapper(unmarshalErr, "unmarshal_response_failed", http.StatusInternalServerError)
	}

	// 直接转发响应
	c.Data(resp.StatusCode, resp.Header.Get("Content-Type"), responseBody)

	// 提取使用量
	usage = &model.Usage{
		PromptTokens:     responsesResponse.Usage.PromptTokens,
		CompletionTokens: responsesResponse.Usage.CompletionTokens,
		TotalTokens:      responsesResponse.Usage.TotalTokens,
	}

	return usage, nil
}

// ResponsesStreamHandler 处理 /v1/responses 流式响应
func ResponsesStreamHandler(c *gin.Context, resp *http.Response, meta *meta.Meta) (usage *model.Usage, err *model.ErrorWithStatusCode) {
	if resp == nil || resp.Body == nil {
		return nil, ErrorWrapper(fmt.Errorf("invalid response"), "bad_response", http.StatusInternalServerError)
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, ErrorWrapper(fmt.Errorf(string(body)), "upstream_error", resp.StatusCode)
	}

	// 设置流式响应头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")

	var responseUsage *model.Usage
	var responseTextBuilder strings.Builder

	scanner := bufio.NewScanner(resp.Body)
	for scanner.Scan() {
		data := scanner.Text()
		if len(data) < 6 || !strings.HasPrefix(data, "data: ") {
			continue
		}

		dataStr := data[6:]
		if dataStr == "[DONE]" {
			c.Render(-1, common.CustomEvent{Data: data})
			break
		}

		// 解析事件数据
		var streamResponse map[string]interface{}
		if json.Unmarshal([]byte(dataStr), &streamResponse) == nil {
			// 检查是否是 response.completed 事件
			if eventType, ok := streamResponse["type"].(string); ok && eventType == "response.completed" {
				if response, ok := streamResponse["response"].(map[string]interface{}); ok {
					if usageData, ok := response["usage"].(map[string]interface{}); ok {
						inputTokens := 0
						if it, ok := usageData["input_tokens"].(float64); ok {
							inputTokens = int(it)
						}
						outputTokens := 0
						if ot, ok := usageData["output_tokens"].(float64); ok {
							outputTokens = int(ot)
						}
						totalTokens := 0
						if tt, ok := usageData["total_tokens"].(float64); ok {
							totalTokens = int(tt)
						} else {
							totalTokens = inputTokens + outputTokens
						}

						responseUsage = &model.Usage{
							PromptTokens:     inputTokens,
							CompletionTokens: outputTokens,
							TotalTokens:      totalTokens,
						}
					}
				}
			}

			// 提取文本内容用于token计算
			if eventType, ok := streamResponse["type"].(string); ok {
				switch eventType {
				case "response.content_part.added", "response.content_part.done":
					if part, ok := streamResponse["part"].(map[string]interface{}); ok {
						if text, ok := part["text"].(string); ok {
							responseTextBuilder.WriteString(text)
						}
					}
				case "response.output_item.added", "response.output_item.done":
					if item, ok := streamResponse["item"].(map[string]interface{}); ok {
						if content, ok := item["content"].([]interface{}); ok {
							for _, contentItem := range content {
								if contentMap, ok := contentItem.(map[string]interface{}); ok {
									if text, ok := contentMap["text"].(string); ok {
										responseTextBuilder.WriteString(text)
									}
								}
							}
						}
					}
				}
			}
		}

		// 转发事件
		c.Render(-1, common.CustomEvent{Data: data})
	}

	// 如果没有获取到使用量，尝试从响应文本计算
	if responseUsage == nil || responseUsage.CompletionTokens == 0 {
		responseText := responseTextBuilder.String()
		if len(responseText) > 0 {
			completionTokens := CountTokenText(responseText, meta.ActualModelName)
			if responseUsage == nil {
				responseUsage = &model.Usage{
					PromptTokens:     meta.PromptTokens,
					CompletionTokens: completionTokens,
					TotalTokens:      meta.PromptTokens + completionTokens,
				}
			} else {
				responseUsage.CompletionTokens = completionTokens
				if responseUsage.TotalTokens == 0 {
					responseUsage.TotalTokens = responseUsage.PromptTokens + completionTokens
				}
			}
		}
	}

	return responseUsage, nil
}

func handleAdInsertion(c *gin.Context, hasTokenAd int, adPosition int, adInserted *int32, advertisement string) {
	if hasTokenAd == 1 && adPosition == 3 && atomic.LoadInt32(adInserted) == 0 {
		currentPosition := 5
		currentPosition++
		randNumber := rand.Intn(currentPosition + 1)
		if randNumber == 0 {
			atomic.StoreInt32(adInserted, 1)
			ConstructStreamAdvertisement(c, advertisement)
		}
	}
}

func updateFunctionCallBuilders(nameBuilder *strings.Builder, argsBuilder *strings.Builder, delta model.Message) {
	if delta.FunctionCall != nil {
		if delta.FunctionCall.Name != "" {
			nameBuilder.WriteString(delta.FunctionCall.Name)
		}
		if delta.FunctionCall.Arguments != "" {
			argsBuilder.WriteString(model.GetArgumentsString(delta.FunctionCall.Arguments))
		}
	}

	if delta.ToolCalls != nil && len(delta.ToolCalls) > 0 {
		for _, toolCall := range delta.ToolCalls {
			if toolCall.Function.Name != "" {
				nameBuilder.WriteString(toolCall.Function.Name)
			}
			if toolCall.Function.Arguments != nil {
				argsBuilder.WriteString(model.GetArgumentsString(toolCall.Function.Arguments))
			}
		}
	}
}

func generateFakeCompletionId() string {
	if config.TempOpenAIChatCompletionID == "" || !strings.HasPrefix(config.TempOpenAIChatCompletionID, "chatcmpl-") {
		return fmt.Sprintf("chatcmpl-%s", random.GetRandomString(29))
	} else {
		if len(config.TempOpenAIChatCompletionID) < 13 {
			return fmt.Sprintf("chatcmpl-%s", random.GetRandomString(29))
		} else {
			return fmt.Sprintf("%s%s", config.TempOpenAIChatCompletionID[:13], random.GetRandomString(25))
		}
	}
}
func ResponseOpenAI2Claude(openaiResponse *SlimTextResponse) *anthropic.Response {
	var claudeContent []anthropic.Content

	if openaiResponse.Choices[0].Message.Content != "" {
		claudeContent = append(claudeContent, anthropic.Content{
			Type: "text",
			Text: openaiResponse.GetFirstChoiceMessage(),
		})
	}

	for _, tool := range openaiResponse.Choices[0].Message.ToolCalls {
		claudeContent = append(claudeContent, anthropic.Content{
			Type:  "tool_use",
			Id:    tool.Id,
			Name:  tool.Function.Name,
			Input: json.RawMessage([]byte(model.GetArgumentsString(tool.Function.Arguments))),
		})
	}

	var stopReason *string
	if openaiResponse.Choices[0].FinishReason != "" {
		reason := stopReasonOpenAI2Claude(openaiResponse.Choices[0].FinishReason)
		stopReason = &reason
	}

	claudeResponse := &anthropic.Response{
		Id:         strings.TrimPrefix(openaiResponse.Id, "chatcmpl-"),
		Type:       "message",
		Role:       "assistant",
		Content:    claudeContent,
		Model:      openaiResponse.Model,
		StopReason: stopReason,
		Usage: anthropic.Usage{
			InputTokens:  openaiResponse.Usage.PromptTokens,
			OutputTokens: openaiResponse.Usage.CompletionTokens,
		},
	}

	return claudeResponse
}

func stopReasonOpenAI2Claude(openaiReason string) string {
	switch openaiReason {
	case "stop":
		return "end_turn"
	case "length":
		return "max_tokens"
	case "content_filter":
		return "content_filtered"
	case "tool_calls":
		return "tool_calls"
	default:
		return openaiReason
	}
}

func excludeFieldsFromJSON(data interface{}, excludeFields []string) {
	switch v := data.(type) {
	case map[string]interface{}:
		for key, value := range v {
			if contains(excludeFields, key) {
				delete(v, key)
			} else {
				excludeFieldsFromJSON(value, excludeFields)
			}
		}
	case []interface{}:
		for _, item := range v {
			excludeFieldsFromJSON(item, excludeFields)
		}
	}
}

func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// processThinkTags 处理内容中的<think>标签
// 返回值：extractedThinking(提取的思考内容), cleanedContent(清理后的普通内容), hasThinking(是否包含思考内容)
func processThinkTags(content string) (string, string, bool) {
	// 匹配<think>...</think>标签的正则表达式
	re := regexp.MustCompile(`<think>([\s\S]*?)</think>`)
	matches := re.FindAllStringSubmatch(content, -1)

	if len(matches) == 0 {
		return "", content, false
	}

	var thinkingParts []string
	for _, match := range matches {
		if len(match) > 1 {
			// 对每个thinking部分进行trim处理，去除前后空白字符
			trimmedThinking := strings.TrimSpace(match[1])
			if trimmedThinking != "" {
				thinkingParts = append(thinkingParts, trimmedThinking)
			}
		}
	}

	// 提取思考内容，用换行符连接多个部分，然后再次trim
	extractedThinking := strings.TrimSpace(strings.Join(thinkingParts, "\n"))

	// 从原内容中移除<think>标签及其内容
	cleanedContent := re.ReplaceAllString(content, "")

	// 清理多余的空白字符
	cleanedContent = strings.TrimSpace(cleanedContent)

	return extractedThinking, cleanedContent, true
}

// processStreamThinkTags 处理流式响应中的<think>标签
// 用于跟踪流式响应中的<think>标签状态
type ThinkTagState struct {
	InThinkTag       bool
	ThinkBuffer      strings.Builder
	ContentBuffer    strings.Builder
	ThinkTagComplete bool
}

// processStreamThinkFragment 处理流式响应中的单个片段
func processStreamThinkFragment(fragment string, state *ThinkTagState) (reasoning string, content string, hasUpdate bool) {
	if fragment == "" {
		return "", "", false
	}

	var reasoningContent string
	var normalContent string

	// 检查是否包含<think>开始标签
	if strings.Contains(fragment, "<think>") && !state.InThinkTag {
		parts := strings.Split(fragment, "<think>")
		if len(parts) > 1 {
			// 第一部分是普通内容
			normalContent = parts[0]

			// 第二部分开始是思考内容
			state.InThinkTag = true
			thinkContent := parts[1]

			// 检查这个片段中是否也包含结束标签
			if strings.Contains(thinkContent, "</think>") {
				endParts := strings.Split(thinkContent, "</think>")
				if len(endParts) > 1 {
					// 思考部分
					reasoningContent = strings.TrimSpace(endParts[0])
					state.ThinkTagComplete = true
					state.InThinkTag = false

					// 结束标签后的普通内容
					normalContent += endParts[1]
				}
			} else {
				// 只有开始标签，没有结束标签
				reasoningContent = thinkContent
			}
		} else {
			normalContent = fragment
		}
	} else if strings.Contains(fragment, "</think>") && state.InThinkTag {
		// 包含结束标签
		parts := strings.Split(fragment, "</think>")
		if len(parts) > 1 {
			// 第一部分是思考内容，进行trim处理
			reasoningContent = strings.TrimSpace(parts[0])
			state.ThinkTagComplete = true
			state.InThinkTag = false

			// 第二部分是普通内容
			normalContent = parts[1]
		}
	} else if state.InThinkTag {
		// 在思考标签内
		reasoningContent = fragment
	} else {
		// 普通内容
		normalContent = fragment
	}

	return reasoningContent, normalContent, true
}

// isAudioModel checks if the model supports audio features
func isAudioModel(modelName string) bool {
	audioModels := []string{
		"gpt-4o-audio-preview",
		"gpt-4o-mini-audio-preview",
		"gpt-4o-realtime-preview",
	}

	for _, audioModel := range audioModels {
		if strings.Contains(modelName, audioModel) {
			return true
		}
	}
	return false
}

// processStreamBase64Images 处理流式响应chunk中的base64图片
func processStreamBase64Images(data string, meta *meta.Meta) string {
	// 只处理包含markdown base64图片的数据
	if !strings.Contains(data, "data:image") {
		return data
	}

	// 直接对整行数据进行处理
	processedData, processErr := common.ProcessMarkdownBase64Images(
		data,
		meta.UserId,
		meta.Config.GetImageServerUrl(),
	)
	if processErr != nil {
		logger.SysError(fmt.Sprintf("Failed to process base64 images in stream: %v", processErr))
		return data
	}

	return processedData
}

// calculateAudioTokens calculates audio tokens for audio-enabled models
// isAudioModel checks if the model supports audio features
