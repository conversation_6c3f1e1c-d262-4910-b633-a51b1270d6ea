import {API} from '../helpers';

export async function getOAuthState() {
    const res = await API.get('/api/oauth/state');
    const {success, message, data} = res.data;
    if (success) {
        return data;
    } else {
        console.error(message);
        return '';
    }
}

export async function onGitHubOAuthClicked(github_client_id: string) {
    const state = await getOAuthState();
    if (!state) return;
    window.location.href = `https://github.com/login/oauth/authorize?client_id=${github_client_id}&state=${state}&scope=user:email`;
}

export async function onGoogleOAuthClicked(google_client_id: string) {
    const state = await getOAuthState();
    if (!state) return;
    const redirectUri = `${window.location.origin}/oauth/google`;
    const scope = 'openid email profile';
    window.location.href = `https://accounts.google.com/o/oauth2/v2/auth?client_id=${google_client_id}&redirect_uri=${redirectUri}&scope=${scope}&response_type=code&state=${state}`;
}

//FIXME：返回值非法，用户字段为空，请稍后重试！

//https://api.qwqai.com/api/oauth/github?code=24bb9d29fe723b8488dc&state=3Bv2ooJYD7aC
//{"message":"返回值非法，用户字段为空，请稍后重试！","success":false}

