// MarkdownOrOpen:传入类型，优先从后端获取，失败则使用前端默认组件
import {API, showError} from "../helpers";
import {DefaultResponse} from "../constants";
import React, {useEffect, useState} from "react";
import {marked} from "marked";
import {ProCard} from "@ant-design/pro-components";
import PrivacyPolicy from "../pages/Legal/PrivacyPolicy";
import TermsOfService from "../pages/Legal/TermsOfService";
import Loading from "./Loading";

const MarkdownOrOpen = ({type}: { type: "serviceAgreement" | "privacyPolicy" }) => {
    const [data, setData] = useState('')
    const [loading, setLoading] = useState(true)
    const [useDefault, setUseDefault] = useState(false)

    const fetchData = async () => {
        try {
            const res = await API.get('api/document')
            const {success, data} = res.data as DefaultResponse;
            if (success) {
                let content = '';
                switch (type) {
                    case "serviceAgreement":
                        content = data.ServiceAgreement || '';
                        break;
                    case "privacyPolicy":
                        content = data.PrivacyPolicy || '';
                        break;
                }

                // 如果后端有内容且不为空，使用后端内容
                if (content && content.trim() !== '') {
                    setData(content);
                    setUseDefault(false);
                } else {
                    // 后端没有内容，使用前端默认组件
                    setUseDefault(true);
                }
            } else {
                // API 调用失败，使用前端默认组件
                setUseDefault(true);
            }
        } catch (e) {
            // 网络错误或其他异常，使用前端默认组件
            console.warn('Failed to fetch document from backend, using default component:', e);
            setUseDefault(true);
        } finally {
            setLoading(false);
        }
    }

    useEffect(() => {
        fetchData().then();
    }, [type]);

    // 显示加载状态
    if (loading) {
        return <Loading />;
    }

    // 使用前端默认组件
    if (useDefault) {
        switch (type) {
            case "serviceAgreement":
                return <TermsOfService />;
            case "privacyPolicy":
                return <PrivacyPolicy />;
            default:
                return <div>未知页面类型</div>;
        }
    }

    // 使用后端数据
    if (data.startsWith('http')) {
        window.open(data);
        return <p>跳转中...</p>
    } else {
        (async () => {
            const markedData = await marked(data);
            setData(markedData);
        })();

        return <ProCard style={{height: '81vh'}}>
            <div style={{fontSize: 'larger'}} dangerouslySetInnerHTML={{__html: data}}></div>
        </ProCard>
    }
}

export default MarkdownOrOpen;