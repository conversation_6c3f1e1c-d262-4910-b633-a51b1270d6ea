import i18n from "i18next";
import {initReactI18next} from "react-i18next";
import translationEN from './translations/en.json';
import translationZHCN from './translations/zh-CN.json';
import translationZHTW from './translations/zh-TW.json';
import translationES from './translations/es.json';
import translationFR from './translations/fr.json';
import translationDE from './translations/de.json';
import translationJA from './translations/jp.json';
import translationKO from './translations/kr.json';
import translationRU from './translations/ru.json';
import translationIT from './translations/it.json';
import translationPT from './translations/pt.json';
import translationAR from './translations/ar.json';
import translationVI from './translations/vi.json';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

// the translations
const resources = {
    en: {
        translation: translationEN
    },
    "zh-CN": {
        translation: translationZHCN
    },
    "zh-TW": {
        translation: translationZHTW
    },
    es: {
        translation: translationES
    },
    fr: {
        translation: translationFR
    },
    de: {
        translation: translationDE
    },
    jp: {
        translation: translationJA
    },
    kr: {
        translation: translationKO
    },
    ru: {
        translation: translationRU
    },
    it: {
        translation: translationIT
    },
    pt: {
        translation: translationPT
    },
    ar: {
        translation: translationAR
    },
    vi: {
        translation: translationVI
    },
};

i18n.use(Backend) // 加载翻译文件
    .use(LanguageDetector) // 检测用户语言
    .use(initReactI18next) // 通过react-i18next绑定
    .init({
        resources, // 初始化资源
        // 其他配置...
        // backend: {
        //   // 指定翻译文件的加载路径
        //   loadPath: './{{ns}}/{{lng}}.json',
        // },
        fallbackLng: 'en', // 如果当前语言没有翻译，则回退到英语
        debug: false, // 开启调试模式，查看加载和错误信息
        interpolation: {
            escapeValue: false, // 不需要对xss进行转义
        },
    }).then();
export default i18n;

