{"message": {"copyModelSuccess": "Le nom du modèle a été copié dans le presse-papiers !", "copyFailed": "Échec de la copie, veuillez copier manuellement.", "logoutSuccess": "Déconnexion réussie.", "loginSuccess": {"default": "Connexion réussie", "welcomeBack": "Bienvenue de retour"}, "removeLocalStorage": {"confirm": "Voulez-vous vider le cache local ?", "success": "Effacement du cache local réussi."}, "loadData": {"error": "Échec du chargement des données {{name}}."}, "noNotice": "Aucun contenu d'annonce pour le moment.", "verification": {"turnstileChecking": "Turnstile vérifie l'environnement utilisateur !", "pleaseWait": "Veuillez réessayer plus tard."}, "clipboard": {"inviteCodeDetected": "Le code d'invitation a été détecté et a été automatiquement rempli !", "clickToCopy": "Cliquez pour copier", "copySuccess": "<PERSON><PERSON> r<PERSON>"}}, "common": {"yes": "oui", "no": "Non", "copyAll": "<PERSON><PERSON><PERSON> tout", "all": "<PERSON>ut", "more": "plus", "unlimited": "sans restriction", "enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled": "fermer", "save": "sauve<PERSON><PERSON>", "cancel": "Annuler", "create": "<PERSON><PERSON><PERSON>", "usd": "dollar américain", "day": "{{count}} jours", "day_plural": "{{count}} jours", "days": "ciel", "seconds": "seconde", "times": "suivant", "submit": "So<PERSON><PERSON><PERSON>", "bind": "lier", "unknown": "inconnu", "loading": "Chargement en cours...", "copyFailed": "Échec de la copie.", "people": "personne", "ok": "Déterminé", "close": "fermer", "copied": "<PERSON><PERSON><PERSON>", "expand": "Développer", "collapse": "ranger", "none": "aucun", "remark": "<PERSON><PERSON><PERSON>", "selectPlaceholder": "Veuillez choisir {{name}}.", "on": "ouvrir", "off": "fermer", "name": "Identification", "displayName": "Nom d'affichage", "description": "Description", "ratio": "taux de multiplication", "unnamed": "Canal sans nom", "groups": "Groupe", "captchaPlaceholder": "Veuillez entrer le code de vérification.", "confirm": "Confirmer", "permissions": "Autorisations", "actions": "Actions", "createdTime": "Heure de création", "expiredTime": "Heure d'expiration", "search": "<PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "refresh": "Actualiser", "pagination": {"total": "Total {{total}} éléments"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "Cliquez pour ouvrir le lien."}, "userRole": {"normal": "utilisateur ordinaire", "agent": "agent", "admin": "Administrateur", "superAdmin": "Super administrateur", "loading": "Chargement en cours..."}, "channelStatus": {"enabled": "Activer", "disabled": "Désactivé", "waitingRestart": "At<PERSON>re le redémarrage", "waiting": "<PERSON><PERSON><PERSON>", "autoStoppedTitle": "Le réessai automatique du canal a dépassé le nombre maximum de tentatives ou a déclenché les conditions d'arrêt automatique.", "stopped": "Désactiver", "partiallyDisabled": "Partiellement désactivé", "unknown": "inconnu", "reason": "<PERSON>son"}, "channelBillingTypes": {"payAsYouGo": "Facturation à la consommation", "payPerRequest": "Facturation à l'acte", "unknown": "méthode inconnue"}, "tokenStatus": {"normal": "normal", "disabled": "Désactivé", "expired": "expiré", "exhausted": "é<PERSON><PERSON>é", "unknown": "inconnu"}, "userStatus": {"normal": "normal", "banned": "interdiction", "unknown": "inconnu"}, "redemptionStatus": {"normal": "normal", "disabled": "Désactiver", "redeemed": "<PERSON><PERSON><PERSON>", "expired": "expiré", "unknown": "inconnu"}, "duration": {"request": "demande", "firstByte": "premier octet", "total": "Total", "seconds": "seconde", "lessThanOneSecond": "<1 seconde"}, "streamType": {"stream": "flux", "nonStream": "Non-flux."}, "noSet": {"title": "L'administrateur n'a pas configuré {{name}}.", "name": {"about": "À propos de", "chat": "Dialogue"}}, "buttonText": {"add": "Nouveau", "cancel": "Annuler", "confirm": "Confirmer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON>", "save": "sauve<PERSON><PERSON>", "updateBalance": "Mettre à jour le solde", "test": "test", "multiple": "Choix multiple"}, "channelPage": {"title": "Gestion des canaux"}, "channelStatusCount": {"title": "Statistiques sur l'état des canaux", "summary": "Activé {{enabled}} | <PERSON><PERSON><PERSON><PERSON><PERSON> {{disabled}} | En cours de réessai {{retry}} | Arrê<PERSON> {{stopped}}", "statusEnabled": "Activé", "statusDisabled": "<PERSON><PERSON><PERSON><PERSON>.", "statusRetry": "Nouvelle tentative.", "statusStopped": "Status", "statusPartially": "Partiellement désactivé"}, "header": {"routes": {"status": "État", "home": "Accueil", "chat": "Dialogue", "pptGen": "Génération de PPT", "chart": "statistiques", "agency": "agent", "channel": "canal", "ability": "Capacité de canal", "channelGroup": "Groupe de canaux", "token": "jeton", "log": "journal", "logDetail": "Détails", "midjourney": "dessin", "user": "utilisateur", "config": "Configuration", "packagePlanAdmin": "forfait", "redemption": "code de échange", "group": "Groupe", "query": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "about": "À propos de", "setting": {"default": "Paramètres", "operation": "Paramètres d'exploitation", "system": "Paramètres du système", "global": "Paramètres globaux", "advance": "Paramètres de caractéristiques", "sensitive": "Configuration des mots sensibles", "verification": "Configuration du code de vérification", "update": "Vérifier les mises à jour"}, "account": {"default": "compte", "profile": "Centre personnel", "cardTopup": "Échange de code de carte", "onlineTopup": "Recharge en ligne", "recharge": "Recharge de solde", "balanceTransfer": "Transfert de solde", "pricing": "Description des frais", "packagePlan": {"list": "<PERSON><PERSON><PERSON>", "record": "Historique d'achats"}, "notificationSettings": "Paramètres de notification"}, "tools": {"default": "outil", "fileUpload": "Téléchargement de fichiers", "keyExtraction": "Extraction de clé", "multiplierCalculator": "Calculateur de multiplicateur", "shortLink": "Génération de liens courts", "testConnection": "Test d'accès", "customPrompts": "Gestion des mots-clés", "redis": "Visualisation de Redis", "ratioCompare": "Comparaison des multiplicateurs", "serverLog": "Visualiseur de journaux de serveur"}, "onlineTopupRecord": "Historique des recharges", "channelScores": "Score du canal", "dynamicRouter": "Routage dynamique", "task": "Tâches asynchrones", "agencyJoin": "Partenariat d'agence"}, "dropdownMenu": {"profile": "Centre personnel", "recharge": "Recharge de solde", "agencyCenter": "Centre des agents", "checkin": "Enregistrement", "darkMode": {"enable": "Mode sombre", "disable": "Mode jour"}, "fullScreen": {"default": "Passer en plein écran", "enable": "Mode plein écran", "disable": "<PERSON><PERSON><PERSON> le mode plein écran"}, "logout": "Déconnexion"}, "checkin": {"default": "Enregistrement", "success": "Inscription réussie.", "failed": "Échec de l'enregistrement.", "verification": "Veuillez compléter la vérification."}, "avatarProps": {"login": "Connexion"}}, "settings": {"public": {"titles": {"default": "Paramètres publics"}, "SystemName": "Nom du système", "ServerAddress": "Adresse de service", "TopUpLink": "Lien de recharge", "ChatLink": "Lien de <PERSON>", "Logo": "Logo du système", "HomePageContent": "Contenu de la page d'accueil", "About": "À propos du contenu", "Notice": "Contenu de l'annonce", "Footer": "Contenu du pied de page", "RegisterInfo": "Notification d'inscription", "HeaderScript": "<PERSON><PERSON>t<PERSON><PERSON> person<PERSON>", "SiteDescription": "Description du site", "PrivacyPolicy": "Politique de confidentialité", "ServiceAgreement": "Contrat de service", "FloatButton": {"FloatButtonEnabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DocumentInfo": "Informations sur le document", "WechatInfo": "Message WeChat", "QqInfo": "Informations QQ"}, "CustomThemeConfig": "<PERSON>h<PERSON> person<PERSON>", "AppList": "Liens d'amitié"}}, "home": {"default": {"title": "Bienvenue !", "subtitle": "Développement secondaire basé sur One API, offrant des fonctionnalités plus complètes.", "start": "Commencer à utiliser", "description": {"title": "Nouvelles fonctionnalités :", "part1": "Nouvelle interface utilisateur, pratique et rapide.", "part2": "Optimiser le mécanisme de planification, efficace et stable.", "part3": "Développé pour les entreprises, sûr et fiable.", "part4": "Plus de fonctionnalités avancées vous attendent."}}}, "dailyUsageChart": {"title": "Utilisation quotidienne du modèle", "yAxisName": "Utilisation (USD)", "loadingTip": "Utilisation quotidienne", "fetchError": "Erreur lors de l'obtention des données d'utilisation quotidienne :"}, "modelUsageChart": {"title": "Utilisation du modèle", "hourlyTitle": "Utilisation du modèle par heure", "dailyTitle": "Utilisation quotidienne du modèle", "weeklyTitle": "Utilisation du modèle par semaine", "monthlyTitle": "Utilisation du modèle par mois"}, "granularity": {"hour": "par heure", "day": "<PERSON><PERSON> jour", "week": "chaque semaine", "month": "chaque mois", "all": "<PERSON>ut"}, "abilitiesTable": {"title": "Capacité de canal", "export": "Exporter", "group": "Groupe", "model": "mod<PERSON><PERSON>", "channelId": "Numéro de canal", "enabled": "Activé", "weight": "poids", "priority": "Priorité", "billingType": "Type de facturation", "functionCallEnabled": "Activation de l'appel de fonction", "imageSupported": "Soutenir l'image", "yes": "oui", "no": "Non", "perToken": "Facturation par token", "perRequest": "Facturation à la demande", "noDataToExport": "Aucune donnée à exporter.", "exportConfirm": "Êtes-vous sûr de vouloir exporter les données de la page actuelle ?", "exportSuccess": "Exportation réussie.", "toggleSuccess": "Changement réussi.", "toggleError": "Échec du changement.", "selectOrInputGroup": "Sélectionnez ou saisissez un groupe d'utilisateurs."}, "logsTable": {"retry": "<PERSON><PERSON><PERSON><PERSON>", "retryChannelList": "Liste des canaux de réessai", "retryDurations": "<PERSON>é<PERSON> du temps de réessai", "channel": "canal", "duration": "temps nécessaire", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "retryCount": "Nombre de tentatives", "retryDetails": "Détails de la nouvelle tentative", "totalRetryTime": "Temps total de réessai", "seconds": "seconde", "tokenGroup": "Groupe de jetons", "selectGroup": "Choisir un groupe", "dailyModelUsageStats": "Aperçu des appels de données", "time": "Temps", "moreInfo": "Plus d'informations", "ip": "IP", "remoteIp": "IP distant", "ipTooltip": "IP : {{ip}}  \nIP distant : {{remoteIp}}", "requestId": "ID de la demande", "username": "nom d'utilisateur", "userId": "Identifiant utilisateur", "tokenName": "Nom du jeton", "token": "jeton", "type": "Type", "typeUnknown": "inconnu", "type充值": "recharge", "type消费": "consommation", "type管理": "Gestion", "type系统": "système", "type邀请": "Invitation", "type提示": "indice", "type警告": "Avertissement", "type错误": "<PERSON><PERSON><PERSON>", "type签到": "Enregistrement", "type日志": "journal", "type退款": "Remboursement", "type邀请奖励金划转": "Transfert de la prime d'invitation", "type代理奖励": "Récompense d'agent", "type下游错误": "Erreur en aval", "type测试渠道": "Canal de test", "typeRecharge": "recharge", "typeConsumption": "consommation", "typeManagement": "Gestion", "typeSystem": "système", "typeInvitation": "Invitation", "typePrompt": "<PERSON><PERSON><PERSON>", "typeWarning": "Avertissement", "typeError": "<PERSON><PERSON><PERSON>", "typeCheckin": "Enregistrement", "typeLog": "journal", "typeRefund": "Remboursement", "typeInviteReward": "Transfert de la prime d'invitation", "typeAgencyBonus": "Récompense d'agent", "typeDownstreamError": "Erreur en aval", "typeChannelTest": "Canal de test", "channelId": "ID de canal", "channelName": "Nom du canal", "model": "mod<PERSON><PERSON>", "modelPlaceholder": "Entrer/choisir le nom du modèle", "info": "information", "isStream": "flux", "isStreamPlaceholder": "Entr<PERSON>/Choisir si en flux", "prompt": "indice", "completion": "<PERSON><PERSON><PERSON><PERSON>", "consumption": "consommation", "consumptionRange": "Plage de montant de consommation", "description": "Explication", "action": "Opération", "details": "Détails", "tokenKey": "clé de jeton", "requestDuration": "Temps de demande", "firstByteDuration": "Temps de latence du premier octet", "totalDuration": "Temps total écoulé", "lessThanOneSecond": "<1 seconde", "modelInvocation": "<PERSON><PERSON> mod<PERSON>", "modelUsage": "Utilisation du modèle", "totalQuota": "Montant total de consommation : {{quota}}", "totalRpm": "Nombre de requêtes par minute : {{rpm}}", "totalTpm": "Nombre de tokens par minute : {{tpm}}", "totalMpm": "Montant/minute : {{mpm}}", "dailyEstimate": "Consommation quotidienne estimée : {{estimate}}", "currentStats": "RPM actuel : {{rpm}} TPM actuel : {{tpm}} MPM actuel : ${{mpm}} Estimation de consommation quotidienne : ${{dailyEstimate}}", "statsTooltip": "Seules les journaux non archivés sont comptabilisés. RPM : nombre de requêtes par minute, TPM : nombre de tokens par minute, MPM : argent consommé par minute, la consommation quotidienne estimée est déduite de l'actuel MPM.", "showAll": "<PERSON><PERSON> afficher", "exportConfirm": "Exporter le journal de cette page ?", "export": "Exporter", "statsData": "Données statistiques", "today": "le jour même", "lastHour": "1 heure", "last3Hours": "3 heures", "lastDay": "1 jour", "last3Days": "3 jours", "last7Days": "7 jours", "lastMonth": "1 mois", "last3Months": "3 mois", "excludeModels": "mod<PERSON><PERSON> d'exclusion", "selectModelsToExclude": "Choisissez le modèle à exclure.", "excludeErrorCodes": "Exclure le code d'erreur.", "excludeErrorCodesPlaceholder": "Sélectionnez les codes d'erreur à exclure.", "errorCode": "Code d'erreur", "errorCodePlaceholder": "Entrer/choisir le code d'erreur", "timezoneTip": "<PERSON><PERSON> horaire actuel : {timezone}", "timezoneNote": "Alerte de fuseau horaire", "timezoneDescription": "Les données statistiques sont regroupées par date selon votre fuseau horaire actuel. Les différents fuseaux horaires peuvent entraîner des variations dans les périodes de regroupement des données. Pour ajuster cela, veuillez vous rendre dans votre centre personnel pour modifier les paramètres de fuseau horaire.", "goToProfile": "Aller au centre personnel", "realtimeQuota": "Consommation en temps réel (1 minute)", "viewTotalQuota": "Voir la consommation totale", "viewTotalQuotaTip": "Vérifiez le montant total des dépenses historiques (la recherche peut prendre quelques secondes).", "loadingTotalQuota": "Recherche du montant total des dépenses, veuillez patienter...", "totalQuotaTitle": "Statistiques de consommation historique", "loadTotalQuotaError": "Échec de l'obtention du montant total des dépenses.", "requestLogs": "Journal des requêtes - {{requestId}}", "noRequestLogs": "Aucun journal de requêtes disponible.", "metricsExplanation": "Seules les journaux non archivés sont comptabilisés. RPM : nombre de requêtes par minute, TPM : nombre de tokens par minute, MPM : montant d'argent consommé par minute, la consommation quotidienne estimée est déduite du MPM actuel.", "autoRefresh": "Actualisation automatique", "autoRefreshTip": "Cliquez pour activer/désactiver le rafraîchissement automatique. Une fois activé, les données se rafraîchiront automatiquement toutes les quelques secondes spécifiées.", "autoRefreshOn": "Actualisation automatique activée.", "autoRefreshOff": "Le rafraîchissement automatique a été désactivé.", "refreshInterval": "Intervalle de rafraîchissement", "stopRefresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>.", "secondsWithValue": "{{seconds}} secondes", "minutesWithValue": "{{minutes}} minutes"}, "mjLogs": {"logId": "ID de journal", "submitTime": "Date de soumission", "type": "Type", "channelId": "ID de canal", "userId": "Identifiant utilisateur", "taskId": "ID de la tâche", "submit": "So<PERSON><PERSON><PERSON>", "status": "État", "progress": "progr<PERSON>", "duration": "temps nécessaire", "result": "résultat", "prompt": "Invite", "promptEn": "<PERSON><PERSON><PERSON><PERSON>, je ne peux pas traduire cela.", "failReason": "Raisons de l'échec", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "today": "le jour même", "lastHour": "1 heure", "last3Hours": "3 heures", "lastDay": "1 jour", "last3Days": "3 jours", "last7Days": "7 jours", "lastMonth": "1 mois", "last3Months": "3 mois", "selectTaskType": "Choisissez le type de tâche.", "selectSubmitStatus": "Choisir l'état de soumission", "submitSuccess": "Soumission réussie", "queueing": "En train de faire la queue.", "duplicateSubmit": "Soumission répétée", "selectTaskStatus": "Choisir l'état de la tâche", "success": "<PERSON><PERSON><PERSON>", "waiting": "<PERSON><PERSON><PERSON>", "queued": "Faire la queue", "executing": "exécution", "failed": "échec", "seconds": "seconde", "unknown": "inconnu", "viewImage": "Cliquez pour voir", "markdownFormat": "Format Markdown", "midjourneyTaskId": "ID de tâche Midjourney", "copiedAsMarkdown": "Copié au format Markdown.", "copyFailed": "Échec de la copie.", "copiedMidjourneyTaskId": "ID de tâche Midjourney copié.", "drawingLogs": "Journal de dessin", "onlyUnarchived": "<PERSON>ules les journaux non archivés sont comptabilisés.", "imagePreview": "Aperçu de l'image", "copiedImageUrl": "L'adresse de l'image a été copiée.", "copy": "copier", "download": "Télécharger", "resultImage": "image de résultat", "downloadError": "Échec du téléchargement de l'image.", "mode": "mod<PERSON><PERSON>", "selectMode": "Choi<PERSON> le mode", "relax": "Mode facile", "fast": "Mode rapide", "turbo": "Mode rapide", "actions": "Opération", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshSuccess": "L'état de la tâche a été rafraîchi avec succès.", "refreshFailed": "Échec de la mise à jour de l'état de la tâche.", "refreshError": "Une erreur s'est produite lors de la mise à jour de l'état de la tâche.", "tasks": {"title": "Liste des tâches", "taskId": "ID de tâche", "platform": "Plateforme", "type": "Type", "status": "Statut", "progress": "Progrès", "submitTime": "Heure de soumission", "startTime": "<PERSON><PERSON> d<PERSON>", "endTime": "Heure de fin", "duration": "<PERSON><PERSON><PERSON>", "result": "Résultat", "taskIdPlaceholder": "Saisir l'ID de tâche", "platformPlaceholder": "Sélectionner la plateforme", "typePlaceholder": "Sélectionner le type", "statusPlaceholder": "Sélectionner le statut", "videoGeneration": "Génération vidéo", "imageGeneration": "Génération d'image", "musicGeneration": "Text", "textGeneration": "文本生成", "unknown": "Text", "success": "成功", "failed": "échec", "inProgress": "Text", "submitted": "已提交", "queued": "Text", "notStarted": "未开始", "viewResult": "Text", "viewError": "查看错误", "taskDetails": "Text", "errorDetails": "错误详情", "loadError": "Error"}, "viewVideo": "查看视频", "videoPreview": "Text", "copyVideoUrl": "Text", "copiedVideoUrl": "Text", "downloadVideo": "下载视频", "videoNotSupported": "Text", "videoUrl": "视频地址", "videoUrls": "Text"}, "mjTaskType": {"IMAGINE": "<PERSON><PERSON><PERSON>rer une image", "UPSCALE": "<PERSON><PERSON><PERSON><PERSON>", "VARIATION": "changement", "REROLL": "<PERSON><PERSON><PERSON><PERSON>", "DESCRIBE": "Image to text", "BLEND": "mélange d'images", "OUTPAINT": "zoom", "DEFAULT": "inconnu"}, "mjCode": {"submitSuccess": "Soumission réussie", "queueing": "En train de faire la queue.", "duplicateSubmit": "Soumission répétée", "unknown": "inconnu"}, "mjStatus": {"success": "<PERSON><PERSON><PERSON>", "waiting": "<PERSON><PERSON><PERSON>", "queued": "Faire la queue", "executing": "Exécution", "failed": "échec", "unknown": "inconnu"}, "tokensTable": {"title": "Gestion des jetons", "table": {"title": "Gestion des jetons", "toolBar": {"add": "<PERSON><PERSON>er un nouveau jeton", "delete": "<PERSON><PERSON><PERSON><PERSON> le jeton", "deleteConfirm": "Vous êtes en train de supprimer en masse {{count}} jetons, cette opération est irréversible.", "export": "Exporter", "exportConfirm": "Exporter le jeton de la page actuelle ?"}, "action": "Opération"}, "modal": {"title": {"add": "<PERSON><PERSON>er un nouveau jeton", "edit": "Modifier le jeton"}, "field": {"name": "Nom du jeton", "description": "Description du jeton", "type": {"default": "Méthode de facturation", "type1": "Facturation à la consommation", "type2": "Facturation à l'acte", "type3": "Facturation mixte", "type4": "Priorité par quantité", "type5": "Priorité secondaire"}, "status": "État", "statusEnabled": "normal", "statusDisabled": "Désactiver", "statusExpired": "expiré", "statusExhausted": "é<PERSON><PERSON>é", "models": "<PERSON><PERSON><PERSON><PERSON> disponible", "usedQuota": "Quota de consommation", "remainQuota": "solde restant", "createdTime": "Date de création", "expiredTime": "date d'expiration", "all": "<PERSON>ut", "more": "plus", "notEnabled": "Non activé", "unlimited": "sans restriction", "daysLeft": "Expire dans {{days}} jours.", "expired": "expiré depuis {{days}} jours", "userId": "Identifiant utilisateur", "key": "Clé API", "neverExpire": "<PERSON><PERSON> p<PERSON>"}, "delete": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": "Êtes-vous sûr de vouloir supprimer la clé API {{name}} ?"}, "footer": {"cancel": "Annuler", "confirm": "Confirmer", "update": "Mise à jour"}, "bridge": {"title": "Intégration rapide des canaux", "placeholder": "Veuillez entrer votre adresse de service {{name}}."}, "copy": {"title": "<PERSON><PERSON> man<PERSON>"}}, "dropdown": {"onlineChat": "Dialogue en ligne", "disableToken": "Interdiction de jeton", "enableToken": "<PERSON><PERSON> le <PERSON>on", "editToken": "Modifier le jeton", "requestExample": "Exemple de demande", "tokenLog": "Journal des jetons", "shareToken": "Jeton de partage", "quickIntegration": "Intégration en un clic"}, "error": {"fetchModelsFailed": "Échec de l'obtention du modèle : {{message}}", "batchDeleteFailed": "Échec de la suppression en masse : {{message}}", "deleteTokenFailed": "Échec de la suppression du jeton : {{message}}", "refreshTokenFailed": "Échec du rafraîchissement du jeton : {{message}}", "enableTokenFailed": "Échec de l'activation du jeton : {{message}}", "disableTokenFailed": "Échec de la désactivation du jeton : {{message}}", "fetchDataFailed": "Échec de l'obtention des données : {{message}}"}, "success": {"batchDelete": "Suppression ré<PERSON>ie de {{count}} jetons.", "shareTextCopied": "Le texte partagé a été copié dans le presse-papiers.", "tokenCopied": "Le jeton a été copié dans le presse-papiers.", "deleteToken": "Suppression du jeton réussie.", "refreshToken": "Le jeton de rafraîchissement a été réussi.", "enableToken": "Activation du jeton réussie", "disableToken": "Désactivation du jeton réussie", "export": "Exportation du jeton de la page actuelle réussie."}, "warning": {"copyFailed": "Échec de la copie, veuillez copier manuellement.", "invalidServerAddress": "<PERSON><PERSON><PERSON>z entrer l'adresse du serveur correcte."}, "info": {"openingBridgePage": "Ouverture de la page de connexion, le jeton a été copié pour vous."}, "export": {"name": "Nom", "key": "clé", "billingType": "Méthode de facturation", "status": "État", "models": "<PERSON><PERSON><PERSON><PERSON> disponible", "usedQuota": "quota de consommation", "remainQuota": "solde restant", "createdTime": "Date de création", "expiredTime": "date d'expiration", "unlimited": "sans restriction", "neverExpire": "<PERSON><PERSON> p<PERSON>"}, "billingType": {"1": "Facturation à la consommation", "2": "Facturation à l'acte", "3": "Facturation mixte", "4": "Priorité par quantité", "5": "Priorité secondaire"}, "bridge": {"quickIntegration": "Intégration en un clic"}}, "editTokenModal": {"editTitle": "Modifier le jeton", "createTitle": "<PERSON><PERSON><PERSON> un jeton", "defaultTokenName": "Le jeton de {{username}} {{date}}", "tokenName": "Nom du jeton", "unlimitedQuota": "limite illimitée", "remainingQuota": "solde restant", "authorizedQuota": "Montant auto<PERSON>", "quotaLimitNote": "Le montant maximum disponible pour le jeton est limité par le solde du compte.", "quickOptions": "Options rapides", "neverExpire": "<PERSON><PERSON> p<PERSON>", "expiryTime": "date d'expiration", "billingMode": "Modèle de facturation", "selectGroup": "Choisir un groupe", "switchGroup": "Choisir un groupe", "switchGroupTooltip": "Sélectionnez le groupe auquel appartient le jeton, différents groupes ont des tarifs et des autorisations de fonctionnalités différents. Si vous ne sélectionnez pas, le groupe actuel de l'utilisateur sera utilisé par défaut.", "switchGroupHint": "Le choix du groupe influencera le multiplicateur de facturation des jetons et les modèles disponibles, veuillez choisir en fonction de vos besoins réels.", "importantFeature": "Important", "tokenRemark": "Remarque sur le jeton", "discordProxy": "Proxy Discord", "enableAdvancedOptions": "Activer les options avancées", "generationAmount": "Quantité générée", "availableModels": "<PERSON><PERSON><PERSON><PERSON> disponible", "selectModels": "<PERSON><PERSON><PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>/Ajouter des modèles disponibles, laisser vide pour indiquer aucune restriction.", "activateOnFirstUse": "Activation initiale", "activateOnFirstUseTooltip": "Si cette option est activée lors de l'activation lors de la première utilisation, elle remplacera la durée de validité du jeton configurée ci-dessus.", "activationValidPeriod": "période de validité d'activation", "activationValidPeriodTooltip": "Durée de validité du token après activation lors de la première utilisation (unité : jours)", "ipWhitelist": "Liste blanche IP", "ipWhitelistPlaceholder": "Adresse IP (plage), prend en charge IPV4 et IPV6, plusieurs séparés par des virgules.", "rateLimiter": "limiteur de courant", "rateLimitPeriod": "période de limitation de flux", "rateLimitPeriodTooltip": "Période de limitation de débit (unité : seconde)", "rateLimitCount": "Limitation du nombre de flux", "rateLimitCountTooltip": "Nombre d'utilisations disponibles pendant la période de limitation de flux.", "promptMessage": "Message d'alerte", "promptMessageTooltip": "Message d'alerte en cas de dépassement de la limite de flux.", "promotionPosition": "Emplacement de promotion", "promotionPositionStart": "D<PERSON>but", "promotionPositionEnd": "fin", "promotionPositionRandom": "aléatoire", "promotionContent": "Contenu promotionnel", "currentGroup": "Groupe actuel", "searchGroupPlaceholder": "Rechercher le nom du groupe, la description ou le multiplicateur...", "mjTranslateConfig": "Configuration de traduction MJ", "mjTranslateConfigTip": "Configuration de traduction valable uniquement pour les mots-clés Midjourney.", "mjTranslateBaseUrlPlaceholder": "Veuillez entrer l'URL de base du service de traduction.", "mjTranslateApiKeyPlaceholder": "Veuillez entrer la clé API du service de traduction.", "mjTranslateModelPlaceholder": "Veuillez entrer le nom du modèle utilisé pour le service de traduction.", "mjTranslateBaseUrlRequired": "Lors de l'activation de la traduction, une URL de base doit être fournie.", "mjTranslateApiKeyRequired": "L'activation de la traduction nécessite une clé API.", "mjTranslateModelRequired": "Le nom du modèle doit être fourni lors de l'activation de la traduction."}, "addTokenQuotaModal": {"title": "Gestion du solde de jetons {{username}}", "defaultReason": "Opération de l'administrateur", "enterRechargeAmount": "Veuillez entrer le montant du recharge.", "enterRemark": "Veuillez entrer un message de remarque.", "confirmOperation": "Confirmer l'opération", "confirmContent": "Confirmez-vous {{username}}{{action}}{{amount}} dollars {{updateExpiry}} ?", "recharge": "recharge", "deduct": "déduction", "andUpdateExpiry": "et met à jour la validité du solde à {{days}} jours.", "alertMessage": "L'entrée d'un nombre négatif peut déduire le solde de l'utilisateur.", "rechargeAmount": "Montant de recharge", "operationReason": "Raison de l'opération", "finalBalance": "Solde final"}, "billingType": {"1": "Facturation à la consommation", "2": "Facturation à l'acte", "3": "Facturation mixte", "4": "Priorité par quantité", "5": "Priorité secondaire", "payAsYouGo": "Facturation à la consommation", "payPerRequest": "Facturation à l'acte", "hybrid": "Facturation mixte", "payAsYouGoPriority": "Priorité par quantité", "payPerRequestPriority": "Priorité secondaire", "unknown": "méthode inconnue"}, "packagePlanAdmin": {"title": "forfait", "table": {"title": "Gestion des forfaits", "toolBar": {"add": "<PERSON><PERSON>er un nouveau forfait", "delete": "Supprimer le forfait"}, "action": {"edit": "<PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON><PERSON>", "detail": "Détails", "recovery": "Mise en ligne", "offline": "<PERSON><PERSON><PERSON> du march<PERSON>"}}, "modal": {"title": {"add": "<PERSON><PERSON>er un nouveau forfait", "edit": "Édition de forfait"}, "field": {"name": "Nom du forfait", "type": {"default": "Type de forfait", "type1": "Forfait de crédit", "type2": "forfait à la séance", "type3": "Forfait de durée"}, "group": "Groupes de forfaits", "description": "Description du forfait", "price": "Prix du forfait", "valid_period": "Date d'expiration", "first_buy_discount": "Remise pour premier achat", "rate_limit_num": "Text", "rate_limit_duration": "période de restriction", "inventory": "Inventaire des forfaits", "available_models": "<PERSON><PERSON><PERSON><PERSON> disponible", "quota": "<PERSON><PERSON> forfa<PERSON>", "times": "Nombre de fois du forfait"}, "footer": {"cancel": "Annuler", "confirm": "Confirmer", "update": "Mise à jour"}}}, "login": {"title": "Connexion", "username": "nom d'utilisateur", "password": "mot de passe", "login": "Connexion", "otherLoginMethods": "Autres méthodes de connexion", "register": "<PERSON><PERSON><PERSON> un compte", "accountLogin": "Connexion au compte", "phoneLogin": "Connexion par numéro de téléphone", "usernamePlaceholder": "nom d'utilisateur", "usernameRequired": "Veuillez entrer votre nom d'utilisateur !", "passwordPlaceholder": "mot de passe", "passwordRequired": "Veuillez entrer le mot de passe !", "passwordMaxLength": "La longueur du mot de passe ne peut pas dépasser 20 caractères !", "phonePlaceholder": "numéro de téléphone portable", "phoneRequired": "Veuillez entrer votre numéro de téléphone !", "phoneFormatError": "Le format du numéro de téléphone est incorrect !", "smsCodePlaceholder": "Code de vérification par SMS", "smsCodeCountdown": "Récupérer à nouveau dans {{count}} secondes.", "getSmsCode": "Obtenir le code de vérification", "agreementText": "Je suis d'accord.", "privacyPolicy": "Politique de confidentialité", "and": "et", "serviceAgreement": "« Accord de service »", "alreadyLoggedIn": "Vous êtes connecté.", "weakPasswordWarning": "Votre mot de passe est trop simple, veuillez le modifier rapidement !", "welcomeMessage": "Bienvenue !", "captchaError": "Le code de vérification est incorrect.", "credentialsError": "Nom d'utilisateur ou mot de passe incorrect.", "resetPassword": "Réinitialiser le mot de passe", "captchaExpired": "Le code de vérification n'existe pas ou a expiré.", "loginFailed": "Échec de la connexion : {{message}}", "captchaRequired": "Veuillez entrer le code de vérification !", "captchaPlaceholder": "code de vérification", "smsSent": "Le code de vérification par SMS a été envoyé avec succès.", "smsSendFailed": "L'envoi du code de vérification par SMS a échoué.", "agreementWarning": "Veuillez d'abord accepter la « Politique de confidentialité » et le « Contrat de service ».", "turnstileWarning": "Veuillez réessayer plus tard, Turnstile vérifie l'environnement de l'utilisateur !", "loginSuccess": "Connexion réussie"}, "register": {"title": "S'inscrire", "usernameRequired": "Veuillez entrer votre nom d'utilisateur !", "usernameNoAt": "Le nom d'utilisateur ne peut pas contenir le symbole @.", "usernameNoChinese": "Le nom d'utilisateur ne peut pas contenir de caractères chinois.", "usernameLength": "La longueur du nom d'utilisateur doit être de 4 à 12 caractères.", "usernamePlaceholder": "nom d'utilisateur", "passwordRequired": "Veuillez entrer le mot de passe !", "passwordLength": "La longueur du mot de passe doit être comprise entre 8 et 20 caractères.", "passwordPlaceholder": "mot de passe", "confirmPasswordRequired": "Veuillez confirmer le mot de passe !", "passwordMismatch": "Les deux mots de passe saisis ne correspondent pas !", "confirmPasswordPlaceholder": "Confirmer le mot de passe", "emailInvalid": "V<PERSON><PERSON>z entrer une adresse e-mail valide !", "emailRequired": "Veuillez entrer votre adresse e-mail !", "emailPlaceholder": "adresse e-mail", "emailCodeRequired": "Veuillez entrer le code de vérification par e-mail !", "emailCodePlaceholder": "Code de vérification par e-mail", "enterCaptcha": "Veuillez entrer le code de vérification.", "resendEmailCode": "<PERSON><PERSON><PERSON> dans {{seconds}} secondes.", "getEmailCode": "Obtenir le code de vérification", "phoneRequired": "Veuillez entrer votre numéro de téléphone !", "phoneInvalid": "Le format du numéro de téléphone est incorrect !", "phonePlaceholder": "numéro de téléphone portable", "smsCodeRequired": "Veuillez entrer le code de vérification par SMS !", "smsCodePlaceholder": "Code de vérification par SMS", "resendSmsCode": "<PERSON><PERSON><PERSON> dans {{seconds}} secondes.", "getSmsCode": "Obtenir le code de vérification", "captchaRequired": "Veuillez entrer le code de vérification !", "captchaPlaceholder": "code de vérification", "inviteCodePlaceholder": "Code d'invitation (optionnel)", "submit": "S'inscrire", "successMessage": "Inscription réussie", "failMessage": "Échec de l'inscription", "emailCodeSent": "Le code de vérification par e-mail a été envoyé.", "smsCodeSent": "Le code de vérification par SMS a été envoyé.", "confirm": "Confirmer", "emailVerifyTitle": "Vérification de l'email", "smsVerifyTitle": "Vérification par SMS", "registerVerifyTitle": "Vérification d'inscription"}, "profile": {"timezone": "<PERSON><PERSON> ho<PERSON>", "phoneNumber": "numéro de téléphone portable", "emailAddress": "adresse e-mail", "wechatAccount": "<PERSON><PERSON><PERSON>", "telegramAccount": "Compte Telegram", "bindTelegram": "<PERSON><PERSON>", "balanceValidPeriod": "Date d'expiration du solde", "lastLoginIP": "Dernière adresse IP de connexion", "lastLoginTime": "Dernière date de connexion", "inviteCode": "code d'invitation", "inviteLink": "Lien d'invitation", "generate": "<PERSON><PERSON><PERSON><PERSON>", "pendingEarnings": "Revenus à utiliser", "transfer": "Transfert", "totalEarnings": "Revenu total", "accountBalance": "Solde du compte", "totalConsumption": "<PERSON><PERSON><PERSON><PERSON> cum<PERSON>", "callCount": "Nombre d'appels", "invitedUsers": "Inviter des utilisateurs", "promotionInfo": "Informations promotionnelles", "inviteDescription": "Une invitation, un remboursement à vie. Plus vous invitez, plus le remboursement est important.", "userInfo": "Informations utilisateur", "availableModels": "<PERSON><PERSON><PERSON><PERSON> disponible", "modelNameCopied": "Nom du modèle copié", "noAvailableModels": "Aucun modèle disponible.", "accountOptions": "Options de compte", "changePassword": "Modifier le mot de passe", "systemToken": "jeton système", "unsubscribe": "Annuler", "educationCertification": "Certification éducative", "timezoneUpdateSuccess": "Mise à jour du fuseau horaire réussie.", "inviteLinkCopied": "Le lien d'invitation a été copié.", "inviteLinkCopyFailed": "Échec de la copie du lien d'invitation.", "inviteLinkGenerationFailed": "Échec de la génération du lien d'invitation.", "allModelsCopied": "Tous les modèles ont été copiés dans le presse-papiers.", "copyAllModels": "<PERSON><PERSON>r tous les modèles.", "totalModels": "Nombre de modèles disponibles", "expired": "expiré", "validPeriod": "date d'expiration", "longTermValid": "À long terme.", "failedToLoadModels": "Échec du chargement de la liste des modèles.", "accessTokens": "Text", "accessTokensManagement": "访问令牌gestion", "accessTokenDescription": "Text", "tokenNameLabel": "Title", "tokenNamePlaceholder": "Title", "presetPermissions": "预设权限", "detailPermissions": "Text", "validityPeriod": "Text", "validityPeriodExtra": "0表示n'expire jamais", "remarkLabel": "Label", "remarkPlaceholder": "Please enter...", "createNewToken": "créer un nouveau jeton", "tokenCreatedSuccess": "jeton d'accès créé avec succès", "tokenSavePrompt": "Text", "copyToken": "复制令牌", "readPermission": "Text", "writePermission": "写入权限", "deletePermission": "Text", "tokenManagement": "令牌管理", "channelManagement": "Text", "logView": "Text", "statisticsView": "统计信息", "userManagement": "User", "quotaManagement": "额度gestion", "readOnlyPermission": "Text", "writeOnlyPermission": "只写权限", "readWritePermission": "Text", "standardPermission": "标准权限", "fullPermission": "Text", "selectPermission": "Option", "tokenStatus": "状态", "tokenEnabled": "activer", "tokenDisabled": "Text", "enableToken": "activer", "disableToken": "Text", "deleteToken": "supprimer", "deleteTokenConfirm": "Text", "disableTokenConfirm": "Action", "enableTokenConfirm": "Action", "tokenExpiryNever": "n'expire jamais", "accessTokensInfo": "Description", "accessTokensInfoDetail1": "Text", "accessTokensInfoDetail2": "Description", "accessTokensInfoDetail3": "Text", "accessTokensInfoDetail4": "Description", "accessTokensInfoDetail5": "Description", "noPermission": "无权进行此操作"}, "topup": {"onlineRecharge": "Recharge en ligne", "cardRedemption": "Échange de code de vérification", "accountBalance": "Solde du compte", "rechargeReminder": "Rappel de recharge", "reminder1": "1. Le solde peut être utilisé pour des appels de modèle, des achats de forfaits, etc.", "reminder2": "2. <PERSON> le montant n'est pas crédité après le paiement, veuillez contacter le service client pour le traitement.", "reminder3": "3. Le solde ne peut pas être retiré, mais il peut être transféré au sein du même groupe d'utilisateurs.", "reminder4WithTransfer": "4. Apr<PERSON> le rechargement ré<PERSON>i, la durée de validité du solde du compte sera réinitialisée à", "reminder4WithoutTransfer": "3. <PERSON><PERSON> le rechargement ré<PERSON>i, la durée de validité du solde du compte sera réinitialisée à", "days": "ciel", "paymentSuccess": "<PERSON><PERSON><PERSON>", "paymentError": "<PERSON><PERSON><PERSON> de paiement", "paymentAmount": "Montant à payer :", "purchaseAmount": "Montant d'achat : $", "yuan": "yuan", "or": "ou", "usd": "dollar américain", "cny": "yuan", "enterAmount": "Veuillez entrer le montant du recharge !", "amountPlaceholder": "Veuillez saisir le montant de recharge, à partir de {{min}} dollars américains.", "amountUpdateError": "Erreur lors de la mise à jour du montant.", "alipay": "Alipay", "wechat": "WeChat", "visaMastercard": "Visa / Mastercard", "cardFormatError": "Le format du code d'échange est incorrect.", "redeemSuccess": "{{amount}} é<PERSON><PERSON> avec succès !", "redeemError": "<PERSON><PERSON><PERSON> de conversion, veuil<PERSON><PERSON> réessayer plus tard.", "enterCardKey": "Veuillez entrer le code de la carte de recharge.", "cardKeyPlaceholder": "Veuillez entrer le code de la carte de recharge.", "buyCardKey": "Acheter un code de carte de recharge", "redeem": "Vérification immédiate", "record": {"title": "Historique des recharges", "amount": "Montant de recharge", "payment": "Montant à payer", "paymentMethod": "Modes de paiement", "orderNo": "<PERSON><PERSON><PERSON><PERSON> de commande", "status": "État", "createTime": "Date de création", "statusSuccess": "<PERSON><PERSON><PERSON>", "statusPending": "En cours de traitement", "statusFailed": "échec"}, "paymentMethodAlipay": "Alipay", "paymentMethodWxpay": "WeChat", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "Administrateur", "paymentMethodRedeem": "code de échange", "alipayF2F": "Text"}, "pricing": {"fetchErrorMessage": "Une anomalie est survenue lors de l'obtention des informations de prix, veuillez contacter l'administrateur.", "availableModelErrorMessage": "Une anomalie est survenue lors de l'obtention des modèles disponibles, veuillez contacter l'administrateur.", "modelName": "Nom du modèle", "billingType": "Type de facturation", "price": "Prix", "ratio": "taux de multiplication", "promptPriceSame": "Prix indicatif : identique au tarif d'origine", "completionPriceSame": "Prix de complément : identique au tarif d'origine.", "promptPrice": "Prix indicatif :  {{price}} $ / 1M de tokens", "completionPrice": "Prix complet : $ {{price}} / 1M de jetons", "promptRatioSame": "Taux de suggestion : identique au taux d'origine.", "completionRatioSame": "Complément de multiplicateur : identique au multiplicateur d'origine.", "promptRatio": "Taux de suggestion : {{ratio}}", "completionRatio": "Taux de complément : {{ratio}}", "payAsYouGo": "Paiement à l'usage - Chat", "fixedPrice": "$ {{price}} / fois", "payPerRequest": "Paiement à la séance - Chat", "dynamicPrice": "$ {{price}} / fois", "payPerRequestAPI": "Paiement à l'utilisation - API", "loadingTip": "Obtention des informations de prix...", "userGroupRatio": "Votre ratio de groupe d'utilisateurs est : {{ratio}}", "readFailed": "Échec de la lecture.", "billingFormula": "Frais de facturation à la consommation = Taux de conversion × Coefficient de groupe × Coefficient de modèle × (Nombre de tokens d'invite + Nombre de tokens de complétion × Coefficient de complétion) / 500000 (unité : dollars)", "billingFormula1": "Taux de conversion = (nouveau taux de recharge / ancien taux de recharge) × (nouveau taux de regroupement / ancien taux de regroupement)", "generatedBy": "Cette page est générée automatiquement par {{systemName}}.", "modalTitle": "Détails des prix", "perMillionTokens": "/1M jetons", "close": "fermer", "searchPlaceholder": "Recherche du nom du modèle", "viewGroups": "Voir le groupe", "copiedToClipboard": "Copié dans le presse-papiers.", "copyFailed": "Échec de la copie.", "groupName": "Nom du groupe", "availableGroups": "Groupes disponibles pour le modèle {{model}}", "noGroupsAvailable": "Aucun groupe disponible.", "modelGroupsErrorMessage": "Échec de l'obtention des données de regroupement du modèle.", "currentGroup": "Groupe actuel", "copyModelName": "<PERSON><PERSON><PERSON> le nom du modèle", "groupRatio": "Taux de répartition", "closeModal": "fermer", "groupsForModel": "Modèle disponible par groupe", "actions": "Opération", "filterByGroup": "Filtrer par groupe", "groupSwitched": "Passage à la groupe : {{group}}", "showAdjustedPrice": "Afficher le prix après ajustement du groupe (taux actuel : {{ratio}})"}, "guestQuery": {"usageTime": "Utilisation du temps", "modelName": "Nom du modèle", "promptTooltip": "Entrée de consommation de jetons", "completionTooltip": "Sortie de consommation de jetons", "quotaConsumed": "Quota de consommation", "pasteConfirm": "Un jeton valide a été détecté dans le presse-papiers, souhaitez-vous le coller ?", "queryFailed": "Échec de la requête.", "tokenExpired": "<PERSON> jeton a expiré.", "tokenExhausted": "Le quota de ce jeton a été épuisé.", "invalidToken": "Veu<PERSON>z entrer le bon jeton.", "focusRequired": "Veuillez vous assurer que la page est en état de focus.", "queryFirst": "Veuillez d'abord vérifier.", "tokenInfoText": "Total des jetons : {{totalQuota}}  \nConsommation de jetons : {{usedQuota}}  \nSolde des jetons : {{remainQuota}}  \nNombre d'appels : {{callCount}}  \nValide jusqu'au : {{validUntil}}", "unlimited": "sans restriction", "neverExpire": "<PERSON><PERSON> p<PERSON>", "infoCopied": "Les informations du jeton ont été copiées dans le presse-papiers.", "copyFailed": "Échec de la copie.", "noDataToExport": "Aucune donnée à exporter.", "prompt": "<PERSON><PERSON><PERSON>", "completion": "<PERSON><PERSON><PERSON><PERSON>", "disabled": "La recherche des visiteurs n'est pas activée.", "tokenQuery": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "tokenPlaceholder": "Veuillez entrer le jeton à interroger (sk-xxx)", "tokenInfo": "Informations sur le jeton", "copyInfo": "<PERSON><PERSON><PERSON> les informations", "totalQuota": "Montant total des jetons", "usedQuota": "Consommation de jetons", "remainQuota": "Solde de jeton", "callCount": "Nombre d'appels", "validUntil": "Date d'expiration jusqu'à", "currentRPM": "RPM actuel", "currentTPM": "TPM actuel", "callLogs": "Journal d'appel", "exportLogs": "Exporter les journaux"}, "agencyProfile": {"fetchError": "Échec de l'obtention des informations sur l'agent.", "fetchCommissionError": "Échec de l'obtention de la liste des commissions.", "systemPreset": "Paramètres système prédéfinis", "lowerRatioWarning": "Le taux est inférieur à celui prédéfini par le système.", "lowerRatioMessage": "Les taux suivants sont inférieurs à la valeur prédéfinie par le système, veuillez les modifier rapidement :", "cancelRatioEdit": "Annuler les frais de modification", "updateSuccess": "Mise à jour réussie", "updateError": "Échec de la mise à jour des informations de l'agent :", "updateFailed": "Échec de la mise à jour :", "customPriceUpdateSuccess": "Mise à jour du prix personnalisé ré<PERSON>.", "customPriceUpdateError": "Échec de la mise à jour du prix personnalisé :", "time": "Temps", "type": "Type", "agencyCommission": "Commission de l'agent", "unknownType": "Type inconnu", "amount": "montant", "balance": "solde", "description": "Description", "group": "Groupe", "customRate": "<PERSON><PERSON>", "systemDefaultRate": "Taux par défaut du système", "action": "Opération", "save": "sauve<PERSON><PERSON>", "cancel": "Annuler", "edit": "<PERSON><PERSON><PERSON>", "agencyConsole": "Console d'agent", "agencyInfo": "Informations sur l'agent", "editInfo": "Modifier les informations", "agencyName": "Nom de l'agent", "agencyLevel": "Niveau d'agent", "level1": "Niveau 1", "subordinateUsers": "utilisateur subalterne", "totalSales": "<PERSON><PERSON>re d'affaires total", "commissionIncome": "Revenu de commission", "cumulativeEarnings": "revenu cumul<PERSON>", "agencyFunctions": "Fonctionnalité de proxy", "hideSubordinateUsers": "Masquer les utilisateurs de niveau inférieur", "viewSubordinateUsers": "Voir les utilisateurs subordonnés", "hideCommissionDetails": "Text", "viewCommissionDetails": "Consulter les détails de la commission", "hideCustomPrice": "Masquer le prix personnalisé", "setCustomPrice": "Définir un prix personnalisé", "subordinateUsersList": "Liste des utilisateurs inférieurs", "commissionRecords": "Enregistrement des commissions", "customPriceSettings": "Paramètres de prix personnalisés", "saveChanges": "Enregistrer les modifications", "editAgencyInfo": "Modifier les informations de l'agent.", "logo": "Logo", "setAgencyLogo": "Configurer le logo de l'agent.", "customHomepage": "Page d'accueil personnalis<PERSON>", "aboutContent": "À propos du contenu", "newHomepageConfig": "Nouvelle configuration de la page d'accueil", "customAnnouncement": "<PERSON><PERSON><PERSON>", "customRechargeGroupRateJson": "JSON des taux de regroupement de recharge personnalisés", "customRechargeRate": "Taux de recharge personnalisé", "viewSystemDefaultRate": "Consulter le tarif par défaut du système.", "rateComparison": "Comparaison des tarifs", "comparisonResult": "Résultats de la comparaison", "higherThanSystem": "Sup<PERSON>ur au système", "lowerThanSystem": "inférieur au système", "equalToSystem": "égale au système", "unknown": "inconnu", "notAnAgentYet": "Vous n'êtes pas encore un agent.", "becomeAnAgent": "Devenir agent.", "startYourOnlineBusiness": "🌟 Lancez facilement votre entreprise en ligne", "becomeOurAgent": "Devenez notre agent et profitez d'une expérience d'entrepreneuriat sans pression :", "noInventory": "💼 Pas besoin de stock, aucune pression de rotation de fonds.", "instantCommission": "💰 Partage instantané des ventes, obtenez des retours généreux en proportion.", "easyManagement": "🖥️ Pas besoin de compétences en création de sites, gérez facilement votre boutique en ligne.", "flexibleDomainChoice": "🌐 Choix de nom de domaine flexible", "youCan": "<PERSON><PERSON> p<PERSON> :", "useOwnDomain": "🏠 Utiliser son propre nom de domaine", "orUseOurSubdomain": "🎁 Ou nous pouvons vous fournir un sous-domaine exclusif.", "convenientStart": "🔥 Que vous soyez un expert ou que vous débutiez, nous vous offrons un moyen pratique de commencer.", "actNow": "🚀 Agissez maintenant !", "contactAdmin": "Contactez l'administrateur du site pour commencer votre aventure d'agent ! 📞", "applyNow": "Postuler maintenant", "contactCooperation": "Consultation et coopération", "understandPolicy": "Comprendre la politique des agents et les détails de la coopération.", "provideDomain": "Fournir un nom de domaine", "configDomain": "Fournis<PERSON>z votre nom de domaine, nous vous aiderons à le configurer.", "promoteAndEarn": "Promotion des bénéfices", "startPromoting": "Commencez à promouvoir votre site d'agent et gagnez des commissions.", "noDeploymentWorries": "Pas besoin de s'inquiéter des déploiements complexes de services cloud, des canaux de paiement ou des problèmes de stock.", "easySetup": "Il suffit de fournir le nom de domaine et de configurer selon le tutoriel pour lancer facilement une activité de revendeur API de niveau entreprise.", "customizeContent": "Vous pouvez personnaliser le prix, les informations du site, le SEO, le logo, etc.", "commissionBenefits": "En tant qu'agent, vous recevrez une part des recharges des utilisateurs, le système déduira automatiquement les coûts, et le montant restant pourra être retiré à tout moment.", "joinNowBenefit": "Rejoignez-nous dès maintenant pour profiter ensemble des bénéfices de l'ère de l'IA !", "groups": {"student": "étudiant universitaire", "studentDesc": "Avec le temps libre, j'espère augmenter facilement mes revenus grâce à des activités promotionnelles pour couvrir une partie de mes frais de subsistance et de mes dépenses de loisirs.", "partTime": "Travail à temps partiel ou activité secondaire", "partTimeDesc": "Pas besoin d'investir beaucoup de temps, il suffit de promouvoir simplement pendant votre temps libre pour gagner facilement un revenu supplémentaire.", "mediaWorker": "pratiquant des médias sociaux", "mediaWorkerDesc": "Avec une certaine base de fans, il suffit d'ajouter un lien à la fin de l'article ou du post pour générer facilement des revenus supplémentaires.", "freelancer": "travailleur indépendant", "freelancerDesc": "Avoir beaucoup de temps flexible et augmenter facilement ses revenus supplémentaires simplement en participant à des activités de vente."}, "stories": {"story1": {"name": "<PERSON>", "role": "étudiant universitaire"}, "story2": {"name": "Madame Li", "role": "enseignant de collège"}, "story3": {"name": "Monsieur <PERSON>", "role": "commerce électronique"}, "story4": {"name": "<PERSON>", "role": "média indépendant"}, "story5": {"name": "<PERSON>", "role": "chercheur scientifique"}, "story6": {"name": "<PERSON>", "role": "<PERSON><PERSON><PERSON> <PERSON>"}, "story7": {"name": "<PERSON>", "role": "média indépendant"}, "story8": {"name": "Monsieur <PERSON>", "role": "secteur informatique"}}, "earnedAmount": "V<PERSON> avez gagné {{amount}}.", "applyForAgentNow": "Postulez dès maintenant pour devenir agent.", "businessLinesConnected": "Plus de 40 lignes de services ont déjà été intégrées.", "agencyJoin": "Text", "becomeExclusiveAgent": "Devenez notre agent exclusif.", "startBusinessJourney": "Commencez facilement votre voyage commercial~", "welcomeToAgencyPage": "Bienvenue sur notre page d'agent !", "earningsTitle": "Plus de cent personnes ont déjà gagné plus de 3000 yuans.", "becomeAgentSteps": "Devenir agent : étapes à suivre.", "agencyRules": "Règles de représentation", "suitableGroups": "Public cible", "agencyImages": {"becomeAgent": "Devenir agent.", "agencyBusiness": "Activité d'agence"}, "rules": {"howToEstablishRelation": "Comment l'utilisateur peut-il établir une relation de mandat avec moi ?", "howToEstablishRelationAnswer": "En vous inscrivant sur votre site d'agent, vous devenez votre utilisateur.", "canSetPrice": "<PERSON>ui<PERSON>-je définir le prix de vente ?", "canSetPriceAnswer": "Bien sûr ! Mais votre prix de vente doit être supérieur de 10 % au prix d'achat.", "commissionShare": "Combien de commission puis-je obtenir ?", "commissionShareAnswer": {"assumption": "Supposons : votre prix d'achat est de 1 $ = 1 yuan, votre prix de vente est de 1 $ = 2 yuan, et votre taux de commission est de 90 %.", "example": "L'utilisateur achète pour 10 $ sur votre site, dépensant 20 yuans.", "calculation": "<PERSON><PERSON> pouvez obtenir : (2-1)*10*0,9 = 9 yuans.", "explanation": "Interprétation : (prix de vente - prix d'achat) * volume des transactions * taux de commission"}}}, "error": {"title": "<PERSON><PERSON><PERSON>", "content": "Une erreur s'est produite."}, "loading": {"title": "Chargement en cours", "content": "Chargement en cours..."}, "notfound": {"title": "404", "content": "Page non trouvée"}, "servererror": {"title": "500", "content": "<PERSON><PERSON><PERSON> du <PERSON>"}, "unauthorized": {"title": "401", "content": "Non autorisé"}, "forbidden": {"title": "403", "content": "Accès interdit"}, "networkerror": {"title": "<PERSON><PERSON><PERSON>", "content": "<PERSON><PERSON><PERSON>"}, "timeout": {"title": "dépassement de délai", "content": "<PERSON><PERSON><PERSON> <PERSON><PERSON>"}, "noresult": {"title": "Aucun résultat", "content": "Aucun résultat"}, "nopermission": {"title": "Pas d'autorisation", "content": "Pas d'autorisation"}, "channelBridge": {"title": "Intégration rapide des canaux", "channelPlatform": "plateforme de canal", "billingMethod": "Méthode de facturation", "channelName": "Nom du canal", "remark": "<PERSON><PERSON><PERSON>", "availableGroups": "Groupes disponibles", "availableModels": "<PERSON><PERSON><PERSON><PERSON> disponible", "channelKey": "Clé de canal", "proxyAddress": "Adresse de connexion", "cancel": "Annuler", "submit": "So<PERSON><PERSON><PERSON>", "gpt35Models": "Modèle GPT-3.5", "gpt4Models": "Modèle GPT-4", "clear": "Vider", "customModelName": "Nom du modèle personnal<PERSON>", "add": "Ajouter", "moreConfigReminder": "Pour plus de configurations, veuillez enregistrer le canal puis éditer.", "quickIntegration": "Intégration en un clic", "selectBillingMethod": "Veuillez choisir le mode de facturation.", "enterChannelName": "Veuillez entrer le nom du canal.", "enterChannelRemark": "Veuillez entrer les remarques sur le canal.", "selectAvailableGroups": "Veuillez sélectionner le groupe pouvant utiliser ce canal.", "selectAvailableModels": "Sélectionner/rechercher les modèles disponibles sur ce canal.", "enterChannelKey": "Veuillez entrer la clé de canal.", "proxyAddressPlaceholder": "Cette option est facultative, utilisée pour effectuer des appels API via un site proxy. Veuillez entrer l'adresse du site proxy.", "includes16kModels": "Comprend un modèle 16k.", "excludes32kModels": "Ne comprend pas le modèle 32k.", "cleared": "<PERSON><PERSON>jà vidé.", "addCustomModel": "Ajouter un modèle personnalisé", "clipboardTokenDetected": "Un jeton valide a été détecté dans le presse-papiers, souhaitez-vous le coller ?", "channelIntegrationSuccess": "Intégration du canal réussie !", "channelIntegrationFailed": "Échec de la connexion au canal :"}, "about": {"loading": "Obtenir le contenu le plus récent...", "noContent": "L'administrateur n'a pas défini le contenu de la page à propos.", "loadFailed": "Échec du chargement du contenu..."}, "onlineTopupRecord": {"title": "Historique des recharges", "columns": {"id": "ID", "username": "utilisateur", "amount": "Montant de recharge", "money": "Montant à payer", "paymentMethod": "Modes de paiement", "tradeNo": "<PERSON><PERSON><PERSON><PERSON> de commande", "status": "État", "createTime": "Date de création"}, "status": {"success": "<PERSON><PERSON><PERSON>", "pending": "En cours de traitement", "failed": "échec"}, "paymentMethod": {"alipay": "Alipay", "wxpay": "WeChat", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "PayPal"}}, "logContentDetail": {"description": "Informations descriptives", "downstreamError": "Erreur en aval", "originalError": "Erreur originale", "requestParams": "Paramètres de la requête", "copy": "copier"}, "viewMode": {"switchTo": "Changer de perspective {{mode}}.", "cost": "coût", "usage": "Utilisation"}, "agenciesTable": {"title": "Gestion des agents", "addAgency": "Nouveaux agents commerciaux", "columns": {"id": "ID", "userId": "Identifiant utilisateur", "name": "Nom", "domain": "nom de domaine", "commissionRate": "Taux de commission", "salesVolume": "chiffre d'affaires", "userCount": "Nombre d'utilisateurs", "commissionIncome": "Revenu de commission", "historicalCommission": "revenu cumul<PERSON>", "actions": "Opération"}, "confirm": {"deleteTitle": "Êtes-vous sûr de vouloir supprimer cet agent ?", "updateName": "Mise à jour du nom de l'agent...", "updateSuccess": "Mise à jour réussie", "updateFailed": "Échec de la mise à jour.", "deleteSuccess": "Suppression réussie !"}, "messages": {"getListFailed": "Échec de l'obtention de la liste des agents : {{message}}", "deleteSuccess": "Suppression réussie !", "loadingData": "Chargement en cours..."}}, "units": {"times": "suivant", "percentage": "{{value}}%", "formatUsage": "{{name}} : {{value}} fois ({{percent}}%)"}, "dailyUsage": {"total": "Total", "totalCost": "Coût total", "tooltipTitle": {"cost": "Situation des coûts", "usage": "Utilisation"}, "yAxisName": {"cost": "Coût (USD)", "usage": "Utilisation (USD)"}}, "dailyUsageByModel": {"total": "Total", "tooltipTotal": "Total : $ {{value}}", "switchTo": "Changer pour", "cost": "coût", "usage": "Utilisation", "perspective": "perspective", "granularity": {"hour": "à l'heure", "day": "par jour", "week": "par semaine", "month": "par mois"}}, "checkinModal": {"title": "Veuillez compléter la vérification.", "captchaPlaceholder": "code de vérification", "confirm": "Déterminé", "close": "fermer"}, "balanceTransfer": {"title": "Transfert entre comptes", "accountInfo": {"balance": "Solde du compte", "transferFee": "Frais de transfer<PERSON>", "groupNote": "Les transferts ne peuvent être effectués qu'entre des groupes d'utilisateurs identiques."}, "form": {"receiverId": "ID du destinataire", "receiverUsername": "Nom d'utilisateur du destinataire", "remark": "Informations complémentaires", "amount": "<PERSON><PERSON>", "expectedFee": "<PERSON><PERSON> p<PERSON>vu<PERSON>", "submit": "Initier un virement"}, "result": {"success": "<PERSON><PERSON>", "continueTransfer": "Con<PERSON>uer le transfert.", "viewRecord": "Consulter les enregistrements"}, "warning": {"disabled": "La fonction de transfert n'est pas activée par l'administrateur, elle ne peut donc pas être utilisée pour le moment."}, "placeholder": {"autoCalculate": "Le montant du transfert est calculé automatiquement."}}, "channelsTable": {"title": "Gestion des canaux", "columns": {"id": "ID", "name": "Nom", "type": "Type", "key": "clé", "base": "Adresse de l'interface", "models": "mod<PERSON><PERSON>", "weight": "poids", "priority": "Priorité", "retryInterval": "Intervalle de nouvelle tentative", "responseTime": "Temps de réponse", "rpm": "RPM", "status": "Statut", "quota": "solde", "expireTime": "date d'expiration", "group": "Groupe", "billingType": "Type de facturation", "actions": "Opération", "fusing": "coupure de circuit", "sort": "Priorité", "createdTime": "Time", "disableReason": "禁用原因"}, "status": {"all": "tout", "normal": "normal", "enabled": "État normal", "manualDisabled": "Désactivation manuelle", "waitingRetry": "At<PERSON>re le redémarrage", "suspended": "Suspendre l'utilisation", "specified": "État désigné", "allDisabled": "Désactiver", "specifiedDisabled": "Type de désactivation spécifié", "partiallyDisabled": "Status"}, "placeholder": {"selectGroup": "Veuillez sélectionner/rechercher un groupe.", "selectStatus": "Choisir l'état du canal", "inputSelectModel": "Entrer/choisir le nom du modèle", "selectFusingStatus": "Choisir l'état de coupure automatique."}, "quota": {"usageAmount": "Consommation : {amount}", "remainingAmount": "Reste : {amount}", "customTotalAmount": "Montant personnalisé : {amount}", "updateNotSupported": "La mise à jour du solde n'est pas encore prise en charge, veuillez utiliser un solde personnalisé.", "details": "Détails", "sufficient": "suffisant"}, "actions": {"edit": "<PERSON><PERSON><PERSON>", "copy": "Canal de clonage", "delete": "Supprimer le canal", "enable": "Activer", "disable": "Désactiver", "test": "test", "advancedTest": "Test avancé", "viewLog": "Journal des canaux", "viewAbility": "Vérifier les capacités", "cleanUsage": "Vider l'utilisation.", "updateBalance": "Mettre à jour le solde", "copyKey": "Co<PERSON>r la clé"}, "confirm": {"deleteTitle": "Confirmation de suppression", "deleteContent": "Êtes-vous sûr de vouloir supprimer le canal {{name}} (#{{id}}) ?", "cleanUsageTitle": "Confirmation de la réinitialisation de l'utilisation", "cleanUsageContent": "Êtes-vous sûr de vouloir vider le montant consommé du canal {{name}} (#{{id}}) ?", "testTitle": "Confirmation de test", "testContent": "Êtes-vous sûr de vouloir tester le canal {{status}} ?", "testNote": "Remarque : cette fonctionnalité nécessite d'être utilisée avec [Configuration] -> [<PERSON><PERSON><PERSON>] -> [Paramètres de surveillance] -> [Désactiver le canal en cas d'échec, activer le canal en cas de succès]. Si les paramètres correspondants ne sont pas activés, le canal ne sera pas automatiquement désactivé ou activé après la fin du test.", "deleteDisabledTitle": "Confirmation de suppression", "deleteDisabledContent": "Êtes-vous sûr de vouloir supprimer tous les canaux {{type}} ?"}, "messages": {"operationSuccess": "Opération réussie", "operationSuccessWithSort": "Opération réussie, l'ordre des canaux peut avoir changé, il est conseillé de trier par ID !", "operationFailed": "Échec de l'opération : {{message}}", "testRunning": "Le test du canal {{name}}(#{{id}}) est en cours, veuillez patienter...", "testSuccess": "Le canal « {{name}}(#{{id}}) » {{model}} a r<PERSON><PERSON><PERSON> le test, temps de réponse {{time}} secondes.", "testFailed": "Le test du canal « {{name}}(#{{id}}) » {{model}} a échoué. Code d'état : {{code}}, Raison : {{reason}}, cliquez pour voir les détails.", "testStarted": "Commencez à tester le canal {{status}}, veuillez rafraîchir pour voir les résultats plus tard. L'application des résultats du test dépend de vos paramètres de surveillance.", "testOperationFailed": "Échec du test", "deleteSuccess": "Suppression ré<PERSON>ie de {{count}} canaux.", "deleteFailed": "Échec de la suppression : {{message}}", "modelPrefix": "<PERSON><PERSON><PERSON><PERSON> {{model}}", "channelInfo": "Informations sur les canaux", "channelDetail": "{{nom}}(#{{id}}){{infoModèle}}", "updateBalanceSuccess": "La mise à jour du solde du canal « {{name}} » a réussi.", "updateBalanceFailed": "Échec de la mise à jour du solde du canal « {{name}} » : {{message}}", "updateAllBalanceStarted": "Commencer à mettre à jour le solde de tous les canaux en état normal.", "updateAllBalanceSuccess": "Mise à jour réussie des soldes de tous les canaux.", "fetchGroupError": "Erreur lors de l'obtention des données du groupe de canaux : {{response}}", "fetchChannelError": "Échec de l'obtention des données du canal : {{message}}", "selectChannelFirst": "Veuillez d'abord sélectionner le canal à supprimer.", "deleteDisabledSuccess": "Tous les canaux {{type}} ont été supprimés, au total {{count}}.", "deleteOperationFailed": "Échec de la suppression.", "copySuccess": "<PERSON><PERSON> r<PERSON>", "copyFailed": "Échec de la copie : {{message}}", "emptyKey": "La clé est vide.", "testSuccessWithWarnings": "Message", "viewDetails": "Message", "fetchChannelDetailError": "Message", "topupSuccess": "充值成功", "topupFailed": "rechargeéchec：{{message}}"}, "popover": {"channelInfo": "Informations sur les canaux"}, "menu": {"deleteManualDisabled": "Supprimer les canaux désactivés manuellement.", "deleteWaitingRetry": "Supprimer le canal d'attente de redémarrage.", "deleteSuspended": "Supprimer le canal suspendu.", "testAll": "Tester tous les canaux.", "testNormal": "Tester le canal normal", "testManualDisabled": "Tester la désactivation manuelle du canal.", "testWaitingRetry": "Tester en attente de redémarrage du canal.", "testSuspended": "Test de suspension de l'utilisation des canaux.", "deleteDisabledAccount": "Text", "deleteQuotaExceeded": "Text", "deleteRateLimitExceeded": "删除频率限制渠道", "deleteInvalidKey": "Text", "deleteConnectionError": "删除连接错误渠道"}, "tooltip": {"testNote": "Il est nécessaire de configurer [Configuration] -> [<PERSON><PERSON><PERSON>] -> [Paramètres de surveillance] -> [Désactiver le canal en cas d'échec, activer le canal en cas de succès] pour l'utiliser. Si cela n'est pas activé, le canal ne sera pas automatiquement désactivé ou activé après la fin du test de vitesse."}, "disableReasons": {"account_deactivated": "compte désactivé", "quota_exceeded": "Text", "rate_limit_exceeded": "limite de fréquence", "invalid_key": "Text", "connection_error": "连接erreur"}, "topup": {"reminder1": "Text", "reminder2": "Text"}}, "billingTypes": {"quota": "limite", "times": "nombre de fois"}, "serverLogViewer": {"title": "Visualiseur de journaux de serveur", "connecting": "Connexion au serveur en cours...", "downloadSelect": "Sélectionner le fichier journal à télécharger", "nginxConfig": "Configuration des WebSocket pour Nginx", "directAccess": "Si vous accédez via un nom de domaine et que le support WebSocket n'est pas configuré, le visualiseur de journaux ne fonctionnera pas. Dans ce cas, vous pouvez y accéder directement via l'adresse IP et le port du serveur (par exemple : http://votre-ip:9527).", "domainAccess": "Pour accéder via un nom de domaine, il est nécessaire d'ajouter la configuration suivante dans Nginx pour prendre en charge WebSocket :", "buttons": {"pause": "Pause", "resume": "<PERSON><PERSON><PERSON>", "clear": "Vider"}, "errors": {"fetchFailed": "Échec de l'obtention de la liste des fichiers journaux.", "downloadFailed": "Échec du téléchargement du fichier journal.", "wsError": "Erreur de connexion WebSocket"}}, "channelScore": {"score": "Score", "successRate": "taux de réussite", "avgResponseTime": "Temps de réponse moyen", "title": "Score du canal", "hourlyTitle": "Score horaire du canal", "dailyTitle": "Score quotidien par canal", "weeklyTitle": "Score hebdomadaire des canaux", "monthlyTitle": "Score mensuel par canal", "allTimeTitle": "Score global du canal", "infoTooltip": "Le score du canal est une note globale calculée en fonction du taux de réussite et du temps de réponse.", "tableView": "Vue en tableau", "chartView": "Vue graphique", "refresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectModel": "<PERSON>sir un modèle", "allModels": "Tous les modèles", "sortByScore": "Trié par score", "sortBySuccessRate": "Trier par taux de réussite", "sortByResponseTime": "Trier par temps de réponse", "noData": "Au<PERSON>ne donnée disponible.", "totalItems": "Total {{total}} éléments", "fetchError": "Échec de l'obtention des données de score des canaux.", "aboutScoring": "Concernant le calcul des points", "scoringExplanation": "Le score du canal est un score composite calculé en fonction du taux de réussite et du temps de réponse, avec un score maximum de 1.", "successRateWeight": "Poids du taux de réussite (70%)", "successRateExplanation": "Plus le taux de réussite est élevé, plus le score est élevé.", "responseTimeWeight": "Poids du temps de réponse (30%)", "responseTimeExplanation": "Un temps de réponse inférieur à 1000 ms obtient un score parfait, au-delà, des points sont déduits proportionnellement.", "columns": {"rank": "Classement", "channelId": "ID de canal", "channelName": "Nom du canal", "model": "mod<PERSON><PERSON>", "totalRequests": "Nombre total de requêtes", "successRequests": "Nombre de requêtes réussies", "failedRequests": "Nombre de requêtes échouées", "successRate": "taux de réussite", "avgResponseTime": "Temps de réponse moyen", "score": "Score global", "actions": "Opération"}, "actions": {"viewDetails": "Voir les détails", "test": "Canal de test", "edit": "Canal d'édition"}, "tooltips": {"excellent": "excellent", "good": "Bien", "average": "général", "poor": "médiocre", "veryPoor": "très ma<PERSON>"}, "scoringExplanation100": "Le score du canal est une note globale calculée en fonction du taux de réussite et du temps de réponse, avec un score maximum de 100 points."}, "menu": {"channelScores": "Score du canal"}, "relay": {"dispatchOptions": "Options de planification", "preciseWeightCalculation": "Calcul de poids précis", "preciseWeightCalculationTip": "Une fois activé, un algorithme plus précis sera utilisé pour calculer le poids des canaux, ce qui pourrait augmenter la charge CPU.", "channelMetricsEnabled": "Activer les statistiques des indicateurs de canal", "channelMetricsEnabledTip": "Une fois activé, il collectera des indicateurs tels que le taux de succès et le temps de réponse des canaux, afin d'évaluer les performances des canaux. S'il est désactivé, ces données ne seront pas collectées, ce qui peut réduire l'utilisation des ressources système.", "channelScoreRoutingEnabled": "Activer le routage basé sur le score des canaux.", "channelScoreRoutingEnabledTip": "Une fois activé, le système ajustera automatiquement la priorité de répartition des demandes en fonction des performances historiques des canaux, les canaux ayant de meilleures performances recevant une probabilité de répartition des demandes plus élevée.", "globalIgnoreBillingTypeFilteringEnabled": "Text", "globalIgnoreBillingTypeFilteringEnabledTip": "Text", "globalIgnoreFunctionCallFilteringEnabled": "Text", "globalIgnoreFunctionCallFilteringEnabledTip": "Text", "globalIgnoreImageSupportFilteringEnabled": "Text", "globalIgnoreImageSupportFilteringEnabledTip": "Text"}, "dynamicRouter": {"title": "Gestion des routes dynamiques", "reloadRoutes": "Recharger la route", "exportConfig": "Exporter la configuration", "clearConfig": "Réinitialiser la configuration", "importantNotice": "Avertissement important", "reloadLimitation": "1. Le rechargement des routes ne peut mettre à jour que la configuration des routes existantes, sans possibilité d'ajouter ou de supprimer des routes. Pour recharger complètement la structure des routes, veuillez redémarrer l'application.", "exportDescription": "2. L'exportation de la configuration permettra d'exporter la configuration actuelle de la base de données dans le fichier router.json, en filtrant les valeurs nulles et les valeurs nulles.", "clearDescription": "3. Vider la configuration supprimera toutes les configurations de route dynamique dans la base de données, et après le redémarrage de l'application, elles seront rechargées à partir du fichier router.json.", "routeGroups": "groupe de routage", "upstreamConfig": "Configuration en amont", "endpointConfig": "Configuration des points de terminaison", "editRouteGroup": "Modifier le groupe de routage", "editUpstream": "Modifier la configuration en amont", "editEndpoint": "Modifier la configuration des points de terminaison", "editJSON": "Éditer JSON", "confirmClear": "Confirmer la réinitialisation de la configuration.", "confirmClearMessage": "Cette opération va vider toutes les configurations de routes dynamiques dans la base de données. Lors du prochain redémarrage de l'application, elles seront rechargées à partir du fichier de configuration. Voulez-vous vraiment continuer ?", "configCleared": "La configuration de routage dynamique a été réinitialisée, veuillez redémarrer l'application pour appliquer les modifications.", "configExported": "La configuration a été exportée avec succès dans le fichier.", "configReloaded": "La configuration du routeur a été rechargée avec succès."}, "notification": {"title": "paramètres de notification", "subscriptionEvents": "Message", "notificationMethods": "通知方式", "alertSettings": "Message", "emailConfig": "configuration email", "customEmails": "Message", "addEmail": "添加邮箱", "removeEmail": "supprimer", "emailPlaceholder": "Message", "emailTooltip": "Message", "emailDescription": "Message", "balanceThreshold": "Message", "balanceThresholdTooltip": "Message", "balanceThresholdDescription": "Description", "alertExplanationTitle": "预警说明", "alertExplanation": "Message", "selectEvents": "Message", "eventsDescription": "Message", "selectMethods": "Message", "methodsDescription": "Message", "description": "Description", "recommended": "Message", "important": "Message", "testRecommendation": "Message", "testNotification": "测试通知", "testMessage": "Message", "testSuccess": "测试通知发送成功", "testFailed": "Message", "saveSuccess": "Message", "saveFailed": "保存设置échec", "validation": {"invalidEmail": "Message", "emailRequired": "邮箱地址不能为空", "invalidUrl": "Message", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "请输入WxPusher APP Token", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "请输入飞书机器人Webhook URL", "webhookUrlRequired": "Message", "telegramTokenRequired": "请输入Telegram Bot Token", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "Veuillez saisir le Token du Bot Telegram"}, "qywxbotConfig": "Configuration du bot WeChat Enterprise", "qywxbotGuide": "Message", "wxpusherConfig": "WxPusher配置", "wxpusherGuide": "Message", "wxpusherUid": "用户UID", "dingtalkConfig": "Configuration du bot DingTalk", "dingtalkGuide": "Message", "feishuConfig": "Configuration du bot <PERSON><PERSON>u", "feishuGuide": "Message", "webhookConfig": "Webhook配置", "webhookGuide": "Message", "webhookUrl": "调用地址", "webhookToken": "Message", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "Bear<PERSON>（可选）", "telegramConfig": "Configuration du bot Telegram", "telegramGuide": "Message", "telegramChatIdPlaceholder": "********* 或 @username", "events": {"account_balance_low": "Message", "account_quota_expiry": "额度即将过期", "security_alert": "alerte de sécurité", "system_announcement": "Message", "promotional_activity": "notification d'activité promotionnelle", "model_pricing_update": "Message", "anti_loss_contact": "Message"}, "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "当账户额度即将过期时提前通知您", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "模型价格变动和计费规则更新通知", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "通过邮件接收通知消息", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "通过企业微信机器人接收通知", "dingtalk": "Description", "feishu": "通过飞书机器人接收通知"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook通知", "wxpusher": "Message", "qywxbot": "企业微信机器人", "dingtalk": "Message", "feishu": "飞书机器人"}, "configurationSteps": "Étapes de configuration :", "detailedDocumentation": "Documentation détaillée :", "qywxbotConfigurationGuide": "Guide de configuration du bot WeChat Enterprise", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "SaisirWebhook URLdans la configuration ci-dessus", "qywxbotDocumentationLink": "Message", "wxpusherConfiguration": "Configuration Wx<PERSON><PERSON>er", "wxpusherConfigurationGuide": "Guide de configuration WxPusher", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "Message", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Veuillez saisirWxPusher APP Token", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "Visitez le site officiel WxPusher pour créer un compte", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "SaisirAPP Token和用户UIDdans la configuration ci-dessus", "wxpusherOfficialWebsite": "WxPusherSite officiel", "dingtalkConfigurationGuide": "Guide de configuration du bot DingTalk", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "钉钉自定义机器人接入", "feishuConfigurationGuide": "Guide de configuration du bot Feishu", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "Message", "feishuStep4": "Message", "feishuStep5": "复制生成的Webhook URL", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Guide de configuration du bot Telegram", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "Message", "telegramStep5": "Message", "telegramStep6": "Visiter https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "SaisirWebhook URLdans la configuration ci-dessus", "dingtalkNoticeTitle": "Notes importantes :", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "SaisirWebhook URLdans la configuration ci-dessus", "feishuMessageFormatsTitle": "Formats de message pris en charge :", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Notes importantes :", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• 建议启用签名校验以提高安全性", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "SaisirBot Token和Chat IDdans la configuration ci-dessus", "telegramNoticeTitle": "Notes importantes :", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Title", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "Message", "telegramChannelPermission": "Message", "webhookCallUrl": "<PERSON><PERSON><PERSON>'<PERSON>el", "webhookConfigurationGuide": "Guide de configuration Webhook", "webhookDataFormatExample": "Exemple de format de données：", "webhookConfigurationInstructions": "Instructions de configuration：", "webhookRequestMethod": "• Méthode de requête：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• Méthode d'authentification：<PERSON><PERSON>（optionnel, après saisie, ajoutera Authorization: Bearer {token} dans l'en-tête de la requête）", "webhookTimeout": "• D<PERSON><PERSON> d'expiration：30 secondes", "webhookRetryMechanism": "• Mécanisme de nouvelle tentative：Réessaie 2 fois après échec", "webhookTip": "💡 Conseil：Assurez-vous que votre endpoint Webhook peut recevoir des requêtes POST et retourner des codes de statut 2xx", "telegramStep3Detailed": "Configurez le nom du bot et le nom d'utilisateur selon les invites（le nom d'utilisateur doit se terminer par bot）", "telegramPersonalChatDetailed": "• Chat personnel：Envoyez un message au bot, puis accédez", "telegramGroupChatDetailed": "• Chat de groupe：Ajoutez le bot au groupe, après avoir envoyé un message, accédez au même lien", "telegramChannelDetailed": "• Canal：Ajoutez le bot comme administrateur, l'ID de chat commence généralement par -100", "telegramQuickChatIdTitle": "Exemple d'obtention rapide d'ID de chat：", "telegramQuickStep1": "Remplacer BOT_TOKEN：https://api.telegram.org/bot YOUR_BOT_TOKEN /getUpdates", "telegramQuickStep2": "Accédez au lien ci-dessus dans le navigateur", "telegramQuickStep3": "Recherchez dans la réponse JSON：\"chat\":{\"id\":*********}"}, "legal": {"privacyPolicy": {"title": "Title", "lastUpdated": "Time", "sections": {"informationCollection": {"title": "信息收集", "description": "Description", "items": {"accountInfo": "Text", "usageData": "Description", "technicalInfo": "Description"}}, "informationUsage": {"title": "信息使用", "description": "Description", "items": ["提供和维护我们的服务", "Description", "Description", "发送重要的服务通知", "Description"]}, "informationSharing": {"title": "信息共享", "description": "Description", "items": ["Description", "Description", "Description"]}, "dataSecurity": {"title": "数据安全", "description": "Description", "items": ["数据加密传输和存储", "Text", "Text", "员工隐私培训"]}, "dataRetention": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "userRights": {"title": "您的权利", "description": "Description", "items": ["User", "删除您的账户和相关数据", "User", "User"]}, "cookieUsage": {"title": "Title", "description": "Description", "items": ["维持用户会话", "Text", "Text"]}, "thirdPartyServices": {"title": "第三方服务", "description": "Description", "items": ["Text", "GitHub OAuth：用于用户身份验证", "Text"]}, "childrenPrivacy": {"title": "儿童隐私", "description": "Description"}, "policyUpdates": {"title": "Title", "description": "Description"}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text"}}}, "termsOfService": {"title": "Title", "lastUpdated": "Time", "importantNotice": "Message", "sections": {"serviceDescription": {"title": "服务描述", "description": "Description", "items": ["Description", "Description", "使用统计和监控", "Description", "Description"]}, "userAccount": {"title": "用户账户", "description": "Description", "items": ["Text", "User", "User", "及时更新账户信息", "User"]}, "usageRules": {"title": "使用规则", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "prohibitedBehavior": {"title": "禁止行为", "description": "Description", "items": ["尝试未经授权访问système", "Text", "Text", "Text", "Text"]}, "serviceAvailability": {"title": "服务可用性", "description": "Description", "items": ["Text", "Text", "Text", "会提前通知计划内的维护"]}, "feesAndPayment": {"title": "Title", "description": "Description", "items": ["Text", "高级功能可能需要付费", "Text", "Text"]}, "intellectualProperty": {"title": "知识产权", "description": "Description", "items": ["Text", "您获得有限的使用许可", "Text", "Text"]}, "privacyProtection": {"title": "隐私保护", "description": "Description", "items": ["Text", "采取合理措施保护数据安全", "Text"]}, "disclaimer": {"title": "免责声明", "description": "Description", "items": ["Text", "Text", "Text", "Text"]}, "serviceTermination": {"title": "服务终止", "description": "Description", "items": ["您违反这些条款", "Text", "Text", "法律要求"]}, "termsModification": {"title": "Title", "description": "Description", "items": ["重大变更会提前通知", "Text", "Text"]}, "disputeResolution": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text", "serviceHours": "Text"}}}, "common": {"copyright": "Text", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "Text"}}}