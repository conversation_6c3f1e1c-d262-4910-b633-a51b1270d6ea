{"message": {"copyModelSuccess": "¡El nombre del modelo ha sido copiado al portapapeles!", "copyFailed": "La copia falló, por favor copia manualmente.", "logoutSuccess": "Cierre de sesión exitoso.", "loginSuccess": {"default": "Inicio de sesión exitoso", "welcomeBack": "Bienvenido de nuevo."}, "removeLocalStorage": {"confirm": "¿Desea borrar la caché local?", "success": "Cache local eliminado con éxito."}, "loadData": {"error": "Error al cargar los datos de {{name}}."}, "noNotice": "No hay contenido de anuncio disponible.", "verification": {"turnstileChecking": "¡Turnstile está verificando el entorno del usuario!", "pleaseWait": "Por favor, inténtalo de nuevo más tarde."}, "clipboard": {"inviteCodeDetected": "Se ha detectado un código de invitación, ¡se ha rellenado automáticamente!", "clickToCopy": "Haga clic para copiar", "copySuccess": "<PERSON><PERSON> exitosa"}}, "common": {"yes": "sí", "no": "No", "copyAll": "<PERSON><PERSON><PERSON> todo", "all": "Todo", "more": "más", "unlimited": "sin restricciones", "enabled": "Abrir", "disabled": "<PERSON><PERSON><PERSON>", "save": "guardar", "cancel": "<PERSON><PERSON><PERSON>", "create": "crear", "usd": "d<PERSON>lar estadounidense", "day": "{{count}} días", "day_plural": "{{count}} días", "days": "cielo", "seconds": "segundo", "times": "sigu<PERSON>e", "submit": "Enviar", "bind": "vinculación", "unknown": "desconocido", "loading": "Cargando...", "copyFailed": "Copia fallida.", "people": "persona", "ok": "Determinado", "close": "<PERSON><PERSON><PERSON>", "copied": "Copiado.", "expand": "<PERSON><PERSON><PERSON>", "collapse": "Recoger", "none": "ning<PERSON>", "remark": "<PERSON>a", "selectPlaceholder": "Por favor, elige {{name}}.", "on": "abrir", "off": "cierra", "name": "Identificación", "displayName": "Nombre para mostrar", "description": "Descripción", "ratio": "magnificación", "unnamed": "Canal sin nombre", "groups": "grupo", "captchaPlaceholder": "Por favor, introduce el código de verificación.", "confirm": "Confirmar", "permissions": "<PERSON><PERSON><PERSON>", "actions": "Acciones", "createdTime": "Tiempo de creación", "expiredTime": "Tiempo de expiración", "search": "Buscar", "reset": "Restablecer", "refresh": "Actualizar", "pagination": {"total": "Total {{total}} elementos"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "<PERSON>z clic para abrir el enlace."}, "userRole": {"normal": "usuario común", "agent": "agente", "admin": "Administrador", "superAdmin": "superadministrador", "loading": "Cargando..."}, "channelStatus": {"enabled": "Activar", "disabled": "Deshabilitar", "waitingRestart": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "waiting": "esperar", "autoStoppedTitle": "El reintento automático del canal ha superado el número máximo de intentos o ha activado las condiciones de desactivación automática.", "stopped": "Desactivar", "partiallyDisabled": "Parte deshabilitada", "unknown": "desconocido", "reason": "razón"}, "channelBillingTypes": {"payAsYouGo": "facturación por consumo", "payPerRequest": "cobro por uso", "unknown": "modo desconocido"}, "tokenStatus": {"normal": "Normal", "disabled": "Deshabilitar", "expired": "caducado", "exhausted": "<PERSON>tado", "unknown": "desconocido"}, "userStatus": {"normal": "Normal", "banned": "prohibición", "unknown": "desconocido"}, "redemptionStatus": {"normal": "normal", "disabled": "Deshabilitar", "redeemed": "<PERSON><PERSON><PERSON>", "expired": "caducado", "unknown": "desconocido"}, "duration": {"request": "Solicitud", "firstByte": "primer byte", "total": "Total", "seconds": "segundo", "lessThanOneSecond": "<1 segundo"}, "streamType": {"stream": "Fluido", "nonStream": "No fluido"}, "noSet": {"title": "El administrador no ha configurado {{name}}.", "name": {"about": "Acerca de", "chat": "Diálogo"}}, "buttonText": {"add": "Nuevo", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "delete": "Eliminar", "edit": "editar", "save": "guardar", "updateBalance": "<PERSON><PERSON><PERSON><PERSON> saldo", "test": "p<PERSON><PERSON>", "multiple": "Sele<PERSON><PERSON> múl<PERSON>"}, "channelPage": {"title": "Gestión de canales"}, "channelStatusCount": {"title": "Estadísticas del estado del canal", "summary": "Habilitado {{enabled}} | Deshabilitado {{disabled}} | Reintentando {{retry}} | Detenido {{stopped}}", "statusEnabled": "Habilitado", "statusDisabled": "Desactivado", "statusRetry": "Reintentando", "statusStopped": "Detenido", "statusPartially": "Parcialmente deshabilitado"}, "header": {"routes": {"status": "estado", "home": "<PERSON><PERSON><PERSON><PERSON> principal", "chat": "Diálogo", "pptGen": "Generación de PPT", "chart": "Estadística", "agency": "agente", "channel": "canal", "ability": "capacidad de canal", "channelGroup": "Grupo de canales", "token": "token", "log": "registro", "logDetail": "de<PERSON>les", "midjourney": "dibujo", "user": "usuario", "config": "Configuración", "packagePlanAdmin": "paquete", "redemption": "código de canje", "group": "grupo", "query": "consulta", "about": "Acerca de", "setting": {"default": "Configuración", "operation": "Configuración de operaciones", "system": "Configuración del sistema", "global": "Configuración global", "advance": "Configuración de características", "sensitive": "Configuración de palabras sensibles", "verification": "Configuración del código de verificación", "update": "Comprobar actualizaciones"}, "account": {"default": "cuenta", "profile": "Centro personal", "cardTopup": "Intercambio de códigos de tarjeta", "onlineTopup": "Recarga en línea", "recharge": "Recarga de saldo", "balanceTransfer": "Transferencia de saldo", "pricing": "Descripción de costos", "packagePlan": {"list": "Compra de paque<PERSON>", "record": "Registro de compras"}, "notificationSettings": "Configuración de notificaciones"}, "tools": {"default": "<PERSON><PERSON><PERSON><PERSON>", "fileUpload": "Subida de archivos", "keyExtraction": "Extracción de clave", "multiplierCalculator": "calculadora de multiplicación", "shortLink": "Generación de enlaces cortos", "testConnection": "Prueba de acceso", "customPrompts": "Gestión de palabras clave", "redis": "Visualización de Redis", "ratioCompare": "Comparación de tasas.", "serverLog": "Visor de registros del servidor"}, "onlineTopupRecord": "Registro de recarga", "channelScores": "Puntuación del canal", "dynamicRouter": "rutas <PERSON>", "task": "<PERSON><PERSON><PERSON>", "agencyJoin": "Asociación de agencia"}, "dropdownMenu": {"profile": "Centro personal", "recharge": "Recarga de saldo", "agencyCenter": "Centro de Agentes", "checkin": "Registro", "darkMode": {"enable": "modo oscuro", "disable": "<PERSON><PERSON>o"}, "fullScreen": {"default": "Cambiar a pantalla completa", "enable": "Modo de pantalla completa", "disable": "Salir de pantalla completa"}, "logout": "<PERSON><PERSON><PERSON>"}, "checkin": {"default": "Registro", "success": "<PERSON><PERSON>oso", "failed": "Error al registrarse", "verification": "Por favor, complete la verificación."}, "avatarProps": {"login": "In<PERSON><PERSON>"}}, "settings": {"public": {"titles": {"default": "Configuración pública"}, "SystemName": "Nombre del sistema", "ServerAddress": "Dirección de servicio", "TopUpLink": "Enlace de recarga", "ChatLink": "Enlace de diálogo", "Logo": "Logo del sistema", "HomePageContent": "Contenido de la página principal", "About": "Sobre el contenido", "Notice": "Contenido del anuncio", "Footer": "Contenido del pie de página", "RegisterInfo": "Notificación de registro", "HeaderScript": "Encabezado personalizado", "SiteDescription": "Descripción del sitio", "PrivacyPolicy": "Política de privacidad", "ServiceAgreement": "Acuerdo de servicio", "FloatButton": {"FloatButtonEnabled": "Abrir", "DocumentInfo": "Información del documento", "WechatInfo": "Información de WeChat", "QqInfo": "Información de QQ"}, "CustomThemeConfig": "<PERSON><PERSON> personalizado", "AppList": "Enlaces de amistad"}}, "home": {"default": {"title": "Bienvenido a usar", "subtitle": "Desarrollo secundario basado en One API, proporcionando funciones más completas.", "start": "Comenzar a usar", "description": {"title": "Nuevas características:", "part1": "Interfaz de usuario completamente nueva, conveniente y rápida.", "part2": "Optimizar el mecanismo de programación, eficiente y estable.", "part3": "Desarrollado para empresas, seguro y confiable.", "part4": "Más funciones avanzadas, te están esperando para que las descubras."}}}, "dailyUsageChart": {"title": "Uso diario del modelo", "yAxisName": "Uso (USD)", "loadingTip": "Uso diario", "fetchError": "Error al obtener los datos de uso diario:"}, "modelUsageChart": {"title": "Uso del modelo", "hourlyTitle": "Uso del modelo por hora", "dailyTitle": "Uso diario del modelo", "weeklyTitle": "Uso del modelo semanalmente", "monthlyTitle": "Uso del modelo cada mes"}, "granularity": {"hour": "cada hora", "day": "todos los días", "week": "cada semana", "month": "cada mes", "all": "Todo"}, "abilitiesTable": {"title": "capacidad de canal", "export": "Exportar", "group": "Grupo", "model": "modelo", "channelId": "Número de canal", "enabled": "Habilitado", "weight": "peso", "priority": "prioridad", "billingType": "Tipo de facturación", "functionCallEnabled": "Activación de llamadas a funciones", "imageSupported": "Soporte de imagen", "yes": "sí", "no": "No", "perToken": "Facturación por token", "perRequest": "Facturación por solicitud", "noDataToExport": "No hay datos que se puedan exportar.", "exportConfirm": "¿Está seguro de que desea exportar los datos de la página actual?", "exportSuccess": "Exportación exitosa", "toggleSuccess": "Cambio exitoso", "toggleError": "Cambio fallido", "selectOrInputGroup": "Seleccionar o ingresar un grupo de usuarios"}, "logsTable": {"retry": "Reintentar", "retryChannelList": "Lista de canales de reintento", "retryDurations": "Detalles del tiempo de reintento", "channel": "canal", "duration": "tiempo consumido", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "retryCount": "Número de reintentos", "retryDetails": "Detalles de reintento", "totalRetryTime": "Tiempo total de reintento", "seconds": "segundo", "tokenGroup": "Grupo de tokens", "selectGroup": "Seleccionar grupo", "dailyModelUsageStats": "Resumen de llamadas de datos", "time": "Tiempo", "moreInfo": "Más información", "ip": "IP", "remoteIp": "IP remoto", "ipTooltip": "IP: {{ip}}  \nIP remoto: {{remoteIp}}", "requestId": "Solicitud de ID", "username": "nombre de usuario", "userId": "ID de usuario", "tokenName": "Nombre del token", "token": "token", "type": "tipo", "typeUnknown": "desconocido", "type充值": "recarga", "type消费": "consumo", "type管理": "gestión", "type系统": "sistema", "type邀请": "Invitación", "type提示": "sugerencia", "type警告": "Advertencia", "type错误": "error", "type签到": "Registro", "type日志": "registro", "type退款": "reembolso", "type邀请奖励金划转": "Transferencia de bonificación por invitación", "type代理奖励": "Recompensa por intermediación", "type下游错误": "Error en la parte inferior.", "type测试渠道": "Canal de prueba", "typeRecharge": "recargar", "typeConsumption": "consumo", "typeManagement": "gestión", "typeSystem": "sistema", "typeInvitation": "Invitación", "typePrompt": "sugerencia", "typeWarning": "Advertencia", "typeError": "error", "typeCheckin": "Registro", "typeLog": "registro", "typeRefund": "reembolso", "typeInviteReward": "Transferencia de bonificación por invitación", "typeAgencyBonus": "Recompensa por intermediación", "typeDownstreamError": "Error de downstream", "typeChannelTest": "Canal de prueba", "channelId": "ID de canal", "channelName": "Nombre del canal", "model": "modelo", "modelPlaceholder": "Ingrese/seleccione el nombre del modelo.", "info": "Información", "isStream": "Fluido", "isStreamPlaceholder": "Entrada/selección de si es en streaming.", "prompt": "sugerencia", "completion": "completar", "consumption": "consumo", "consumptionRange": "Rango de límite de consumo", "description": "Descripción", "action": "Operación", "details": "de<PERSON>les", "tokenKey": "clave de token", "requestDuration": "Tiempo de solicitud", "firstByteDuration": "Tiempo de espera del primer byte", "totalDuration": "Tiempo total consumido", "lessThanOneSecond": "<1 segundo", "modelInvocation": "Llamada al modelo", "modelUsage": "Uso del modelo", "totalQuota": "Límite total de consumo: {{quota}}", "totalRpm": "Número de solicitudes/minuto: {{rpm}}", "totalTpm": "Número de tokens por minuto: {{tpm}}", "totalMpm": "Cantidad/minuto: {{mpm}}", "dailyEstimate": "Consumo diario estimado: {{estimate}}", "currentStats": "RPM actual: {{rpm}} TPM actual: {{tpm}} MPM actual: ${{mpm}} Estimación diaria de consumo: ${{dailyEstimate}}", "statsTooltip": "Solo se contabilizan los registros no archivados. RPM: número de solicitudes por minuto, TPM: número de tokens por minuto, MPM: dinero consumido por minuto, el consumo diario estimado se deduce a partir del MPM actual.", "showAll": "<PERSON><PERSON> todo", "exportConfirm": "¿Exportar el registro de esta página?", "export": "Exportar", "statsData": "Datos estadís<PERSON>", "today": "el mismo día", "lastHour": "1 hora", "last3Hours": "3 horas", "lastDay": "1 día", "last3Days": "3 días", "last7Days": "7 días", "lastMonth": "1 mes", "last3Months": "3 meses", "excludeModels": "Modelo de exclusión", "selectModelsToExclude": "Selecciona el modelo que deseas excluir.", "excludeErrorCodes": "Excluir códigos de error", "excludeErrorCodesPlaceholder": "Seleccione los códigos de error que desea excluir.", "errorCode": "c<PERSON><PERSON> de error", "errorCodePlaceholder": "Código de error de entrada/selección", "timezoneTip": "Zona horaria actual: {timezone}", "timezoneNote": "Sugerencia de zona horaria", "timezoneDescription": "Los datos estadísticos se agrupan por fecha según su zona horaria actual. Las diferentes zonas horarias pueden resultar en períodos de tiempo de agrupación de datos distintos. Si necesita ajustar esto, diríjase al centro personal para modificar la configuración de la zona horaria.", "goToProfile": "Ir al centro personal", "realtimeQuota": "Time", "viewTotalQuota": "Ver el consumo total", "viewTotalQuotaTip": "Ver el monto total de consumo histórico (la consulta puede tardar unos segundos).", "loadingTotalQuota": "Consultando el monto total de consumo, por favor espere...", "totalQuotaTitle": "Estadísticas históricas de consumo total", "loadTotalQuotaError": "Error al obtener el monto total de consumo.", "requestLogs": "Registro de solicitud - {{requestId}}", "noRequestLogs": "No hay registros de solicitudes.", "metricsExplanation": "Solo se contabilizan los registros no archivados. RPM: número de solicitudes por minuto, TPM: número de tokens por minuto, MPM: dinero consumido por minuto, el consumo diario estimado se deduce a partir del MPM actual.", "autoRefresh": "Actualización automática", "autoRefreshTip": "Haga clic para activar/desactivar la actualización automática; al activarla, los datos se actualizarán automáticamente cada ciertos segundos.", "autoRefreshOn": "Se ha activado la actualización automática.", "autoRefreshOff": "Se ha desactivado la actualización automática.", "refreshInterval": "Intervalo de actualización", "stopRefresh": "Detener la actualización.", "secondsWithValue": "{{seconds}} segundos", "minutesWithValue": "{{minutes}} minutos"}, "mjLogs": {"logId": "ID de registro", "submitTime": "<PERSON><PERSON>", "type": "tipo", "channelId": "ID de canal", "userId": "ID de usuario", "taskId": "ID de tarea", "submit": "Enviar", "status": "estado", "progress": "progreso", "duration": "tiempo consumido", "result": "resultado", "prompt": "Indicación", "promptEn": "IndicaciónEn", "failReason": "Razones del fracaso", "startTime": "Hora de inicio", "endTime": "Hora de finalización", "today": "el mismo día", "lastHour": "1 hora", "last3Hours": "3 horas", "lastDay": "1 día", "last3Days": "3 días", "last7Days": "7 días", "lastMonth": "1 mes", "last3Months": "3 meses", "selectTaskType": "Seleccionar tipo de tarea", "selectSubmitStatus": "Seleccionar el estado de envío", "submitSuccess": "<PERSON><PERSON><PERSON>", "queueing": "Están haciendo fila.", "duplicateSubmit": "<PERSON><PERSON><PERSON> duplica<PERSON>", "selectTaskStatus": "Seleccionar el estado de la tarea", "success": "éxito", "waiting": "esperar", "queued": "hacer fila", "executing": "<PERSON><PERSON><PERSON><PERSON>", "failed": "fracaso", "seconds": "segundo", "unknown": "desconocido", "viewImage": "Haz clic para ver", "markdownFormat": "Formato Markdown", "midjourneyTaskId": "ID de tarea de Midjourney", "copiedAsMarkdown": "Copiado en formato Markdown.", "copyFailed": "Copia fallida.", "copiedMidjourneyTaskId": "ID de tarea de Midjourney copiado.", "drawingLogs": "Registro de dibujo", "onlyUnarchived": "Solo contabilizar los registros no archivados.", "imagePreview": "Vista previa de la imagen", "copiedImageUrl": "La dirección de la imagen ha sido copiada.", "copy": "copiar", "download": "<PERSON><PERSON><PERSON>", "resultImage": "Imagen de resultados", "downloadError": "Error al descargar la imagen.", "mode": "modo", "selectMode": "Se<PERSON>ccionar modo", "relax": "<PERSON><PERSON>", "fast": "<PERSON><PERSON>", "turbo": "<PERSON><PERSON>", "actions": "Operación", "refresh": "Actualizar", "refreshSuccess": "La actualización del estado de la tarea fue exitosa.", "refreshFailed": "Error al actualizar el estado de la tarea.", "refreshError": "Se produjo un error al actualizar el estado de la tarea.", "tasks": {"title": "Lista de tareas", "taskId": "ID de tarea", "platform": "Plataforma", "type": "Tipo", "status": "Estado", "progress": "Progreso", "submitTime": "Tiempo de envío", "startTime": "Tiempo de inicio", "endTime": "Tiempo de finalización", "duration": "Duración", "result": "<PERSON><PERSON><PERSON><PERSON>", "taskIdPlaceholder": "Ingrese ID de tarea", "platformPlaceholder": "Seleccione plataforma", "typePlaceholder": "Seleccione tipo", "statusPlaceholder": "Seleccione estado", "videoGeneration": "Generación de video", "imageGeneration": "Generación de imagen", "musicGeneration": "Generación de música", "textGeneration": "Generación de texto", "unknown": "Desconocido", "success": "Éxito", "failed": "Fallido", "inProgress": "En progreso", "submitted": "Enviado", "queued": "En cola", "notStarted": "No iniciado", "viewResult": "Ver resultado", "viewError": "Ver error", "taskDetails": "Detalles de tarea", "errorDetails": "Detalles del error", "loadError": "<PERSON><PERSON>r de carga"}, "viewVideo": "Ver video", "videoPreview": "Vista previa del video", "copyVideoUrl": "Copiar URL del video", "copiedVideoUrl": "URL del video copiada", "downloadVideo": "Des<PERSON><PERSON> video", "videoNotSupported": "Video no soportado", "videoUrl": "URL del video", "videoUrls": "URLs del video"}, "mjTaskType": {"IMAGINE": "Generar imagen", "UPSCALE": "ampliar", "VARIATION": "transformación", "REROLL": "<PERSON><PERSON><PERSON>", "DESCRIBE": "Imagen a texto", "BLEND": "mezcla de imágenes", "OUTPAINT": "zoom", "DEFAULT": "desconocido"}, "mjCode": {"submitSuccess": "<PERSON><PERSON><PERSON>", "queueing": "Están haciendo fila.", "duplicateSubmit": "<PERSON><PERSON><PERSON> duplica<PERSON>", "unknown": "desconocido"}, "mjStatus": {"success": "éxito", "waiting": "esperar", "queued": "hacer fila", "executing": "<PERSON><PERSON><PERSON><PERSON>", "failed": "fracaso", "unknown": "desconocido"}, "tokensTable": {"title": "Gestión de tokens", "table": {"title": "Gestión de tokens", "toolBar": {"add": "Crear un nuevo token", "delete": "Eliminar token", "deleteConfirm": "Se están eliminando en masa {{count}} tokens, esta operación no se puede deshacer.", "export": "Exportar", "exportConfirm": "¿Exportar el token de la página actual?"}, "action": "Operación"}, "modal": {"title": {"add": "Crear nuevo token", "edit": "Editar token"}, "field": {"name": "Nombre del token", "description": "Descripción del token", "type": {"default": "Método de facturación", "type1": "facturación por consumo", "type2": "cobro por uso", "type3": "facturación mixta", "type4": "Prioridad por cantidad", "type5": "Por orden de prioridad secundaria."}, "status": "estado", "statusEnabled": "normal", "statusDisabled": "Deshabilitar", "statusExpired": "caducado", "statusExhausted": "<PERSON>tado", "models": "Modelos disponibles", "usedQuota": "Límite de consumo", "remainQuota": "<PERSON><PERSON> restante", "createdTime": "Fecha de creación", "expiredTime": "<PERSON><PERSON> de caducidad", "all": "Todo", "more": "más", "notEnabled": "No activado", "unlimited": "sin restricciones", "daysLeft": "<PERSON>pirar<PERSON> en {{days}} días.", "expired": "Ha expirado hace {{days}} días.", "userId": "ID de usuario", "key": "Clave API", "neverExpire": "Nunca caduca"}, "delete": {"title": "Eliminar", "content": "¿Está seguro de que desea eliminar la clave API {{name}}?"}, "footer": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "update": "actualización"}, "bridge": {"title": "Conexión rápida de canales", "placeholder": "Por favor, introduce tu dirección de servicio {{name}}."}, "copy": {"title": "copia manualmente"}}, "dropdown": {"onlineChat": "Diálogo en línea", "disableToken": "Desactivar el token", "enableToken": "Activar el token", "editToken": "Editar token", "requestExample": "<PERSON>je<PERSON><PERSON> de solicitud", "tokenLog": "Registro de tokens", "shareToken": "Compartir token", "quickIntegration": "Conexión con un solo clic"}, "error": {"fetchModelsFailed": "Error al obtener el modelo: {{message}}", "batchDeleteFailed": "Error al eliminar en masa: {{message}}", "deleteTokenFailed": "Error al eliminar el token: {{message}}", "refreshTokenFailed": "Error al actualizar el token: {{message}}", "enableTokenFailed": "Error al habilitar el token: {{message}}", "disableTokenFailed": "Error al deshabilitar el token: {{message}}", "fetchDataFailed": "Error al obtener datos: {{message}}"}, "success": {"batchDelete": "Se eliminaron con éxito {{count}} tokens.", "shareTextCopied": "El texto compartido ha sido copiado al portapapeles.", "tokenCopied": "El token ha sido copiado al portapapeles.", "deleteToken": "Token eliminado con éxito.", "refreshToken": "Token de actualización exitoso.", "enableToken": "Token activado con éxito.", "disableToken": "Desactivación de token exitosa.", "export": "Exportación del token de la página actual exitosa."}, "warning": {"copyFailed": "La copia falló, por favor copia manualmente.", "invalidServerAddress": "Por favor, ingrese la dirección del servidor correcta."}, "info": {"openingBridgePage": "Se está abriendo la página de conexión, ya he copiado el token para usted."}, "export": {"name": "Nombre", "key": "clave", "billingType": "Método de facturación", "status": "estado", "models": "Modelos disponibles", "usedQuota": "Límite de consumo", "remainQuota": "<PERSON><PERSON> restante", "createdTime": "Fecha de creación", "expiredTime": "<PERSON><PERSON> de caducidad", "unlimited": "sin restricciones", "neverExpire": "Nunca caduca"}, "billingType": {"1": "facturación por consumo", "2": "cobro por uso", "3": "facturación mixta", "4": "Prioridad por cantidad", "5": "Por orden de prioridad secundaria."}, "bridge": {"quickIntegration": "Conexión con un solo clic"}}, "editTokenModal": {"editTitle": "Editar token", "createTitle": "Crear un token", "defaultTokenName": "El token de {{username}} {{date}}", "tokenName": "Nombre del token", "unlimitedQuota": "límite ilimitado", "remainingQuota": "<PERSON><PERSON> restante", "authorizedQuota": "Límite de autorización", "quotaLimitNote": "El límite máximo de disponibilidad del token está restringido por el saldo de la cuenta.", "quickOptions": "Opciones rápidas", "neverExpire": "Nunca caduca", "expiryTime": "<PERSON><PERSON> de caducidad", "billingMode": "Modelo de facturación", "selectGroup": "Seleccionar grupo", "switchGroup": "Seleccionar grupo", "switchGroupTooltip": "Seleccione el grupo al que pertenece el token; diferentes grupos tienen diferentes precios y permisos de función. Si no se selecciona, se utilizará el grupo al que pertenece el usuario actual por defecto.", "switchGroupHint": "La selección del grupo afectará el multiplicador de facturación de los tokens y los modelos disponibles, así que elija según sus necesidades reales.", "importantFeature": "Importante", "tokenRemark": "Nota del <PERSON>", "discordProxy": "Proxy de <PERSON>rd", "enableAdvancedOptions": "Habilitar opciones avanzadas", "generationAmount": "Cantidad generada", "availableModels": "Modelos disponibles", "selectModels": "Seleccionar/buscar/agregar modelos disponibles, dejar en blanco indica sin restricciones.", "activateOnFirstUse": "Activación por primera vez", "activateOnFirstUseTooltip": "Si se activa esta opción y se activa mediante el primer uso, se sobrescribirá el período de validez del token configurado anteriormente.", "activationValidPeriod": "Periodo de validez de activación", "activationValidPeriodTooltip": "El período de validez del token activado después del primer uso (unidad: días)", "ipWhitelist": "Lista blanca de IP", "ipWhitelistPlaceholder": "Dirección IP (rango), soporta IPV4 e IPV6, múltiples separadas por comas.", "rateLimiter": "limitador de corriente", "rateLimitPeriod": "período de limitación de flujo", "rateLimitPeriodTooltip": "Período de limitación de flujo (unidad: segundos)", "rateLimitCount": "Límite de veces.", "rateLimitCountTooltip": "Número de veces disponibles durante el período de limitación.", "promptMessage": "Mensaje de aviso", "promptMessageTooltip": "Mensaje de advertencia cuando se supera el límite de flujo.", "promotionPosition": "Ubicación de promoción", "promotionPositionStart": "<PERSON><PERSON>o", "promotionPositionEnd": "finalización", "promotionPositionRandom": "aleatorio", "promotionContent": "Contenido promocional", "currentGroup": "Grupo actual", "searchGroupPlaceholder": "Buscar nombre del grupo, descripción o multiplicador...", "mjTranslateConfig": "Configuración de traducción de MJ", "mjTranslateConfigTip": "Configuración de traducción que solo es efectiva para las palabras clave de Midjourney.", "mjTranslateBaseUrlPlaceholder": "Por favor, introduzca la URL base del servicio de traducción.", "mjTranslateApiKeyPlaceholder": "Por favor, ingrese la clave API del servicio de traducción.", "mjTranslateModelPlaceholder": "Por favor, introduzca el nombre del modelo utilizado para el servicio de traducción.", "mjTranslateBaseUrlRequired": "Se debe proporcionar una URL base al habilitar la traducción.", "mjTranslateApiKeyRequired": "Se debe proporcionar una clave API al habilitar la traducción.", "mjTranslateModelRequired": "Se debe proporcionar el nombre del modelo al activar la traducción."}, "addTokenQuotaModal": {"title": "Gestión del saldo de tokens {{username}}", "defaultReason": "Operación del administrador", "enterRechargeAmount": "Por favor, introduzca el monto de recarga.", "enterRemark": "Por favor, ingresa un mensaje de nota.", "confirmOperation": "Confirmar operación", "confirmContent": "¿Confirmar {{username}} {{action}} {{amount}} dólares {{updateExpiry}}?", "recharge": "recargar", "deduct": "deducción", "andUpdateExpiry": "y actualizar la validez del saldo a {{days}} días.", "alertMessage": "Ingresar un número negativo puede reducir el saldo del usuario.", "rechargeAmount": "Límite de recarga", "operationReason": "Razón de operación", "finalBalance": "Saldo final"}, "billingType": {"1": "facturación por consumo", "2": "cobro por uso", "3": "facturación mixta", "4": "Prioridad por cantidad", "5": "Por orden de prioridad secundaria.", "payAsYouGo": "facturación por consumo", "payPerRequest": "cobro por uso", "hybrid": "facturación mixta", "payAsYouGoPriority": "Prioridad por cantidad", "payPerRequestPriority": "Por orden de prioridad secundaria.", "unknown": "modo desconocido"}, "packagePlanAdmin": {"title": "paquete", "table": {"title": "Gestión de paquetes", "toolBar": {"add": "Nuevo paquete", "delete": "Eliminar paquete"}, "action": {"edit": "editar", "delete": "Eliminar", "detail": "de<PERSON>les", "recovery": "Listar", "offline": "Retirar del mercado"}}, "modal": {"title": {"add": "Nuevo paquete", "edit": "<PERSON><PERSON>"}, "field": {"name": "Nombre del paquete", "type": {"default": "<PERSON><PERSON><PERSON> <PERSON>", "type1": "Paquete de límite", "type2": "paquete por sesiones", "type3": "Paquete de duración"}, "group": "Grupo de paquetes", "description": "Descripción del paquete", "price": "Precio del paquete", "valid_period": "<PERSON><PERSON>", "first_buy_discount": "Descuento para primera compra", "rate_limit_num": "límite de veces", "rate_limit_duration": "período de restricción", "inventory": "Inventario de paquetes", "available_models": "Modelos disponibles", "quota": "Límite del paquete", "times": "Númer<PERSON>"}, "footer": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "update": "actualización"}}}, "login": {"title": "In<PERSON><PERSON>", "username": "nombre de usuario", "password": "contraseña", "login": "In<PERSON><PERSON>", "otherLoginMethods": "Otros métodos de inicio de sesión", "register": "Registrar una cuenta", "accountLogin": "Inicio de sesión de cuenta", "phoneLogin": "Inicio de sesión con número de teléfono móvil", "usernamePlaceholder": "nombre de usuario", "usernameRequired": "¡Por favor, introduce el nombre de usuario!", "passwordPlaceholder": "contraseña", "passwordRequired": "¡Por favor, introduce la contraseña!", "passwordMaxLength": "¡La longitud de la contraseña no puede exceder de 20 caracteres!", "phonePlaceholder": "Número de teléfono móvil", "phoneRequired": "¡Por favor, introduce el número de teléfono!", "phoneFormatError": "¡El formato del número de teléfono es incorrecto!", "smsCodePlaceholder": "código de verificación por SMS", "smsCodeCountdown": "Reintentar en {{count}} segundos.", "getSmsCode": "Obtener código de verificación", "agreementText": "Estoy de acuerdo.", "privacyPolicy": "Política de privacidad", "and": "y", "serviceAgreement": "\"Acuerdo de Servicio\"", "alreadyLoggedIn": "Has iniciado sesión.", "weakPasswordWarning": "¡Su contraseña es demasiado simple, por favor modifíquela a tiempo!", "welcomeMessage": "Bienvenido a usar.", "captchaError": "Código de verificación incorrecto.", "credentialsError": "Nombre de usuario o contraseña incorrectos.", "resetPassword": "Restablecer contraseña", "captchaExpired": "El código de verificación no existe o ha expirado.", "loginFailed": "Error de inicio de sesión: {{message}}", "captchaRequired": "¡Por favor, introduce el código de verificación!", "captchaPlaceholder": "código de verificación", "smsSent": "El código de verificación por SMS se ha enviado con éxito.", "smsSendFailed": "El envío del código de verificación por SMS ha fallado.", "agreementWarning": "Por favor, acepte primero la \"Política de Privacidad\" y el \"Acuerdo de Servicio\".", "turnstileWarning": "Por favor, inténtalo de nuevo más tarde, ¡Turnstile está verificando el entorno del usuario!", "loginSuccess": "Inicio de sesión exitoso"}, "register": {"title": "Registro", "usernameRequired": "¡Por favor, introduce el nombre de usuario!", "usernameNoAt": "El nombre de usuario no puede contener el símbolo @.", "usernameNoChinese": "El nombre de usuario no puede contener caracteres chinos.", "usernameLength": "La longitud del nombre de usuario debe ser de 4 a 12 caracteres.", "usernamePlaceholder": "nombre de usuario", "passwordRequired": "¡Por favor, introduce la contraseña!", "passwordLength": "La longitud de la contraseña debe ser de 8 a 20 caracteres.", "passwordPlaceholder": "contraseña", "confirmPasswordRequired": "¡Por favor, confirme la contraseña!", "passwordMismatch": "¡Las contraseñas ingresadas no coinciden!", "confirmPasswordPlaceholder": "Confirmar con<PERSON>", "emailInvalid": "¡Por favor, introduce una dirección de correo electrónico válida!", "emailRequired": "¡Por favor, introduce tu correo electrónico!", "emailPlaceholder": "dirección de correo electrónico", "emailCodeRequired": "¡Por favor, introduce el código de verificación del correo electrónico!", "emailCodePlaceholder": "código de verificación del correo electrónico", "enterCaptcha": "Por favor, introduce el código de verificación.", "resendEmailCode": "Reenviar en {{seconds}} segundos.", "getEmailCode": "Obtener código de verificación", "phoneRequired": "¡Por favor, introduce tu número de teléfono!", "phoneInvalid": "¡El formato del número de teléfono es incorrecto!", "phonePlaceholder": "número de teléfono móvil", "smsCodeRequired": "¡Por favor, introduce el código de verificación por SMS!", "smsCodePlaceholder": "código de verificación por SMS", "resendSmsCode": "Reenviar en {{seconds}} segundos.", "getSmsCode": "Obtener código de verificación", "captchaRequired": "¡Por favor, introduce el código de verificación!", "captchaPlaceholder": "código de verificación", "inviteCodePlaceholder": "Código de invitación (opcional)", "submit": "Registro", "successMessage": "<PERSON><PERSON>oso", "failMessage": "Registro fallido", "emailCodeSent": "El código de verificación del correo electrónico ha sido enviado.", "smsCodeSent": "El código de verificación por SMS ha sido enviado.", "confirm": "Confirmar", "emailVerifyTitle": "Verificación de correo electrónico", "smsVerifyTitle": "Verificación por SMS", "registerVerifyTitle": "Verificación de registro"}, "profile": {"timezone": "zona horaria", "phoneNumber": "número de teléfono móvil", "emailAddress": "dirección de correo electrónico", "wechatAccount": "Cuenta de WeChat", "telegramAccount": "Cuenta de Telegram", "bindTelegram": "Vincular Telegram", "balanceValidPeriod": "<PERSON><PERSON> validez del saldo", "lastLoginIP": "Última IP de inicio de sesión", "lastLoginTime": "Última hora de inicio de sesión", "inviteCode": "código de invitación", "inviteLink": "Enlace de invitación", "generate": "generar", "pendingEarnings": "Ingresos pendientes de uso", "transfer": "transferencia", "totalEarnings": "ingresos totales", "accountBalance": "Saldo de la cuenta", "totalConsumption": "consumo acumulado", "callCount": "Número de ll<PERSON>das", "invitedUsers": "Invitar a los usuarios", "promotionInfo": "Información promocional", "inviteDescription": "Una invitación, reembolso de por vida; cuantas más invitaciones, más reembolso.", "userInfo": "Información del usuario", "availableModels": "Modelos disponibles", "modelNameCopied": "Nombre del modelo copiado", "noAvailableModels": "No hay modelos disponibles.", "accountOptions": "Opciones de cuenta", "changePassword": "Modificar contraseña", "systemToken": "token del sistema", "unsubscribe": "cancelar", "educationCertification": "certificación educativa", "timezoneUpdateSuccess": "Actualización de la zona horaria exitosa.", "inviteLinkCopied": "El enlace de invitación ha sido copiado.", "inviteLinkCopyFailed": "Error al copiar el enlace de invitación.", "inviteLinkGenerationFailed": "Error al generar el enlace de invitación.", "allModelsCopied": "Todos los modelos se han copiado al portapapeles.", "copyAllModels": "Copiar todos los modelos.", "totalModels": "Número de modelos disponibles", "expired": "<PERSON><PERSON><PERSON><PERSON>", "validPeriod": "validez", "longTermValid": "Válido a largo plazo.", "failedToLoadModels": "Error al cargar la lista de modelos.", "accessTokens": "Tokens de acceso", "accessTokensManagement": "Gestión de tokens de acceso", "accessTokenDescription": "Los tokens de acceso permiten a aplicaciones externas acceder a tu cuenta", "tokenNameLabel": "Nombre del token", "tokenNamePlaceholder": "Ingresa el nombre del token", "presetPermissions": "Permisos preestablecidos", "detailPermissions": "<PERSON><PERSON><PERSON>", "validityPeriod": "<PERSON><PERSON><PERSON>", "validityPeriodExtra": "0 significa que nunca expira", "remarkLabel": "Observación", "remarkPlaceholder": "Por favor ingresa...", "createNewToken": "crear nuevo token", "tokenCreatedSuccess": "token de acceso creado exitosamente", "tokenSavePrompt": "Guarda el token en un lugar seguro", "copyToken": "Copiar token", "readPermission": "Permiso de lectura", "writePermission": "Permiso de escritura", "deletePermission": "Permiso de eliminación", "tokenManagement": "Gestión de tokens", "channelManagement": "Gestión de canales", "logView": "Vista de registros", "statisticsView": "Vista de estadísticas", "userManagement": "Gestión de usuarios", "quotaManagement": "Gestión de cuotas", "readOnlyPermission": "Text", "writeOnlyPermission": "只写权限", "readWritePermission": "Text", "standardPermission": "标准权限", "fullPermission": "Text", "selectPermission": "Option", "tokenStatus": "状态", "tokenEnabled": "habilitar", "tokenDisabled": "Text", "enableToken": "habilitar", "disableToken": "Text", "deleteToken": "eliminar", "deleteTokenConfirm": "Text", "disableTokenConfirm": "Action", "enableTokenConfirm": "Action", "tokenExpiryNever": "nunca expira", "accessTokensInfo": "Description", "accessTokensInfoDetail1": "Text", "accessTokensInfoDetail2": "Description", "accessTokensInfoDetail3": "Text", "accessTokensInfoDetail4": "Description", "accessTokensInfoDetail5": "Description", "noPermission": "无权进行此操作"}, "topup": {"onlineRecharge": "Recarga en línea", "cardRedemption": "Canje de código", "accountBalance": "Saldo de la cuenta", "rechargeReminder": "Recordatorio de recarga", "reminder1": "1. El saldo se puede utilizar para llamadas de modelos, compra de paquetes, etc.", "reminder2": "2. Si el monto no se acredita después del pago, por favor contacte al servicio de atención al cliente para que lo gestione.", "reminder3": "3. El saldo no se puede retirar, pero se puede transferir dentro del mismo grupo de usuarios.", "reminder4WithTransfer": "4. Después de que la recarga sea exitosa, la validez del saldo de la cuenta se restablecerá a", "reminder4WithoutTransfer": "3. Después de que la recarga sea exitosa, la validez del saldo de la cuenta se restablecerá a", "days": "cielo", "paymentSuccess": "<PERSON><PERSON>oso", "paymentError": "Error en el pago", "paymentAmount": "Monto a pagar:", "purchaseAmount": "Límite de compra: $", "yuan": "yuan", "or": "o", "usd": "dólares estadounidenses", "cny": "yuan", "enterAmount": "¡Por favor, ingrese el monto de recarga!", "amountPlaceholder": "Por favor, ingrese el monto de recarga, desde {{min}} d<PERSON><PERSON><PERSON>.", "amountUpdateError": "Error al actualizar el monto.", "alipay": "Alipay", "wechat": "WeChat", "visaMastercard": "Visa / Mastercard", "cardFormatError": "El formato del código de canje es incorrecto.", "redeemSuccess": "¡Intercambio de {{amount}} exitoso!", "redeemError": "Error en el intercambio, por favor inténtelo de nuevo más tarde.", "enterCardKey": "Por favor, introduce el código de canje.", "cardKeyPlaceholder": "Por favor, ingrese el código de canje.", "buyCardKey": "Compra de códigos de canje.", "redeem": "Liquidar de inmediato", "record": {"title": "Registro de recarga", "amount": "Límite de recarga", "payment": "Monto a pagar", "paymentMethod": "Método de pago", "orderNo": "Número de pedido", "status": "estado", "createTime": "Fecha de creación", "statusSuccess": "éxito", "statusPending": "Procesando", "statusFailed": "fracaso"}, "paymentMethodAlipay": "Alipay", "paymentMethodWxpay": "WeChat", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "Administrador", "paymentMethodRedeem": "código de canje", "alipayF2F": "Alipay cara a cara"}, "pricing": {"fetchErrorMessage": "Se ha producido un error al obtener la información de precios, por favor contacte al administrador.", "availableModelErrorMessage": "Se ha producido un error al obtener el modelo disponible, por favor contacte al administrador.", "modelName": "Nombre del modelo", "billingType": "Tipo de facturación", "price": "precio", "ratio": "magnificación", "promptPriceSame": "Precio sugerido: igual a la tarifa original.", "completionPriceSame": "Precio complementario: igual a la tarifa original.", "promptPrice": "Precio sugerido: $ {{price}} / 1M tokens", "completionPrice": "Precio completo: $ {{price}} / 1M tokens", "promptRatioSame": "Tasa de indicación: igual a la tasa original.", "completionRatioSame": "Completar el multiplicador: igual al multiplicador original.", "promptRatio": "Tasa de sugerencia: {{ratio}}", "completionRatio": "Completar la tasa: {{ratio}}", "payAsYouGo": "[TRANSLATION_FAILED] pago por uso - Chat", "fixedPrice": "$ {{price}} / vez", "payPerRequest": "Text", "dynamicPrice": "$ {{price}} / vez", "payPerRequestAPI": "Pago por uso - API", "loadingTip": "Obteniendo información de precios...", "userGroupRatio": "Su ratio de agrupación de usuarios es: {{ratio}}", "readFailed": "Error de lectura", "billingFormula": "Costo por uso = Tasa de conversión × Multiplicador de grupo × Multiplicador de modelo × (Número de tokens de aviso + Número de tokens de completado × Multiplicador de completado) / 500000 (unidad: dólares)", "billingFormula1": "Tasa de conversión = (nuevo multiplicador de recarga / multiplicador de recarga original) × (nuevo multiplicador de grupo / multiplicador de grupo original)", "generatedBy": "Esta página fue generada automáticamente por {{systemName}}.", "modalTitle": "Detalles de precios", "perMillionTokens": "/1M tokens", "close": "<PERSON><PERSON><PERSON>", "searchPlaceholder": "Buscar el nombre del modelo", "viewGroups": "Ver grupos", "copiedToClipboard": "Text", "copyFailed": "Copia fallida.", "groupName": "Nombre del grupo", "availableGroups": "Grupos disponibles para el modelo {{model}}", "noGroupsAvailable": "No hay grupos disponibles.", "modelGroupsErrorMessage": "Error al obtener los datos del grupo del modelo.", "currentGroup": "Grupo actual", "copyModelName": "Copiar el nombre del modelo", "groupRatio": "tasa de agrupamiento", "closeModal": "<PERSON><PERSON><PERSON>", "groupsForModel": "Modelo disponible por grupos", "actions": "Operación", "filterByGroup": "Filtrar por grupos", "groupSwitched": "Se ha cambiado al grupo: {{group}}", "showAdjustedPrice": "Mostrar el precio ajustado por grupo (multiplicador actual: {{ratio}})"}, "guestQuery": {"usageTime": "Uso del tiempo", "modelName": "Nombre del modelo", "promptTooltip": "Entrada consume tokens.", "completionTooltip": "Salida de consumo de tokens", "quotaConsumed": "Límite de consumo", "pasteConfirm": "Se ha detectado un token válido en el portapapeles, ¿desea pegarlo?", "queryFailed": "Consulta fallida.", "tokenExpired": "El token ha expirado.", "tokenExhausted": "El límite de este token se ha agotado.", "invalidToken": "Por favor, ingrese el token correcto.", "focusRequired": "Por favor, asegú<PERSON>e de que la página esté en estado de enfoque.", "queryFirst": "Por favor, consulta primero.", "tokenInfoText": "Total de tokens: {{totalQuota}}  \nConsumo de tokens: {{usedQuota}}  \nSaldo de tokens: {{remainQuota}}  \nNúmero de llamadas: {{callCount}}  \nVálido hasta: {{validUntil}}", "unlimited": "sin restricciones", "neverExpire": "Nunca caduca", "infoCopied": "La información del token ha sido copiada al portapapeles.", "copyFailed": "Copia fallida.", "noDataToExport": "No hay datos que se puedan exportar.", "prompt": "sugerencia", "completion": "completar", "disabled": "La consulta de visitantes no está habilitada.", "tokenQuery": "Consulta de token", "tokenPlaceholder": "Por favor, introduzca el token que desea consultar (sk-xxx).", "tokenInfo": "Información del token", "copyInfo": "Copiar información", "totalQuota": "Monto total de tokens", "usedQuota": "Consumo de tokens", "remainQuota": "Saldo de tokens", "callCount": "Número de ll<PERSON>das", "validUntil": "<PERSON><PERSON><PERSON><PERSON> hasta", "currentRPM": "RPM actual", "currentTPM": "TPM actual", "callLogs": "Registro de llamadas", "exportLogs": "Exportar registro"}, "agencyProfile": {"fetchError": "Error al obtener la información del agente.", "fetchCommissionError": "Error al obtener la lista de comisiones.", "systemPreset": "Configuración del sistema", "lowerRatioWarning": "La tarifa es inferior a la preestablecida por el sistema.", "lowerRatioMessage": "Las siguientes tarifas son inferiores al valor preestablecido por el sistema, por favor modifíquelas a tiempo:", "cancelRatioEdit": "Cancelar la modificación de tarifas.", "updateSuccess": "Actualización exitosa.", "updateError": "Actualización de información del agente fallida:", "updateFailed": "Actualización fallida:", "customPriceUpdateSuccess": "Actualización de precio personalizada exitosa.", "customPriceUpdateError": "La actualización del precio personalizado ha fallado:", "time": "Tiempo", "type": "tipo", "agencyCommission": "Comisión de agentes", "unknownType": "Tipo desconocido", "amount": "monto", "balance": "saldo", "description": "Descripción", "group": "grupo", "customRate": "Tarifa personalizada", "systemDefaultRate": "Tarifa predeterminada del sistema", "action": "Operación", "save": "guardar", "cancel": "<PERSON><PERSON><PERSON>", "edit": "editar", "agencyConsole": "Consola de agentes", "agencyInfo": "Información del agente", "editInfo": "Editar información", "agencyName": "Nombre del agente", "agencyLevel": "<PERSON><PERSON> de agente", "level1": "Nivel 1", "subordinateUsers": "usuario subordinado", "totalSales": "Ventas totales", "commissionIncome": "ingresos por comisiones", "cumulativeEarnings": "ingresos acumulados", "agencyFunctions": "Text", "hideSubordinateUsers": "Ocultar usuarios secundarios", "viewSubordinateUsers": "Ver usuarios subordinados", "hideCommissionDetails": "Ocultar los detalles de la comisión", "viewCommissionDetails": "Ver detalles de comisiones", "hideCustomPrice": "Ocultar precio personalizado", "setCustomPrice": "Establecer un precio personalizado", "subordinateUsersList": "Lista de usuarios inferiores", "commissionRecords": "Registro de comisiones", "customPriceSettings": "Configuración de precios personalizada", "saveChanges": "Guardar cambios", "editAgencyInfo": "Editar información del agente.", "logo": "Logo", "setAgencyLogo": "Configurar el logo del agente.", "customHomepage": "Página de inicio personalizada", "aboutContent": "Sobre el contenido", "newHomepageConfig": "Nueva configuración de la página de inicio", "customAnnouncement": "<PERSON><PERSON><PERSON>", "customRechargeGroupRateJson": "JSON de tarifas de grupos de recarga personalizados", "customRechargeRate": "Tasa de recarga personalizada", "viewSystemDefaultRate": "Ver la tarifa predeterminada del sistema.", "rateComparison": "Comparación de tarifas", "comparisonResult": "Resultados de comparación", "higherThanSystem": "Superior al sistema", "lowerThanSystem": "por debajo del sistema", "equalToSystem": "igual al sistema", "unknown": "desconocido", "notAnAgentYet": "No eres un agente todavía.", "becomeAnAgent": "Convertirse en agente.", "startYourOnlineBusiness": "🌟 Comience fácilmente su negocio en línea.", "becomeOurAgent": "Conviértete en nuestro agente y disfruta de una experiencia de emprendimiento sin presiones:", "noInventory": "💼 No es necesario tener inventario, sin presión de capital.", "instantCommission": "Text", "easyManagement": "🖥️ No se necesita tecnología de creación de sitios, gestione su tienda en línea fácilmente.", "flexibleDomainChoice": "🌐 Selección flexible de dominios", "youCan": "Puede:", "useOwnDomain": "🏠 Usar su propio dominio", "orUseOurSubdomain": "🎁 O podemos ofrecerle un subdominio exclusivo.", "convenientStart": "🔥 Ya sea que tenga experiencia o esté comenzando, le ofrecemos una forma conveniente de empezar.", "actNow": "Text", "contactAdmin": "¡Contacta al administrador del sitio y comienza tu viaje como agente! 📞", "applyNow": "Solicitar ahora", "contactCooperation": "Consulta de cooperación", "understandPolicy": "Entender la política de agentes y los detalles de colaboración.", "provideDomain": "Proporcionar un nombre de dominio", "configDomain": "Proporcione su dominio y le ayudaremos a configurarlo.", "promoteAndEarn": "Promoción de ganancias", "startPromoting": "Comience a promocionar su sitio de agencia y gane comisiones.", "noDeploymentWorries": "No te preocupes por el complicado despliegue de servicios en la nube, los canales de pago o los problemas de inventario.", "easySetup": "Text", "customizeContent": "Puede personalizar precios, información del sitio, SEO, logotipos y otros contenidos.", "commissionBenefits": "Como agente, recibirás una parte de la recarga de los usuarios, el sistema deducirá automáticamente los costos, y el monto restante se puede retirar en cualquier momento.", "joinNowBenefit": "¡Únete a nosotros ahora y aprovechemos juntos los beneficios de la era de la IA!", "groups": {"student": "estudiante universitario", "studentDesc": "Con tiempo suficiente, espero aumentar mis ingresos de manera fácil a través de actividades promocionales para cubrir parte de mis gastos de vida y entretenimiento.", "partTime": "Trabajo a tiempo parcial o actividad secundaria.", "partTimeDesc": "No se necesita una gran inversión de tiempo, solo hay que promocionar de manera sencilla en el tiempo libre de trabajo para ganar ingresos adicionales fácilmente.", "mediaWorker": "Profesional de los medios sociales", "mediaWorkerDesc": "Teniendo una cierta base de seguidores, solo necesitas agregar un enlace al final del artículo o publicación para lograr fácilmente ingresos adicionales.", "freelancer": "trabajador autónomo", "freelancerDesc": "Tener mucho tiempo flexible y poder aumentar fácilmente los ingresos adicionales solo participando en actividades de ventas."}, "stories": {"story1": {"name": "<PERSON><PERSON><PERSON>", "role": "estudiante universitario"}, "story2": {"name": "<PERSON><PERSON><PERSON>", "role": "Profesor de secundaria"}, "story3": {"name": "<PERSON><PERSON><PERSON>", "role": "comercio electrónico"}, "story4": {"name": "<PERSON><PERSON><PERSON>", "role": "medios autogestionados"}, "story5": {"name": "<PERSON><PERSON><PERSON>", "role": "practicante de investigación científica"}, "story6": {"name": "<PERSON><PERSON><PERSON>", "role": "Influencer <PERSON><PERSON>"}, "story7": {"name": "<PERSON><PERSON><PERSON>", "role": "medios de comunicación independientes"}, "story8": {"name": "<PERSON><PERSON><PERSON>", "role": "industria de TI"}}, "earnedAmount": "<PERSON> has ganado {{amount}}.", "applyForAgentNow": "Solicite convertirse en agente de inmediato.", "businessLinesConnected": "Más de 40 líneas de negocio ya están conectadas.", "agencyJoin": "Text", "becomeExclusiveAgent": "Text", "startBusinessJourney": "Comience fácilmente su viaje empresarial~", "welcomeToAgencyPage": "¡Bienvenido a nuestra página de agentes!", "earningsTitle": "Más de cien personas han ganado más de 3000 yuanes.", "becomeAgentSteps": "Pasos para convertirse en agente.", "agencyRules": "Reglas de representación", "suitableGroups": "Público objetivo", "agencyImages": {"becomeAgent": "Convertirse en agente.", "agencyBusiness": "Negocios de representación"}, "rules": {"howToEstablishRelation": "Text", "howToEstablishRelationAnswer": "Regístrate en tu sitio de代理, y serás tu usuario.", "canSetPrice": "¿Puedo establecer el precio de venta?", "canSetPriceAnswer": "¡Claro! Pero tu precio de venta debe ser al menos un 10% superior al precio de costo.", "commissionShare": "¿Cuánto puedo obtener de comisión?", "commissionShareAnswer": {"assumption": "Supongamos: tu precio de adquisición es $1=1 yuan, tu precio de venta es $1=2 yuan, y tu porcentaje de comisión es del 90%.", "example": "Text", "calculation": "Puedes obtener: (2-1)*10*0.9 = 9 yuanes.", "explanation": "Interpretación: (Precio de venta - Precio de compra) * Volumen de transacciones * Tasa de comisión"}}}, "error": {"title": "error", "content": "Se produjo un error."}, "loading": {"title": "Cargando...", "content": "Cargando..."}, "notfound": {"title": "404", "content": "Página no encontrada"}, "servererror": {"title": "500", "content": "Error del servidor"}, "unauthorized": {"title": "401", "content": "No autorizado"}, "forbidden": {"title": "403", "content": "Acceso prohibido"}, "networkerror": {"title": "Error de red", "content": "Error de red"}, "timeout": {"title": "Tiempo de espera excedido", "content": "Tiempo de espera agotado"}, "noresult": {"title": "Sin resultados.", "content": "Sin resultados."}, "nopermission": {"title": "Sin permiso", "content": "Sin permiso"}, "channelBridge": {"title": "Conexión rápida de canales", "channelPlatform": "plataforma de canal", "billingMethod": "Método de facturación", "channelName": "Nombre del canal", "remark": "<PERSON>a", "availableGroups": "Grupos disponibles", "availableModels": "Modelos disponibles", "channelKey": "Clave de canal", "proxyAddress": "Dirección de conexión", "cancel": "<PERSON><PERSON><PERSON>", "submit": "Enviar", "gpt35Models": "Modelo GPT-3.5", "gpt4Models": "Modelo GPT-4", "clear": "Vaciar", "customModelName": "Nombre del modelo personalizado", "add": "Agregar", "moreConfigReminder": "Para más configuraciones, guarde el canal y luego edite.", "quickIntegration": "Conexión con un solo clic", "selectBillingMethod": "Seleccione el método de facturación.", "enterChannelName": "Por favor, ingrese el nombre del canal.", "enterChannelRemark": "Por favor, ingrese la nota del canal.", "selectAvailableGroups": "Seleccione el grupo que puede utilizar este canal.", "selectAvailableModels": "Seleccionar/buscar modelos disponibles en este canal.", "enterChannelKey": "Por favor, introduzca la clave del canal.", "proxyAddressPlaceholder": "Esta opción es opcional y se utiliza para realizar llamadas a la API a través de un proxy. Por favor, ingrese la dirección del proxy.", "includes16kModels": "Incluye el modelo de 16k.", "excludes32kModels": "No incluye el modelo de 32k.", "cleared": "Vacío.", "addCustomModel": "Agregar un modelo personalizado", "clipboardTokenDetected": "Se ha detectado un token válido en el portapapeles, ¿desea pegarlo?", "channelIntegrationSuccess": "¡Conexión de canal exitosa!", "channelIntegrationFailed": "Fallo en la conexión del canal:"}, "about": {"loading": "Obtener contenido más reciente...", "noContent": "El administrador no ha configurado el contenido de la página \"Acerca de\".", "loadFailed": "Error al cargar el contenido..."}, "onlineTopupRecord": {"title": "Registro de recarga", "columns": {"id": "ID", "username": "usuario", "amount": "Límite de recarga", "money": "Monto a pagar", "paymentMethod": "Método de pago", "tradeNo": "Número de pedido", "status": "estado", "createTime": "Fecha de creación"}, "status": {"success": "éxito", "pending": "Procesando", "failed": "fracaso"}, "paymentMethod": {"alipay": "Alipay", "wxpay": "WeChat", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "PayPal"}}, "logContentDetail": {"description": "Descripción de la información", "downstreamError": "Error de downstream", "originalError": "Error original", "requestParams": "Parámetros de solicitud", "copy": "copiar"}, "viewMode": {"switchTo": "Cambiar a la perspectiva de {{mode}}.", "cost": "costo", "usage": "<PERSON><PERSON>"}, "agenciesTable": {"title": "Gestión de agentes", "addAgency": "Nuevo agente autorizado", "columns": {"id": "ID", "userId": "ID de usuario", "name": "Nombre", "domain": "nombre de dominio", "commissionRate": "tasa de comisión", "salesVolume": "ventas", "userCount": "Número de usuarios", "commissionIncome": "ingresos por comisiones", "historicalCommission": "ingresos acumulados", "actions": "Operación"}, "confirm": {"deleteTitle": "¿Está seguro de que desea eliminar a este agente?", "updateName": "Actualizando el nombre del agente...", "updateSuccess": "Actualización exitosa.", "updateFailed": "Actualización fallida.", "deleteSuccess": "¡Eliminación exitosa!"}, "messages": {"getListFailed": "Error al obtener la lista de agentes: {{message}}", "deleteSuccess": "¡Eliminación exitosa!", "loadingData": "Cargando..."}}, "units": {"times": "sigu<PERSON>e", "percentage": "{{value}}%", "formatUsage": "{{name}}: {{value}} veces ({{percent}}%)"}, "dailyUsage": {"total": "Total", "totalCost": "costo total", "tooltipTitle": {"cost": "Situación de costos", "usage": "<PERSON><PERSON>"}, "yAxisName": {"cost": "Costo (USD)", "usage": "Uso (USD)"}}, "dailyUsageByModel": {"total": "Total", "tooltipTotal": "Total: $ {{value}}", "switchTo": "Cambiar a", "cost": "costo", "usage": "<PERSON><PERSON>", "perspective": "perspectiva", "granularity": {"hour": "por hora", "day": "por día", "week": "por semana", "month": "mensualmente"}}, "checkinModal": {"title": "Por favor, complete la verificación.", "captchaPlaceholder": "código de verificación", "confirm": "Determinado", "close": "<PERSON><PERSON><PERSON>"}, "balanceTransfer": {"title": "Transferencia entre cuentas", "accountInfo": {"balance": "Saldo de la cuenta", "transferFee": "tarifa de transferencia", "groupNote": "Solo se pueden realizar transferencias entre grupos de usuarios idénticos."}, "form": {"receiverId": "ID del receptor", "receiverUsername": "Nombre de usuario del receptor", "remark": "Información de notas", "amount": "Monto de la transferencia", "expectedFee": "<PERSON><PERSON>o", "submit": "Iniciar transfer<PERSON>cia"}, "result": {"success": "Transfer<PERSON>cia exitosa.", "continueTransfer": "Continuar la transferencia.", "viewRecord": "Ver registros"}, "warning": {"disabled": "El administrador no ha habilitado la función de transferencia, por lo que no se puede utilizar temporalmente."}, "placeholder": {"autoCalculate": "El monto de la transferencia se calcula automáticamente."}}, "channelsTable": {"title": "Gestión de canales", "columns": {"id": "ID", "name": "Nombre", "type": "tipo", "key": "clave", "base": "Dirección de la interfaz", "models": "modelo", "weight": "peso", "priority": "prioridad", "retryInterval": "Intervalo de reintento", "responseTime": "Tiempo de respuesta", "rpm": "RPM", "status": "Estado", "quota": "saldo", "expireTime": "<PERSON><PERSON> de caducidad", "group": "grupo", "billingType": "Tipo de facturación", "actions": "Operación", "fusing": "fusión", "sort": "prioridad", "createdTime": "Time", "disableReason": "禁用原因"}, "status": {"all": "todos", "normal": "normal", "enabled": "Estado normal", "manualDisabled": "Desactivación manual", "waitingRetry": "<PERSON><PERSON><PERSON><PERSON> re<PERSON>", "suspended": "Suspendido", "specified": "Estado designado", "allDisabled": "Deshabilitar", "specifiedDisabled": "Especificar el tipo de desactivación", "partiallyDisabled": "Status"}, "placeholder": {"selectGroup": "Seleccione/Búsqueda de grupos", "selectStatus": "Seleccionar el estado del canal", "inputSelectModel": "Ingrese/seleccione el nombre del modelo.", "selectFusingStatus": "Seleccionar el estado de desconexión automática."}, "quota": {"usageAmount": "Consumo: {amount}", "remainingAmount": "Restante: {amount}", "customTotalAmount": "Monto personalizado: {amount}", "updateNotSupported": "No se admite la actualización del saldo por el momento, por favor use el saldo personalizado.", "details": "de<PERSON>les", "sufficient": "suficiente"}, "actions": {"edit": "editar", "copy": "canal de clonación", "delete": "Eliminar canal", "enable": "Activar", "disable": "Deshabilitar", "test": "p<PERSON><PERSON>", "advancedTest": "<PERSON><PERSON><PERSON>", "viewLog": "Registro de canal", "viewAbility": "Ver capacidad", "cleanUsage": "Vaciar lo utilizado", "updateBalance": "<PERSON><PERSON><PERSON><PERSON> saldo", "copyKey": "<PERSON><PERSON><PERSON> clave"}, "confirm": {"deleteTitle": "Confirmación de eliminación", "deleteContent": "¿Estás seguro de que deseas eliminar el canal {{name}} (#{{id}})?", "cleanUsageTitle": "Confirmación de vaciado de uso", "cleanUsageContent": "¿Está seguro de que desea vaciar el monto consumido del canal {{name}} (#{{id}})?", "testTitle": "Confirmación de prueba", "testContent": "¿Está seguro de que desea probar el canal {{status}}?", "testNote": "Nota: Esta función requiere la configuración de [Configuración] -> [Repetidor] -> [Configuración de monitoreo] -> [Deshabilitar canal en caso de fallo, habilitar canal en caso de éxito]. Si no se activan las configuraciones relacionadas, los canales no se deshabilitarán ni habilitarán automáticamente después de completar la prueba.", "deleteDisabledTitle": "Eliminar confirmación", "deleteDisabledContent": "¿Está seguro de que desea eliminar todos los canales {{type}}?"}, "messages": {"operationSuccess": "Operación exitosa", "operationSuccessWithSort": "Operación exitosa, el orden de los canales puede haber cambiado, ¡se recomienda ordenar por ID!", "operationFailed": "Error de operación: {{message}}", "testRunning": "La prueba del canal {{name}}(#{{id}}) se está ejecutando, por favor espere...", "testSuccess": "La prueba del canal \"{{name}}(#{{id}})\" {{model}} fue exitosa, tiempo de respuesta {{time}} segundos.", "testFailed": "La prueba del canal \"{{name}}(#{{id}})\" {{model}} ha fallado. Código de estado: {{code}}, razón: {{reason}}, haga clic para ver detalles.", "testStarted": "Comience a probar el canal {{status}}, por favor actualice más tarde para ver los resultados. La aplicación de los resultados de la prueba depende de su configuración de monitoreo.", "testOperationFailed": "Prueba fallida", "deleteSuccess": "Se eliminaron con éxito {{count}} canales.", "deleteFailed": "Eliminación fallida: {{message}}", "modelPrefix": "modelo {{model}}", "channelInfo": "Información del canal", "channelDetail": "{{nombre}}(#{{id}}){{infoModelo}}", "updateBalanceSuccess": "La actualización del saldo del canal \"{{name}}\" se realizó con éxito.", "updateBalanceFailed": "La actualización del saldo del canal \"{{name}}\" falló: {{message}}", "updateAllBalanceStarted": "Comenzar a actualizar el saldo de todos los canales en estado normal.", "updateAllBalanceSuccess": "Actualización del saldo de todos los canales exitosa.", "fetchGroupError": "Error al obtener datos del grupo de canales: {{response}}", "fetchChannelError": "Error al obtener datos del canal: {{message}}", "selectChannelFirst": "Por favor, seleccione primero el canal que desea eliminar.", "deleteDisabledSuccess": "Se han eliminado todos los canales {{type}}, un total de {{count}}.", "deleteOperationFailed": "Eliminación fallida.", "copySuccess": "<PERSON><PERSON> exitosa", "copyFailed": "Error de copia: {{message}}", "emptyKey": "La clave está vacía.", "testSuccessWithWarnings": "Prueba exitosa con advertencias", "viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "fetchChannelDetailError": "Error al obtener detalles del canal", "topupSuccess": "<PERSON><PERSON><PERSON> exitosa", "topupFailed": "Recarga fallida: {{message}}"}, "popover": {"channelInfo": "Información del canal"}, "menu": {"deleteManualDisabled": "Eliminar el canal deshabilitado manualmente.", "deleteWaitingRetry": "Eliminar el canal de espera para reiniciar.", "deleteSuspended": "Eliminar el canal en pausa.", "testAll": "<PERSON>bar todos los canales", "testNormal": "Prueba del canal normal.", "testManualDisabled": "Prueba de desactivación manual del canal.", "testWaitingRetry": "Prueba de espera para reiniciar el canal.", "testSuspended": "Prueba de suspensión de uso del canal.", "deleteDisabledAccount": "Eliminar canales de cuenta desactivada", "deleteQuotaExceeded": "Eliminar canales de cuota excedida", "deleteRateLimitExceeded": "Eliminar canales de límite de frecuencia", "deleteInvalidKey": "Eliminar canales de clave inválida", "deleteConnectionError": "Eliminar canales de error de conexión"}, "tooltip": {"testNote": "Se necesita combinar [Configuración] -> [<PERSON>etidor] -> [Configuración de monitoreo] -> [Deshabilitar canal en caso de fallo, habilitar canal en caso de éxito] para su uso. Si no está activado, no se deshabilitará ni habilitará automáticamente después de completar la prueba de velocidad."}, "disableReasons": {"account_deactivated": "Cuenta desactivada", "quota_exceeded": "<PERSON><PERSON><PERSON> excedida", "rate_limit_exceeded": "Límite de frecuencia excedido", "invalid_key": "Clave inválida", "connection_error": "E<PERSON>r de conexión"}, "topup": {"reminder1": "Asegúrate de que la información de pago sea correcta antes de proceder", "reminder2": "Si encuentras problemas, contacta al servicio al cliente"}}, "billingTypes": {"quota": "límite", "times": "veces"}, "serverLogViewer": {"title": "Visor de registros del servidor", "connecting": "Conectando al servidor...", "downloadSelect": "Seleccionar archivo de registro para descargar", "nginxConfig": "Descripción de la configuración de Nginx para WebSocket", "directAccess": "Si accede mediante un nombre de dominio y no se ha configurado el soporte de WebSocket, el visor de registros no funcionará. En este caso, puede acceder directamente a través de la IP del servidor y el puerto (por ejemplo: http://your-ip:9527).", "domainAccess": "Para acceder a través del nombre de dominio, es necesario agregar la siguiente configuración en la configuración de Nginx para soportar WebSocket:", "buttons": {"pause": "Pausa", "resume": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Vaciar"}, "errors": {"fetchFailed": "Error al obtener la lista de archivos de registro.", "downloadFailed": "Error al descargar el archivo de registro.", "wsError": "Error de conexión WebSocket"}}, "channelScore": {"score": "puntuación", "successRate": "tasa de éxito", "avgResponseTime": "Tiempo de respuesta promedio", "title": "Puntuación del canal", "hourlyTitle": "Puntuación por hora del canal", "dailyTitle": "Puntuación diaria del canal", "weeklyTitle": "Puntuación semanal del canal", "monthlyTitle": "Puntuación mensual del canal", "allTimeTitle": "Puntuación general del canal", "infoTooltip": "La puntuación del canal se calcula como una puntuación compuesta basada en la tasa de éxito y el tiempo de respuesta.", "tableView": "Vista de tabla", "chartView": "Vista de gráfico", "refresh": "Actualizar", "selectModel": "Seleccionar modelo", "allModels": "todos los modelos", "sortByScore": "Ordenar por puntuación", "sortBySuccessRate": "Ordenado por tasa de éxito.", "sortByResponseTime": "Ordenar por tiempo de respuesta", "noData": "No hay datos disponibles.", "totalItems": "Total {{total}} elementos", "fetchError": "Error al obtener los datos de puntuación del canal.", "aboutScoring": "Sobre el cálculo de puntuaciones.", "scoringExplanation": "La puntuación del canal se calcula como una puntuación compuesta basada en la tasa de éxito y el tiempo de respuesta, con un puntaje máximo de 1.", "successRateWeight": "Peso de tasa de éxito (70%)", "successRateExplanation": "Cuanto mayor sea la tasa de <PERSON>xi<PERSON>, mayor ser<PERSON> la puntuación.", "responseTimeWeight": "Peso del tiempo de respuesta (30%)", "responseTimeExplanation": "Se otorgan puntos completos si el tiempo de respuesta es inferior a 1000 ms; de lo contrario, se deducen puntos proporcionalmente.", "columns": {"rank": "Clasificación", "channelId": "ID de canal", "channelName": "Nombre del canal", "model": "modelo", "totalRequests": "Número total de solicitudes", "successRequests": "Número de solicitudes exitosas", "failedRequests": "Número de solicitudes fallidas", "successRate": "tasa de éxito", "avgResponseTime": "Tiempo de respuesta promedio", "score": "Puntuación total", "actions": "Operación"}, "actions": {"viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "test": "Canal de prueba", "edit": "Canal de edición"}, "tooltips": {"excellent": "excelente", "good": "bueno", "average": "general", "poor": "deficiente", "veryPoor": "<PERSON><PERSON> mal."}, "scoringExplanation100": "La puntuación del canal se calcula como una puntuación compuesta basada en la tasa de éxito y el tiempo de respuesta, con un máximo de 100 puntos."}, "menu": {"channelScores": "Puntuación del canal"}, "relay": {"dispatchOptions": "Opciones de programación", "preciseWeightCalculation": "Cál<PERSON>lo preciso de pesos", "preciseWeightCalculationTip": "Al activarlo, se utilizarán algoritmos más precisos para calcular el peso de los canales, lo que podría aumentar la carga de CPU.", "channelMetricsEnabled": "Activar la estadística de indicadores de canal", "channelMetricsEnabledTip": "Al activarlo, se recopilarán indicadores como la tasa de éxito y el tiempo de respuesta del canal, que se utilizarán para evaluar el rendimiento del canal. Si se desactiva, no se recopilarán estos datos, lo que puede reducir el uso de recursos del sistema.", "channelScoreRoutingEnabled": "Habilitar el enrutamiento basado en la puntuación del canal.", "channelScoreRoutingEnabledTip": "Una vez activado, el sistema ajustará automáticamente la prioridad de asignación de solicitudes según el rendimiento histórico de los canales, y los canales con mejor rendimiento obtendrán una mayor probabilidad de asignación de solicitudes.", "globalIgnoreBillingTypeFilteringEnabled": "Ignorar filtro de tipo de facturación globalmente", "globalIgnoreBillingTypeFilteringEnabledTip": "<PERSON>uando esté habilitado, se ignorará el filtro de tipo de facturación para todos los canales", "globalIgnoreFunctionCallFilteringEnabled": "Ignorar filtro de llamadas de función globalmente", "globalIgnoreFunctionCallFilteringEnabledTip": "<PERSON>uando esté habilitado, se ignorará el filtro de llamadas de función para todos los canales", "globalIgnoreImageSupportFilteringEnabled": "Ignorar filtro de soporte de imágenes globalmente", "globalIgnoreImageSupportFilteringEnabledTip": "<PERSON>uando esté habilitado, se ignorará el filtro de soporte de imágenes para todos los canales"}, "dynamicRouter": {"title": "Gestión de rutas dinámicas", "reloadRoutes": "Recargar la ruta", "exportConfig": "Exportar configuración", "clearConfig": "Vaciar configuración", "importantNotice": "Aviso importante", "reloadLimitation": "1. La recarga de rutas solo puede actualizar la configuración de las rutas existentes, no puede agregar o eliminar rutas. Para recargar completamente la estructura de rutas, reinicie la aplicación.", "exportDescription": "2. La exportación de la configuración exportará la configuración actual de la base de datos al archivo router.json, filtrando los valores nulos y cero.", "clearDescription": "3. Vaciar la configuración eliminará todas las configuraciones de rutas dinámicas en la base de datos, y después de reiniciar la aplicación, se volverán a cargar desde el archivo router.json.", "routeGroups": "Grupo de enrutamiento", "upstreamConfig": "Configuración de upstream", "endpointConfig": "Configuración de puntos finales", "editRouteGroup": "Editar grupo de rutas", "editUpstream": "Editar la configuración de upstream.", "editEndpoint": "Editar la configuración del punto final", "editJSON": "<PERSON>ar <PERSON>", "confirmClear": "Confirmar la vacuidad de la configuración.", "confirmClearMessage": "Esta operación eliminará toda la configuración de rutas dinámicas en la base de datos, y la próxima vez que se reinicie la aplicación, se volverá a cargar desde el archivo de configuración. ¿Está seguro de que desea continuar?", "configCleared": "La configuración de la ruta dinámica ha sido vaciada, por favor reinicie la aplicación para aplicar los cambios.", "configExported": "La configuración se ha exportado correctamente al archivo.", "configReloaded": "La configuración del enrutador se ha recargado con éxito."}, "notification": {"title": "configuración de notificaciones", "subscriptionEvents": "Message", "notificationMethods": "通知方式", "alertSettings": "Message", "emailConfig": "configuración de correo", "customEmails": "Message", "addEmail": "添加邮箱", "removeEmail": "eliminar", "emailPlaceholder": "Message", "emailTooltip": "Message", "emailDescription": "Message", "balanceThreshold": "Message", "balanceThresholdTooltip": "Message", "balanceThresholdDescription": "Description", "alertExplanationTitle": "预警说明", "alertExplanation": "Message", "selectEvents": "Message", "eventsDescription": "Message", "selectMethods": "Message", "methodsDescription": "Message", "description": "Description", "recommended": "推荐开启", "important": "Message", "testRecommendation": "Message", "testNotification": "测试通知", "testMessage": "Message", "testSuccess": "测试通知发送成功", "testFailed": "Message", "saveSuccess": "Message", "saveFailed": "保存设置fallo", "validation": {"invalidEmail": "Message", "emailRequired": "邮箱地址不能为空", "invalidUrl": "Message", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "请输入WxPusher APP Token", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "请输入飞书机器人Webhook URL", "webhookUrlRequired": "Message", "telegramTokenRequired": "请输入Telegram Bot Token", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "Por favor ingrese el Token del Bot de Telegram"}, "qywxbotConfig": "Configuración del bot de WeChat empresarial", "qywxbotGuide": "Message", "wxpusherConfig": "WxPusher配置", "wxpusherGuide": "Message", "wxpusherUid": "用户UID", "dingtalkConfig": "Configuración del bot de DingTalk", "dingtalkGuide": "Message", "feishuConfig": "Configuración del bot de Feishu", "feishuGuide": "Message", "webhookConfig": "Webhook配置", "webhookGuide": "Message", "webhookUrl": "调用地址", "webhookToken": "Message", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "Bear<PERSON>（可选）", "telegramConfig": "Configuración del bot de Telegram", "telegramGuide": "Message", "telegramChatIdPlaceholder": "********* 或 @username", "events": {"account_balance_low": "Message", "account_quota_expiry": "额度即将过期", "security_alert": "alerta de seguridad", "system_announcement": "Message", "promotional_activity": "notificación de actividad promocional", "model_pricing_update": "Message", "anti_loss_contact": "Message"}, "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "当账户额度即将过期时提前通知您", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "模型价格变动和计费规则更新通知", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "通过邮件接收通知消息", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "通过企业微信机器人接收通知", "dingtalk": "Description", "feishu": "通过飞书机器人接收通知"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook通知", "wxpusher": "Message", "qywxbot": "企业微信机器人", "dingtalk": "Message", "feishu": "飞书机器人"}, "configurationSteps": "Pasos de configuración:", "detailedDocumentation": "Documentación detallada:", "qywxbotConfigurationGuide": "Guía de configuración del bot de WeChat empresarial", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "IngresarWebhook URLen la configuración anterior", "qywxbotDocumentationLink": "Message", "wxpusherConfiguration": "Configuración de WxPusher", "wxpusherConfigurationGuide": "Guía de configuración de WxPusher", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "Message", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Por favor ingreseWxPusher APP Token", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "Visite el sitio web oficial de WxPusher para registrar una cuenta", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "IngresarAPP Token和用户UIDen la configuración anterior", "wxpusherOfficialWebsite": "WxPusherSitio web oficial", "dingtalkConfigurationGuide": "Guía de configuración del bot de DingTalk", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "钉钉自定义机器人接入", "feishuConfigurationGuide": "Guía de configuración del bot de Feishu", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "Message", "feishuStep4": "Message", "feishuStep5": "复制生成的Webhook URL", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Guía de configuración del bot de Telegram", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "Message", "telegramStep5": "Message", "telegramStep6": "Visitar https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "IngresarWebhook URLen la configuración anterior", "dingtalkNoticeTitle": "Notas importantes:", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "IngresarWebhook URLen la configuración anterior", "feishuMessageFormatsTitle": "Formatos de mensaje compatibles:", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Notas importantes:", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• 建议启用签名校验以提高安全性", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "IngresarBot Token和Chat IDen la configuración anterior", "telegramNoticeTitle": "Notas importantes:", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Title", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "Message", "telegramChannelPermission": "• 频道中机器人需要有发送消息的权限", "webhookCallUrl": "Dirección de llamada", "webhookConfigurationGuide": "Guía de configuración de Webhook", "webhookDataFormatExample": "Ejemplo de formato de datos：", "webhookConfigurationInstructions": "Instrucciones de configuración：", "webhookRequestMethod": "• Método de solicitud：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• Método de autenticación：<PERSON><PERSON>（opcional, después de completar se agregará Authorization: Bearer {token} en el encabezado de la solicitud）", "webhookTimeout": "• Tiempo de espera：30 segundos", "webhookRetryMechanism": "• Mecanismo de reintento：Reintenta 2 veces después del fallo", "webhookTip": "💡 Consejo：Asegúrese de que su endpoint de Webhook pueda recibir solicitudes POST y devolver códigos de estado 2xx", "telegramStep3Detailed": "Configure el nombre del bot y el nombre de usuario según las indicaciones（el nombre de usuario debe terminar con bot）", "telegramPersonalChatDetailed": "• Chat personal：Envíe un mensaje al bot, luego acceda", "telegramGroupChatDetailed": "• Chat grupal：Agregue el bot al grupo, después de enviar un mensaje, acceda al mismo enlace", "telegramChannelDetailed": "• Canal：Agregue el bot como administrador, el Chat ID generalmente comienza con -100", "telegramQuickChatIdTitle": "Ejemplo de obtención rápida de Chat ID：", "telegramQuickStep1": "Reemplazar BOT_TOKEN：https://api.telegram.org/bot YOUR_BOT_TOKEN /getUpdates", "telegramQuickStep2": "Acceda al enlace anterior en el navegador", "telegramQuickStep3": "Busque en la respuesta JSON：\"chat\":{\"id\":*********}"}, "legal": {"privacyPolicy": {"title": "Política de Privacidad", "lastUpdated": "Última actualización：{{date}}", "sections": {"informationCollection": {"title": "Recopilación de Información", "description": "Recopilamos los siguientes tipos de información：", "items": {"accountInfo": "Información de cuenta：Cuando inicia sesión a través de Google, recopilamos su nombre, dirección de correo electrónico e información básica del perfil", "usageData": "Datos de uso：Registros de llamadas API, estadísticas de uso y registros del sistema", "technicalInfo": "Información técnica：Dirección IP, tipo de navegador, información del dispositivo"}}, "informationUsage": {"title": "Uso de la Información", "description": "Utilizamos la información recopilada para：", "items": ["Proporcionar y mantener nuestros servicios", "Autenticación de usuarios y gestión de cuentas", "Mejorar la calidad del servicio y la experiencia del usuario", "Enviar notificaciones importantes del servicio", "Prevenir fraude y abuso"]}}}, "serviceAgreement": {"title": "Acuerdo de Servicio", "lastUpdated": "Última actualización：{{date}}", "sections": {"serviceDescription": {"title": "Descripción del Servicio", "description": "Shell API Pro Max es una plataforma que proporciona servicios de API de IA.", "features": ["Acceso unificado a múltiples modelos de IA", "Monitoreo y gestión del uso", "Opciones de precios flexibles", "Herramientas y documentación para desarrolladores"]}}}}}