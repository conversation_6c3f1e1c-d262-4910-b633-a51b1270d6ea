{"message": {"copyModelSuccess": "تم نسخ اسم النموذج إلى الحافظة!", "copyFailed": "فشل النسخ، يرجى النسخ يدويًا.", "logoutSuccess": "تم تسجيل الخروج بنجاح", "loginSuccess": {"default": "تسجيل الدخول ناجح", "welcomeBack": "مرحبًا بعودتك"}, "removeLocalStorage": {"confirm": "هل ترغب في مسح الذاكرة المؤقتة المحلية؟", "success": "تم مسح الذاكرة المؤقتة المحلية بنجاح"}, "loadData": {"error": "فشل في تحميل بيانات {{name}}"}, "noNotice": "لا توجد محتويات للإعلانات في الوقت الحالي.", "verification": {"turnstileChecking": "تورنستيل يقوم بفحص بيئة المستخدم!", "pleaseWait": "يرجى المحاولة لاحقًا"}, "clipboard": {"inviteCodeDetected": "تم الكشف عن رمز الدعوة، وقد تم ملؤه تلقائيًا!", "clickToCopy": "انقر لنسخ", "copySuccess": "نسخ ناجح"}}, "common": {"yes": "نعم", "no": "لا", "copyAll": "نسخ الكل", "all": "الكل", "more": "المزيد", "unlimited": "<PERSON>ير محدود", "enabled": "فتح", "disabled": "إغلاق", "save": "<PERSON><PERSON><PERSON>", "cancel": "إلغاء", "create": "إنشاء", "usd": "دولار أمريكي", "day": "{{count}} يوم", "day_plural": "{{count}} يوم", "days": "السماء", "seconds": "ثانية", "times": "التالي", "submit": "تقديم", "bind": "رب<PERSON>", "unknown": "غير معروف", "loading": "جارٍ التحميل...", "copyFailed": "فشل النسخ", "people": "إنسان", "ok": "تأكيد", "close": "إغلاق", "copied": "تم النسخ", "expand": "توسيع", "collapse": "أخفِ", "none": "لا", "remark": "ملاحظات", "selectPlaceholder": "ير<PERSON>ى اختيار {{name}}", "on": "فتح", "off": "أغلق", "name": "علامة", "displayName": "اسم العرض", "description": "وصف", "ratio": "النسبة", "unnamed": "قناة غير مسماة", "groups": "مجموعة", "captchaPlaceholder": "ير<PERSON>ى إدخال رمز التحقق", "confirm": "تأكيد", "permissions": "الأذونات", "actions": "الإجراءات", "createdTime": "وقت الإنشاء", "expiredTime": "وقت انتهاء الصلاحية", "search": "ب<PERSON><PERSON>", "reset": "إعادة تعيين", "refresh": "تحديث", "pagination": {"total": "المجموع {{total}} عنصر"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "انقر لفتح الرابط"}, "userRole": {"normal": "مستخدم عادي", "agent": "وكيل", "admin": "مدير", "superAdmin": "المشرف العام", "loading": "جارٍ التحميل..."}, "channelStatus": {"enabled": "تفعيل", "disabled": "م<PERSON><PERSON><PERSON><PERSON>", "waitingRestart": "انتظر إعادة التشغيل", "waiting": "انتظار", "autoStoppedTitle": "تجاوز عدد المحاولات التلقائية للقناة الحد الأقصى أو تم تفعيل شرط الإيقاف التلقائي.", "stopped": "إيقا<PERSON> الاستخدام", "partiallyDisabled": "جز<PERSON> من التعطيل", "unknown": "غير معروف", "reason": "السبب"}, "channelBillingTypes": {"payAsYouGo": "الدفع حسب الاستخدام", "payPerRequest": "حس<PERSON> الاستخدام", "unknown": "طريقة غير معروفة"}, "tokenStatus": {"normal": "عادي", "disabled": "م<PERSON><PERSON><PERSON><PERSON>", "expired": "منتهي الصلاحية", "exhausted": "استنفاد", "unknown": "غير معروف"}, "userStatus": {"normal": "طبيعي", "banned": "<PERSON><PERSON><PERSON>", "unknown": "غير معروف"}, "redemptionStatus": {"normal": "طبيعي", "disabled": "ممنوع", "redeemed": "تم استبداله", "expired": "منتهي الصلاحية", "unknown": "غير معروف"}, "duration": {"request": "ط<PERSON><PERSON>", "firstByte": "الحر<PERSON> الأول", "total": "الإجمالي", "seconds": "ثانية", "lessThanOneSecond": "<1 ثانية"}, "streamType": {"stream": "تدفق", "nonStream": "غير متدفق"}, "noSet": {"title": "لم يقم المسؤول بتعيين {{name}}", "name": {"about": "حو<PERSON>", "chat": "<PERSON>و<PERSON><PERSON>"}}, "buttonText": {"add": "إضافة", "cancel": "إلغاء", "confirm": "تأكيد", "delete": "<PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "updateBalance": "تحديث الرصيد", "test": "اختبار", "multiple": "اختيار متعدد"}, "channelPage": {"title": "إدارة القنوات"}, "channelStatusCount": {"title": "إحصائيات حالة القناة", "summary": "مفعل {{enabled}} | معطل {{disabled}} | إعادة المحاولة {{retry}} | متوقف {{stopped}}", "statusEnabled": "تم تفعيله", "statusDisabled": "تم تعطيله", "statusRetry": "إعادة المحاولة", "statusStopped": "تم الإيقاف", "statusPartially": "معطل جزئياً"}, "header": {"routes": {"status": "الحالة", "home": "الصفحة الرئيسية", "chat": "<PERSON>و<PERSON><PERSON>", "pptGen": "إنشاء PPT", "chart": "إحصائيات", "agency": "وكيل", "channel": "قناة", "ability": "قدرة القناة", "channelGroup": "مجموعة القنوات", "token": "<PERSON><PERSON><PERSON>", "log": "سجل", "logDetail": "تفاصيل", "midjourney": "رسم", "user": "مستخدم", "config": "تكوين", "packagePlanAdmin": "إدارة الحزم", "redemption": "<PERSON><PERSON><PERSON> الاسترداد", "group": "مجموعة", "query": "استعلام", "about": "حو<PERSON>", "setting": {"default": "إعدادات", "operation": "إعدادات التشغيل", "system": "إعدادات النظام", "global": "الإعدادات العامة", "advance": "إعدادات الخصائص", "sensitive": "تكوين الكلمات الحساسة", "verification": "تكوين رمز التحقق", "update": "تحقق من التحديثات"}, "account": {"default": "ح<PERSON><PERSON><PERSON>", "profile": "المركز الشخصي", "cardTopup": "تباد<PERSON> كود البطاقة", "onlineTopup": "إعادة شحن عبر الإنترنت", "recharge": "إعادة شحن الرصيد", "balanceTransfer": "تحويل الرصيد", "pricing": "شرح التكاليف", "packagePlan": {"list": "شراء الباقة", "record": "سجل الشراء"}, "notificationSettings": "إعدادات الإشعارات"}, "tools": {"default": "أداة", "fileUpload": "رفع الملفات", "keyExtraction": "استخراج المفتاح", "multiplierCalculator": "حاسبة المضاعفات", "shortLink": "توليد الروابط القصيرة", "testConnection": "اختبار الوصول", "customPrompts": "إدارة كلمات التلميح", "redis": "تصوير Redis", "ratioCompare": "مقارنة النسب", "serverLog": "عارض سجلات الخادم"}, "onlineTopupRecord": "سجل الشحن", "channelScores": "نقاط القناة", "dynamicRouter": "التوجيه الديناميكي", "task": "المهام غير المتزامنة", "agencyJoin": "شراكة الوكالة"}, "dropdownMenu": {"profile": "المركز الشخصي", "recharge": "إعادة شحن الرصيد", "agencyCenter": "مركز الوكلاء", "checkin": "تسجيل الحضور", "darkMode": {"enable": "الوضع الداكن", "disable": "وضع النهار"}, "fullScreen": {"default": "تبديل إلى وضع ملء الشاشة", "enable": "وضع ملء الشاشة", "disable": "إخراج من وضع ملء الشاشة"}, "logout": "تسجيل الخروج"}, "checkin": {"default": "تسجيل الوصول", "success": "تم تسجيل الدخول بنجاح", "failed": "فشل تسجيل الدخول", "verification": "ير<PERSON>ى إكمال التحقق"}, "avatarProps": {"login": "تسجيل الدخول"}}, "settings": {"public": {"titles": {"default": "الإعدادات العامة"}, "SystemName": "اسم النظام", "ServerAddress": "عنوان الخدمة", "TopUpLink": "رابط الشحن", "ChatLink": "رابط المحادثة", "Logo": "شعار النظام", "HomePageContent": "محتوى الصفحة الرئيسية", "About": "<PERSON>و<PERSON> المح<PERSON>وى", "Notice": "محتوى الإعلان", "Footer": "محتوى التذييل", "RegisterInfo": "إشعار التسجيل", "HeaderScript": "رأس مخصص", "SiteDescription": "وصف الموقع", "PrivacyPolicy": "سياسة الخصوصية", "ServiceAgreement": "اتفاقية الخدمة", "FloatButton": {"FloatButtonEnabled": "فتح", "DocumentInfo": "معلومات الوثيقة", "WechatInfo": "رسالة وي شات", "QqInfo": "معلومات QQ"}, "CustomThemeConfig": "موضوع مخصص", "AppList": "روابط صداقة"}}, "home": {"default": {"title": "مرحبًا بك في الاستخدام", "subtitle": "استنادًا إلى تطوير ثانوي لـ One API، نقدم ميزات أكثر كمالًا.", "start": "ابد<PERSON> الاستخدام", "description": {"title": "ميزات جديدة:", "part1": "واجهة مستخدم جديدة كليًا، سهلة وسريعة.", "part2": "تحسين آلية الجدولة، فعالة ومستقرة", "part3": "مخصص لتطوير الشركات، آمن وموثوق", "part4": "المزيد من الميزات المتقدمة في انتظار اكتشافك"}}}, "dailyUsageChart": {"title": "استخدام النموذج اليومي", "yAxisName": "استخدام (دولار أمريكي)", "loadingTip": "استخدام يومي", "fetchError": "حد<PERSON> خطأ أثناء الحصول على بيانات الاستخدام اليومية:"}, "modelUsageChart": {"title": "استخدام النموذج", "hourlyTitle": "استخدام النموذج في كل ساعة", "dailyTitle": "استخدام النموذج اليومي", "weeklyTitle": "استخدام النموذج الأسبوعي", "monthlyTitle": "استخدام النموذج شهريًا"}, "granularity": {"hour": "كل ساعة", "day": "يومياً", "week": "كل أسبوع", "month": "كل شهر", "all": "الكل"}, "abilitiesTable": {"title": "قدرة القناة", "export": "تصدير", "group": "مجموعة", "model": "نموذج", "channelId": "رقم القناة", "enabled": "تم تفعيله", "weight": "الوزن", "priority": "الأولوية", "billingType": "نوع الفوترة", "functionCallEnabled": "تم تفعيل استدعاء الوظائف", "imageSupported": "دعم الصورة", "yes": "نعم", "no": "لا", "perToken": "<PERSON><PERSON><PERSON> ع<PERSON>د التوكنات", "perRequest": "الفوترة حسب الطلب", "noDataToExport": "لا توجد بيانات يمكن تصديرها.", "exportConfirm": "هل أنت متأكد أنك تريد تصدير بيانات الصفحة الحالية؟", "exportSuccess": "تم التصدير بنجاح", "toggleSuccess": "تم التبديل بنجاح", "toggleError": "فشل التبديل", "selectOrInputGroup": "اختر أو أدخل مجموعة المستخدمين"}, "logsTable": {"retry": "إعادة المحاولة", "retryChannelList": "قائمة قنوات إعادة المحاولة", "retryDurations": "تفاصيل الوقت المستغرق لإعادة المحاولة", "channel": "قناة", "duration": "الوقت المستغرق", "startTime": "وقت البدء", "endTime": "وقت الانتهاء", "retryCount": "ع<PERSON><PERSON> محاو<PERSON><PERSON><PERSON> إعادة التشغيل", "retryDetails": "تفاصيل إعادة المحاولة", "totalRetryTime": "إجمالي وقت إعادة المحاولة", "seconds": "ثانية", "tokenGroup": "مجموعة الرموز", "selectGroup": "اختيار المجموعات", "dailyModelUsageStats": "عرض بيانات الاستدعاء", "time": "الوقت", "moreInfo": "مزيد من المعلومات", "ip": "عنوان بروتوكول الإنترنت", "remoteIp": "عنوان IP البعيد", "ipTooltip": "IP: {{ip}}  \nعنوان IP البعيد: {{remoteIp}}", "requestId": "طلب ID", "username": "اسم المستخدم", "userId": "معرّف المستخدم", "tokenName": "اسم الرمز", "token": "<PERSON><PERSON><PERSON>", "type": "نوع", "typeUnknown": "غير معروف", "type充值": "إعادة شحن", "type消费": "الاستهلاك", "type管理": "إدارة", "type系统": "نظام", "type邀请": "دعوة", "type提示": "تلميح", "type警告": "تحذير", "type错误": "خطأ", "type签到": "تسجيل الوصول", "type日志": "سجل", "type退款": "استرداد الأموال", "type邀请奖励金划转": "تحويل مكافأة الدعوة", "type代理奖励": "مكافأة الوكيل", "type下游错误": "خطأ في المنبع", "type测试渠道": "قناة الاختبار", "typeRecharge": "إعادة شحن", "typeConsumption": "الاستهلاك", "typeManagement": "إدارة", "typeSystem": "نظام", "typeInvitation": "دعوة", "typePrompt": "تلميح", "typeWarning": "تحذير", "typeError": "خطأ", "typeCheckin": "تسجيل الوصول", "typeLog": "سجل", "typeRefund": "استرداد المال", "typeInviteReward": "تحويل مكافأة الدعوة", "typeAgencyBonus": "مكافأة الوكلاء", "typeDownstreamError": "خطأ في المصب", "typeChannelTest": "اختبار القناة", "channelId": "معرف القناة", "channelName": "اسم القناة", "model": "نموذج", "modelPlaceholder": "أدخل/اختر اسم النموذج", "info": "معلومات", "isStream": "تدفق", "isStreamPlaceholder": "إدخال/اختيار ما إذا كان يتدفق", "prompt": "تلميح", "completion": "إكمال", "consumption": "الاستهلاك", "consumptionRange": "نطاق حد الاستهلاك", "description": "شرح", "action": "عملية", "details": "تفاصيل", "tokenKey": "م<PERSON><PERSON><PERSON><PERSON> الرمز", "requestDuration": "وقت الطلب", "firstByteDuration": "وقت استجابة البايت الأول", "totalDuration": "إجمالي الوقت المستغرق", "lessThanOneSecond": "<1 ثانية", "modelInvocation": "استدعاء النموذج", "modelUsage": "استخدام النموذج", "totalQuota": "إجمالي حد الاستهلاك: {{quota}}", "totalRpm": "عدد الطلبات في الدقيقة: {{rpm}}", "totalTpm": "عدد الرموز/الدقيقة: {{tpm}}", "totalMpm": "المبلغ/الدقيقة: {{mpm}}", "dailyEstimate": "الاستهلاك اليومي المتوقع: {{estimate}}", "currentStats": "RPM الحالي: {{rpm}} TPM الحالي: {{tpm}} MPM الحالي: ${{mpm}} الاستهلاك اليومي المقدر: ${{dailyEstimate}}", "statsTooltip": "فقط احصاء السجلات غير المؤرشفة، RPM: عدد الطلبات في الدقيقة، TPM: عدد الرموز في الدقيقة، MPM: المال المستهلك في الدقيقة، التقدير اليومي للاستهلاك مستند إلى MPM الحالي.", "showAll": "عر<PERSON> كامل", "exportConfirm": "تصدير سجل هذه الصفحة؟", "export": "تصدير", "statsData": "البيانات الإحصائية", "today": "في ذلك اليوم", "lastHour": "ساعة واحدة", "last3Hours": "3 ساعات", "lastDay": "1 يوم", "last3Days": "3 أيام", "last7Days": "7 أيام", "lastMonth": "شهر واحد", "last3Months": "3 أ<PERSON><PERSON><PERSON>", "excludeModels": "نموذج الاستبعاد", "selectModelsToExclude": "اختر النموذج الذي تريد استبعاده", "excludeErrorCodes": "استبعاد رمز الخطأ", "excludeErrorCodesPlaceholder": "اختر رموز الأخطاء التي تريد استبعادها", "errorCode": "<PERSON><PERSON><PERSON> الخطأ", "errorCodePlaceholder": "إدخال/اختيار رمز الخطأ", "timezoneTip": "المنطقة الزمنية الحالية: {timezone}", "timezoneNote": "تلميح المنطقة الزمنية", "timezoneDescription": "تُجمع البيانات الإحصائية حسب المنطقة الزمنية الحالية الخاصة بك. قد تؤدي المناطق الزمنية المختلفة إلى اختلاف فترات تجميع البيانات. إذا كنت بحاجة إلى التعديل، يرجى زيارة مركزك الشخصي لتغيير إعدادات المنطقة الزمنية.", "goToProfile": "اذه<PERSON> إلى المركز الشخصي", "realtimeQuota": "الاستهلاك الفوري (1 دقيقة)", "viewTotalQuota": "عرض إجمالي الإنفاق", "viewTotalQuotaTip": "عرض إجمالي مبلغ الاستهلاك التاريخي (قد يستغرق الاستعلام بضع ثوانٍ)", "loadingTotalQuota": "جارٍ استعلام عن إجمالي مبلغ الاستهلاك، يرجى الانتظار...", "totalQuotaTitle": "إحصائيات الاستهلاك التاريخي", "loadTotalQuotaError": "فشل في الحصول على إجمالي مبلغ الاستهلاك", "requestLogs": "سجل الطلب - {{requestId}}", "noRequestLogs": "لا توجد سجلات طلبات حالياً", "metricsExplanation": "فقط احصاء السجلات غير المؤرشفة، RPM: عدد الطلبات في الدقيقة، TPM: عدد الرموز في الدقيقة، MPM: المال المستهلك في الدقيقة، التقدير اليومي للاستهلاك مستند إلى MPM الحالي.", "autoRefresh": "تحديث تلقائي", "autoRefreshTip": "انقر لتفعيل/إلغاء تفعيل التحديث التلقائي، عند التفعيل سيتم تحديث البيانات تلقائيًا كل عدد محدد من الثواني.", "autoRefreshOn": "تم تفعيل التحديث التلقائي", "autoRefreshOff": "تم إيقاف التحديث التلقائي", "refreshInterval": "فترة التحديث", "stopRefresh": "توقف عن التحديث", "secondsWithValue": "{{seconds}} ثانية", "minutesWithValue": "{{minutes}} دقيقة"}, "mjLogs": {"logId": "معرّف السجل", "submitTime": "موع<PERSON> التسليم", "type": "نوع", "channelId": "معرّف القناة", "userId": "معرف المستخدم", "taskId": "معرّف المهمة", "submit": "تقديم", "status": "الحالة", "progress": "التقدم", "duration": "الوقت المستغرق", "result": "نتيجة", "prompt": "ط<PERSON><PERSON>", "promptEn": "موجه إن", "failReason": "أس<PERSON>اب الفشل", "startTime": "وقت البدء", "endTime": "وقت الانتهاء", "today": "في ذلك اليوم", "lastHour": "ساعة واحدة", "last3Hours": "3 ساعات", "lastDay": "1 يوم", "last3Days": "3 أيام", "last7Days": "7 أيام", "lastMonth": "شهر واحد", "last3Months": "3 أ<PERSON><PERSON><PERSON>", "selectTaskType": "اختيار نوع المهمة", "selectSubmitStatus": "اختيار حالة التقديم", "submitSuccess": "تم الإرسال بنجاح", "queueing": "في انتظار الدور", "duplicateSubmit": "إعادة تقديم الطلب", "selectTaskStatus": "اختر حالة المهمة", "success": "نجاح", "waiting": "انتظار", "queued": "الانتظار في الطابور", "executing": "تنفيذ", "failed": "فشل", "seconds": "ثانية", "unknown": "غير معروف", "viewImage": "انقر لرؤية", "markdownFormat": "تنسيق ماركداون", "midjourneyTaskId": "معرّف مهمة ميدجورني", "copiedAsMarkdown": "تم نسخه بتنسيق Markdown", "copyFailed": "فشل النسخ", "copiedMidjourneyTaskId": "تم نسخ معرف مهمة Midjourney", "drawingLogs": "سجل الرسم", "onlyUnarchived": "إحصاء السجلات التي لم يتم أرشفتها فقط", "imagePreview": "معاينة الصورة", "copiedImageUrl": "تم نسخ عنوان الصورة", "copy": "نسخ", "download": "تحميل", "resultImage": "صورة النتيجة", "downloadError": "فشل في تحميل الصورة", "mode": "نموذج", "selectMode": "اختيار الوضع", "relax": "وضع السهولة", "fast": "وضع سريع", "turbo": "وضع السرعة القصوى", "actions": "عملية", "refresh": "تحديث", "refreshSuccess": "تم تحديث حالة المهمة بنجاح", "refreshFailed": "فشل تحديث حالة المهمة", "refreshError": "حد<PERSON> خطأ أثناء تحديث حالة المهمة", "tasks": {"title": "قائمة المهام", "taskId": "معرف المهمة", "platform": "المنصة", "type": "النوع", "status": "الحالة", "progress": "التقدم", "submitTime": "وقت الإرسال", "startTime": "وقت البداية", "endTime": "وقت الانتهاء", "duration": "المدة", "result": "النتيجة", "taskIdPlaceholder": "أدخل معرف المهمة", "platformPlaceholder": "اختر المنصة", "typePlaceholder": "اختر النوع", "statusPlaceholder": "اختر الحالة", "videoGeneration": "توليد الفيديو", "imageGeneration": "توليد الصورة", "musicGeneration": "توليد الموسيقى", "textGeneration": "توليد النص", "unknown": "غير معروف", "success": "نجح", "failed": "فشل", "inProgress": "قيد التقدم", "submitted": "تم الإرسال", "queued": "في الطابور", "notStarted": "لم يبدأ", "viewResult": "عرض النتيجة", "viewError": "عرض الخطأ", "taskDetails": "تفاصيل المهمة", "errorDetails": "تفاصيل الخطأ", "loadError": "خطأ في التحميل"}, "viewVideo": "عرض الفيديو", "videoPreview": "معاينة الفيديو", "copyVideoUrl": "نسخ رابط الفيديو", "copiedVideoUrl": "تم نسخ رابط الفيديو", "downloadVideo": "تحميل الفيديو", "videoNotSupported": "الفيديو غير مدعوم", "videoUrl": "رابط الفيديو", "videoUrls": "روابط الفيديو"}, "mjTaskType": {"IMAGINE": "إنشاء صورة", "UPSCALE": "تكبير", "VARIATION": "تحويل", "REROLL": "إعادة توليد", "DESCRIBE": "الصورة تخلق النص", "BLEND": "خليط الصور", "OUTPAINT": "زوم", "DEFAULT": "غير معروف"}, "mjCode": {"submitSuccess": "تم الإرسال بنجاح", "queueing": "في انتظار الدور", "duplicateSubmit": "إعادة تقديم الطلب", "unknown": "غير معروف"}, "mjStatus": {"success": "نجاح", "waiting": "انتظار", "queued": "الانتظار في الطابور", "executing": "تنفيذ", "failed": "فشل", "unknown": "غير معروف"}, "tokensTable": {"title": "إدارة الرموز", "table": {"title": "إدارة الرموز", "toolBar": {"add": "إنشاء رمز جديد", "delete": "<PERSON><PERSON><PERSON> ال<PERSON><PERSON>ز", "deleteConfirm": "يتم حذف {{count}} من الرموز بشكل جماعي، هذه العملية غير قابلة للتراجع.", "export": "تصدير", "exportConfirm": "تصدير رمز الصفحة الحالية؟"}, "action": "عملية"}, "modal": {"title": {"add": "إنشاء رمز جديد", "edit": "<PERSON><PERSON><PERSON> التحرير"}, "field": {"name": "اسم الرمز", "description": "وصف الرمز", "type": {"default": "طريقة الفوترة", "type1": "الدفع حسب الاستخدام", "type2": "حس<PERSON> الاستخدام", "type3": "التحصيل المختلط", "type4": "حسب الكمية أولوية", "type5": "حسب الأولوية الثانية"}, "status": "الحالة", "statusEnabled": "طبيعي", "statusDisabled": "ممنوع", "statusExpired": "منتهي الصلاحية", "statusExhausted": "استنفاد", "models": "النماذج المتاحة", "usedQuota": "ح<PERSON> الاستهلاك", "remainQuota": "الر<PERSON>ي<PERSON> المتبقي", "createdTime": "تاريخ الإنشاء", "expiredTime": "تاريخ انتهاء الصلاحية", "all": "الكل", "more": "المزيد", "notEnabled": "غير مفعل", "unlimited": "<PERSON>ير محدود", "daysLeft": "تنتهي صلاحيتها بعد {{days}} يومًا", "expired": "منتهية منذ {{days}} يوم", "userId": "معرف المستخدم", "key": "مفتاح API", "neverExpire": "لا تنتهي أبداً"}, "delete": {"title": "<PERSON><PERSON><PERSON>", "content": "هل أنت متأكد أنك تريد حذف مفتاح API {{name}}؟"}, "footer": {"cancel": "إلغاء", "confirm": "تأكيد", "update": "تحديث"}, "bridge": {"title": "تواصل سريع مع القنوات", "placeholder": "ير<PERSON>ى إدخال عنوان خدمة {{name}} الخاص بك"}, "copy": {"title": "نسخ يدوي"}}, "dropdown": {"onlineChat": "محادثة عبر الإنترنت", "disableToken": "تعطيل الرمز", "enableToken": "تفعيل الرمز", "editToken": "<PERSON><PERSON><PERSON> التحرير", "requestExample": "طل<PERSON> مثال", "tokenLog": "سجل الرمز", "shareToken": "ر<PERSON>ز المشاركة", "quickIntegration": "توصيل بنقرة واحدة"}, "error": {"fetchModelsFailed": "فشل في الحصول على النموذج: {{message}}", "batchDeleteFailed": "فشل حذف الدفعة: {{message}}", "deleteTokenFailed": "فشل حذف الرمز: {{message}}", "refreshTokenFailed": "فشل تجديد الرمز: {{message}}", "enableTokenFailed": "فشل تفعيل الرمز: {{message}}", "disableTokenFailed": "فشل في تعطيل الرمز: {{message}}", "fetchDataFailed": "فشل في الحصول على البيانات: {{message}}"}, "success": {"batchDelete": "تم حذف {{count}} رمز بنجاح", "shareTextCopied": "تم نسخ النص إلى الحافظة", "tokenCopied": "تم نسخ الرمز إلى الحافظة", "deleteToken": "تم حذف الر<PERSON>ز بنجاح", "refreshToken": "تم تجديد الر<PERSON>ز بنجاح", "enableToken": "تم تفعيل الرمز بنجاح", "disableToken": "تم تعطيل الرمز بنجاح", "export": "تم تصدير رمز الصفحة الحالية بنجاح"}, "warning": {"copyFailed": "فشل النسخ، يرجى النسخ يدويًا.", "invalidServerAddress": "ير<PERSON>ى إدخال عنوان الخادم الصحيح"}, "info": {"openingBridgePage": "جارٍ فتح صفحة الربط، وقد تم نسخ الرمز لك."}, "export": {"name": "اسم", "key": "مفتاح", "billingType": "طريقة الفوترة", "status": "الحالة", "models": "النماذج المتاحة", "usedQuota": "ح<PERSON> الاستهلاك", "remainQuota": "الر<PERSON>ي<PERSON> المتبقي", "createdTime": "تاريخ الإنشاء", "expiredTime": "تاريخ انتهاء الصلاحية", "unlimited": "<PERSON>ير محدود", "neverExpire": "لا تنتهي أبداً"}, "billingType": {"1": "الدفع حسب الاستخدام", "2": "حس<PERSON> الاستخدام", "3": "التحصيل المختلط", "4": "حسب الكمية أولوية", "5": "حسب الأولوية الثانية"}, "bridge": {"quickIntegration": "توصيل بنقرة واحدة"}}, "editTokenModal": {"editTitle": "<PERSON><PERSON><PERSON> التحرير", "createTitle": "إنشاء رمز", "defaultTokenName": "رمز {{username}} بتاريخ {{date}}", "tokenName": "اسم الرمز", "unlimitedQuota": "حدود غير محدودة", "remainingQuota": "الر<PERSON>ي<PERSON> المتبقي", "authorizedQuota": "<PERSON><PERSON> الا<PERSON><PERSON><PERSON>ان", "quotaLimitNote": "الحد الأق<PERSON>ى لمبلغ الرمز المميز المتاح محدود برصيد الحساب.", "quickOptions": "خيارات سريعة", "neverExpire": "لا تنتهي أبداً", "expiryTime": "تاريخ انتهاء الصلاحية", "billingMode": "نموذج الفوترة", "selectGroup": "اختيار المجموعات", "switchGroup": "اختيار المجموعات", "switchGroupTooltip": "اختر المجموعة التي ينتمي إليها الرمز، حيث أن المجموعات المختلفة لها تسعيرات وصلاحيات وظيفية مختلفة. إذا لم تقم بالاختيار، فسيتم استخدام المجموعة التي ينتمي إليها المستخدم الحالي بشكل افتراضي.", "switchGroupHint": "اختيار المجموعة سيؤثر على معدل الفوترة للرموز والنماذج المتاحة، يرجى الاختيار بناءً على الاحتياجات الفعلية.", "importantFeature": "مهم", "tokenRemark": "ملاحظات الرمز", "discordProxy": "وكيل ديسكورد", "enableAdvancedOptions": "تفعيل الخيارات المتقدمة", "generationAmount": "<PERSON><PERSON><PERSON> الإنتاج", "availableModels": "النماذج المتاحة", "selectModels": "اختر/ابحث/أضف نموذج متاح، تركه فارغًا يعني عدم وجود قيود", "activateOnFirstUse": "تفعيل الاستخدام الأول", "activateOnFirstUseTooltip": "إذا تم تفعيل فترة صلاحية التنشيط بعد الاستخدام الأول، وإذا تم تفعيل هذا الخيار وتم التنشيط من خلال الاستخدام الأول، فسيتم تجاوز فترة صلاحية الرمز المميز المكونة أعلاه.", "activationValidPeriod": "مدة صلاحية التفعيل", "activationValidPeriodTooltip": "مدة صلاحية تفعيل الرمز بعد الاستخدام الأول (بالأيام)", "ipWhitelist": "قائمة بيضاء لعنوان IP", "ipWhitelistPlaceholder": "عنوان IP (النطاق)، يدعم IPV4 و IPV6، يفصل بين المتعدد بفواصل.", "rateLimiter": "محد<PERSON> التدفق", "rateLimitPeriod": "دورة الحد من التدفق", "rateLimitPeriodTooltip": "فترة الحد من التدفق (الوحدة: ثانية)", "rateLimitCount": "عدد مرات الحد من التدفق", "rateLimitCountTooltip": "عدد المرات المتاحة خلال فترة الحد من التدفق", "promptMessage": "رسالة تنبيه", "promptMessageTooltip": "رسالة التنبيه عند تجاوز حد التدفق", "promotionPosition": "موقع الترويج", "promotionPositionStart": "البداية", "promotionPositionEnd": "النهاية", "promotionPositionRandom": "عشوائي", "promotionContent": "محتوى ترويجي", "currentGroup": "المجموعة الحالية", "searchGroupPlaceholder": "ابحث عن اسم المجموعة أو الوصف أو المضاعف...", "mjTranslateConfig": "تكوين ترجمة MJ", "mjTranslateConfigTip": "ترتيب الترجمة الذي ينطبق فقط على كلمات مفتاحية لـ Midjourney", "mjTranslateBaseUrlPlaceholder": "يرجى إدخال عنوان URL الأساسي لخدمة الترجمة.", "mjTranslateApiKeyPlaceholder": "يرجى إدخال مفتاح API لخدمة الترجمة.", "mjTranslateModelPlaceholder": "يرجى إدخال اسم النموذج المستخدم في خدمة الترجمة.", "mjTranslateBaseUrlRequired": "يجب تقديم عنوان URL الأساسي عند تفعيل الترجمة.", "mjTranslateApiKeyRequired": "يجب تقديم مفتاح API عند تفعيل الترجمة.", "mjTranslateModelRequired": "يجب تقديم اسم النموذج عند تفعيل الترجمة."}, "addTokenQuotaModal": {"title": "إدارة رصيد الرمز {{username}}", "defaultReason": "إجراءات المشرف", "enterRechargeAmount": "<PERSON><PERSON><PERSON><PERSON> إدخال مبلغ الشحن", "enterRemark": "ير<PERSON>ى إدخال رسالة ملاحظة", "confirmOperation": "تأكيد العملية", "confirmContent": "هل تؤكد أن {{username}} {{action}} بمبلغ {{amount}} دولار {{updateExpiry}}؟", "recharge": "إعادة شحن", "deduct": "خصم", "andUpdateExpiry": "وتحديث صلاحية الرصيد إلى {{days}} يومًا", "alertMessage": "يمكن إدخال أعداد سالبة لخصم رصيد المستخدم.", "rechargeAmount": "<PERSON><PERSON><PERSON><PERSON> الشحن", "operationReason": "سبب التشغيل", "finalBalance": "الرصيد النهائي"}, "billingType": {"1": "الدفع حسب الاستخدام", "2": "حس<PERSON> الاستخدام", "3": "التحصيل المختلط", "4": "حسب الكمية أولوية", "5": "حسب الأولوية الثانية", "payAsYouGo": "الدفع حسب الاستخدام", "payPerRequest": "حس<PERSON> الاستخدام", "hybrid": "التحصيل المختلط", "payAsYouGoPriority": "حسب الكمية أولوية", "payPerRequestPriority": "حسب الأولوية الثانية", "unknown": "طريقة غير معروفة"}, "packagePlanAdmin": {"title": "إدارة الحزم", "table": {"title": "إدارة الحزم", "toolBar": {"add": "إنشاء حزمة جديدة", "delete": "إلغاء الباقة"}, "action": {"edit": "<PERSON><PERSON><PERSON><PERSON>", "delete": "<PERSON><PERSON><PERSON>", "detail": "تفاصيل", "recovery": "إدراج", "offline": "إزالة المنتج"}}, "modal": {"title": {"add": "إنشاء باقة جديدة", "edit": "تحرير الحزمة"}, "field": {"name": "اسم الباقة", "type": {"default": "نوع الحزمة", "type1": "حزمة الائتمان", "type2": "باقة الدفع حسب الاستخدام", "type3": "باقة المدة"}, "group": "مجموعة الباقات", "description": "وصف الباقة", "price": "أسعار الباقات", "valid_period": "صلاحية", "first_buy_discount": "خصم الشراء الأول", "rate_limit_num": "تح<PERSON><PERSON><PERSON> عدد المرات", "rate_limit_duration": "فترة محدودة", "inventory": "مخزون الحزمة", "available_models": "النماذج المتاحة", "quota": "حصة الباقة", "times": "<PERSON><PERSON><PERSON> الوجبات"}, "footer": {"cancel": "إلغاء", "confirm": "تأكيد", "update": "تحديث"}}}, "login": {"title": "تسجيل الدخول", "username": "اسم المستخدم", "password": "كلمة المرور", "login": "تسجيل الدخول", "otherLoginMethods": "طرق تسجيل دخول أخرى", "register": "تسجيل حساب", "accountLogin": "تسجيل الدخول إلى الحساب", "phoneLogin": "تسجيل الدخول برقم الهاتف المحمول", "usernamePlaceholder": "اسم المستخدم", "usernameRequired": "ير<PERSON>ى إدخال اسم المستخدم!", "passwordPlaceholder": "كلمة المرور", "passwordRequired": "ير<PERSON>ى إدخال كلمة المرور!", "passwordMaxLength": "لا يمكن أن يتجاوز طول كلمة المرور 20 حرفًا!", "phonePlaceholder": "رقم الهات<PERSON> المحمول", "phoneRequired": "ير<PERSON>ى إدخال رقم الهاتف!", "phoneFormatError": "رقم الهاتف غير صحيح!", "smsCodePlaceholder": "ر<PERSON>ز التحقق عبر الرسائل القصيرة", "smsCodeCountdown": "إعادة الحصول بعد {{count}} ثانية", "getSmsCode": "الحصول على رمز التحقق", "agreementText": "أوافق", "privacyPolicy": "سياسة الخصوصية", "and": "و", "serviceAgreement": "اتفاقية الخدمة", "alreadyLoggedIn": "لقد قمت بتسجيل الدخول", "weakPasswordWarning": "كلمة مرورك بسيطة جداً، يرجى تعديلها في أقرب وقت!", "welcomeMessage": "مرحبًا بك في الاستخدام", "captchaError": "رمز التحقق غير صحيح", "credentialsError": "اسم المستخدم أو كلمة المرور غير صحيحة", "resetPassword": "إعادة تعيين كلمة المرور", "captchaExpired": "رمز التحقق غير موجود أو انتهت صلاحيته", "loginFailed": "فشل تسجيل الدخول: {{message}}", "captchaRequired": "ير<PERSON>ى إدخال رمز التحقق!", "captchaPlaceholder": "<PERSON><PERSON><PERSON> التحقق", "smsSent": "تم إرسال رمز التحقق عبر الرسائل القصيرة بنجاح", "smsSendFailed": "فشل إرسال رمز التحقق عبر الرسائل القصيرة", "agreementWarning": "يرجى أولاً الموافقة على \"سياسة الخصوصية\" و \"اتفاقية الخدمة\"", "turnstileWarning": "يرجى المحاولة مرة أخرى لاحقًا، Turnstile يقوم بفحص بيئة المستخدم!", "loginSuccess": "تسجيل الدخول ناجح"}, "register": {"title": "تسجيل", "usernameRequired": "ير<PERSON>ى إدخال اسم المستخدم!", "usernameNoAt": "اسم المستخدم لا يمكن أن يحتوي على الرمز @.", "usernameNoChinese": "اسم المستخدم لا يمكن أن يحتوي على أحرف صينية.", "usernameLength": "يجب أن يتراوح طول اسم المستخدم بين 4 و 12 حرفًا.", "usernamePlaceholder": "اسم المستخدم", "passwordRequired": "ير<PERSON>ى إدخال كلمة المرور!", "passwordLength": "يجب أن يتكون طول كلمة المرور من 8 إلى 20 حرفًا", "passwordPlaceholder": "كلمة المرور", "confirmPasswordRequired": "يرجى تأكيد كلمة المرور!", "passwordMismatch": "كلمتا المرور المدخلة غير متطابقتين!", "confirmPasswordPlaceholder": "تأكيد كلمة المرور", "emailInvalid": "ير<PERSON>ى إدخال عنوان بريد إلكتروني صالح!", "emailRequired": "ير<PERSON>ى إدخال البريد الإلكتروني!", "emailPlaceholder": "عنوان البريد الإلكتروني", "emailCodeRequired": "ير<PERSON>ى إدخال رمز التحقق عبر البريد الإلكتروني!", "emailCodePlaceholder": "رمز التحقق عبر البريد الإلكتروني", "enterCaptcha": "ير<PERSON>ى إدخال رمز التحقق", "resendEmailCode": "إعادة الإرسال بعد {{seconds}} ثانية", "getEmailCode": "الحصول على رمز التحقق", "phoneRequired": "ير<PERSON>ى إدخال رقم الهاتف المحمول!", "phoneInvalid": "رقم الهاتف غير صحيح!", "phonePlaceholder": "رقم الهات<PERSON> المحمول", "smsCodeRequired": "ير<PERSON>ى إدخال رمز التحقق عبر الرسائل القصيرة!", "smsCodePlaceholder": "ر<PERSON>ز التحقق عبر الرسائل القصيرة", "resendSmsCode": "إعادة الإرسال بعد {{seconds}} ثانية", "getSmsCode": "الحصول على رمز التحقق", "captchaRequired": "ير<PERSON>ى إدخال رمز التحقق!", "captchaPlaceholder": "<PERSON><PERSON><PERSON> التحقق", "inviteCodePlaceholder": "رمز الدعوة (اختياري)", "submit": "تسجيل", "successMessage": "تم التسجيل بنجاح", "failMessage": "فشل التسجيل", "emailCodeSent": "تم إرسال رمز التحقق إلى البريد الإلكتروني", "smsCodeSent": "تم إرسال رمز التحقق عبر الرسائل القصيرة", "confirm": "تأكيد", "emailVerifyTitle": "تحقق من البريد الإلكتروني", "smsVerifyTitle": "تحقق عبر الرسائل القصيرة", "registerVerifyTitle": "تحقق التسجيل"}, "profile": {"timezone": "المنطقة الزمنية", "phoneNumber": "رقم الهات<PERSON> المحمول", "emailAddress": "عنوان البريد الإلكتروني", "wechatAccount": "حساب وي تشات", "telegramAccount": "حساب تيليجرام", "bindTelegram": "ربط تيليجرام", "balanceValidPeriod": "صلاحية الرصيد", "lastLoginIP": "آخر عنوان IP تم تسجيل الدخول به", "lastLoginTime": "آخر وقت تسجيل دخول", "inviteCode": "رمز الدعوة", "inviteLink": "رابط الدعوة", "generate": "توليد", "pendingEarnings": "الإيرادات المتوقعة للاستخدام", "transfer": "تحويل", "totalEarnings": "الإيرادات الإجمالية", "accountBalance": "ر<PERSON>ي<PERSON> الحساب", "totalConsumption": "الاستهلاك التراكمي", "callCount": "عد<PERSON> الاستدعاءات", "invitedUsers": "دعوة المستخدم", "promotionInfo": "معلومات الترويج", "inviteDescription": "دعوة واحدة، عائد مدى الحياة، كلما زادت الدعوات، زاد العائد.", "userInfo": "معلومات المستخدم", "availableModels": "النماذج المتاحة", "modelNameCopied": "تم نسخ اسم النموذج", "noAvailableModels": "لا توجد نماذج متاحة حالياً.", "accountOptions": "خيارات الحساب", "changePassword": "تغيير كلمة المرور", "systemToken": "ر<PERSON>ز النظام", "unsubscribe": "إلغاء التسجيل", "educationCertification": "اعتماد التعليم", "timezoneUpdateSuccess": "تم تحديث المنطقة الزمنية بنجاح", "inviteLinkCopied": "تم نسخ رابط الدعوة", "inviteLinkCopyFailed": "فشل في نسخ رابط الدعوة", "inviteLinkGenerationFailed": "فشل في إنشاء رابط الدعوة", "allModelsCopied": "تم نسخ جميع النماذج إلى الحافظة", "copyAllModels": "نسخ جميع النماذج", "totalModels": "ع<PERSON><PERSON> النماذج المتاحة", "expired": "منتهي الصلاحية", "validPeriod": "مدة الصلاحية", "longTermValid": "صالح لفترة طويلة", "failedToLoadModels": "فشل في تحميل قائمة النماذج", "accessTokens": "رموز الوصول", "accessTokensManagement": "إدارة رموز الوصول", "accessTokenDescription": "رموز الوصول تسمح للتطبيقات الخارجية بالوصول إلى حسابك", "tokenNameLabel": "اسم الرمز", "tokenNamePlaceholder": "أد<PERSON>ل اسم الرمز", "presetPermissions": "الأذونات المحددة مسبقاً", "detailPermissions": "الأذونات التفصيلية", "validityPeriod": "فترة الصلاحية", "validityPeriodExtra": "فترة الصلاحية الإضافية", "remarkLabel": "ملاحظة", "remarkPlaceholder": "يرجى الإدخال...", "createNewToken": "إنشاء رمز جديد", "tokenCreatedSuccess": "تم إنشاء الرمز بنجاح", "tokenSavePrompt": "ير<PERSON>ى حفظ الرمز في مكان آمن", "copyToken": "نسخ الرمز", "readPermission": "إذن القراءة", "writePermission": "إذن الكتابة", "deletePermission": "<PERSON><PERSON><PERSON> الحذف", "tokenManagement": "إدارة الرموز", "channelManagement": "إدارة القنوات", "logView": "عرض السجلات", "statisticsView": "عرض الإحصائيات", "userManagement": "إدارة المستخدمين", "quotaManagement": "إدارة الحصص", "readOnlyPermission": "إذن القراءة فقط", "writeOnlyPermission": "إذن الكتابة فقط", "readWritePermission": "إذن القراءة والكتابة", "standardPermission": "الإذن القياسي", "fullPermission": "الإذن الكامل", "selectPermission": "اختر الإذن", "tokenStatus": "حالة الرمز", "tokenEnabled": "م<PERSON>عل", "tokenDisabled": "معطل", "enableToken": "تفعيل الرمز", "disableToken": "تعطيل الرمز", "deleteToken": "<PERSON><PERSON><PERSON> ال<PERSON><PERSON>ز", "deleteTokenConfirm": "ت<PERSON><PERSON>يد حذف الرمز", "disableTokenConfirm": "تأكيد تعطيل الرمز", "enableTokenConfirm": "تأ<PERSON>يد تفعيل الرمز", "tokenExpiryNever": "لا ينتهي أبداً", "accessTokensInfo": "معلومات رموز الوصول", "accessTokensInfoDetail1": "رموز الوصول تسمح للتطبيقات بالوصول إلى حسابك", "accessTokensInfoDetail2": "يرجى الاحتفاظ برموز الوصول في مكان آمن", "accessTokensInfoDetail3": "يمكنك إلغاء رموز الوصول في أي وقت", "accessTokensInfoDetail4": "رموز الوصول لها صلاحيات محدودة", "accessTokensInfoDetail5": "يرجى مراجعة الأذونات بعناية قبل الإنشاء", "noPermission": "لا يوجد إذن لتنفيذ هذه العملية"}, "topup": {"onlineRecharge": "إعادة شحن عبر الإنترنت", "cardRedemption": "كو<PERSON> الاسترداد", "accountBalance": "ر<PERSON>ي<PERSON> الحساب", "rechargeReminder": "تذكير بشحن الرصيد", "reminder1": "1. يمكن استخدام الرصيد لاستدعاء النماذج، شراء الحزم، وغيرها.", "reminder2": "2. إذا لم يتم استلام المبلغ بعد الدفع، يرجى الاتصال بخدمة العملاء لمعالجة الأمر.", "reminder3": "3. الرصيد لا يدعم السحب، ولكن يمكن تحويله داخل نفس مجموعة المستخدمين.", "reminder4WithTransfer": "بعد نجاح عملية الشحن، سيتم إعادة تعيين صلاحية رصيد الحساب إلى", "reminder4WithoutTransfer": "بعد نجاح عملية الشحن، سيتم إعادة تعيين فترة صلاحية رصيد الحساب إلى", "days": "السماء", "paymentSuccess": "تم الدفع بنجاح", "paymentError": "حدث خطأ في الدفع", "paymentAmount": "مبلغ الدفع:", "purchaseAmount": "حد الشراء: $", "yuan": "يوان", "or": "أو", "usd": "دولار أمريكي", "cny": "يوان", "enterAmount": "ير<PERSON>ى إدخال مبلغ الشحن!", "amountPlaceholder": "ير<PERSON>ى إدخال مبلغ الشحن، ابتداءً من {{min}} دولار أمريكي.", "amountUpdateError": "حد<PERSON> خطأ أثناء تحديث المبلغ", "alipay": "أليباي", "wechat": "وي شين", "visaMastercard": "فيزا / ماستركارد", "cardFormatError": "تنسيق رمز الاسترداد غير صحيح", "redeemSuccess": "تم تحويل {{amount}} بنجاح!", "redeemError": "حدث خطأ في الاستبدال، يرجى المحاولة لاحقًا.", "enterCardKey": "ير<PERSON>ى إدخال رمز القسيمة", "cardKeyPlaceholder": "ير<PERSON>ى إدخال رمز القسيمة", "buyCardKey": "شراء بطاقة رمز الاسترداد", "redeem": "التسوية الفورية", "record": {"title": "سجل الشحن", "amount": "<PERSON><PERSON><PERSON><PERSON> الشحن", "payment": "مب<PERSON>غ الدفع", "paymentMethod": "طرق الدفع", "orderNo": "رق<PERSON> الطلب", "status": "الحالة", "createTime": "تاريخ الإنشاء", "statusSuccess": "نجاح", "statusPending": "قيد المعالجة", "statusFailed": "فشل"}, "paymentMethodAlipay": "أليباي", "paymentMethodWxpay": "وي شين", "paymentMethodUSDT_TRC20": "تيثر", "paymentMethodEVM_ETH_ETH": "إيثريوم", "paymentMethodPaypal": "باي بال", "paymentMethodAdmin": "مدير", "paymentMethodRedeem": "<PERSON><PERSON><PERSON> الاسترداد", "alipayF2F": "أليباي وجهاً لوجه"}, "pricing": {"fetchErrorMessage": "حدث خطأ في الحصول على معلومات السعر، يرجى الاتصال بالمدير.", "availableModelErrorMessage": "حدث خطأ في الحصول على النموذج المتاح، يرجى الاتصال بالمدير.", "modelName": "اسم النموذج", "billingType": "نوع الفوترة", "price": "السعر", "ratio": "النسبة", "promptPriceSame": "سعر العرض: نفس السعر الأصلي", "completionPriceSame": "سعر التكملة: نفس السعر الأصلي", "promptPrice": "سعر الإشارة: $ {{price}} / 1M توكنز", "completionPrice": "سعر الإكمال: $ {{price}} / 1 مليون توكن", "promptRatioSame": "نسبة التلميح: نفس نسبة الأصلية", "completionRatioSame": "معامل التكبير: نفس معامل التكبير الأصلي", "promptRatio": "نسبة التلميح: {{ratio}}", "completionRatio": "نسبة التكملة: {{ratio}}", "payAsYouGo": "الدفع حسب الاستخدام - دردشة", "fixedPrice": "$ {{price}} / مرة", "payPerRequest": "الدفع حسب الاستخدام - دردشة", "dynamicPrice": "$ {{price}} / مرة", "payPerRequestAPI": "الدفع حسب الاستخدام - واجهة برمجة التطبيقات", "loadingTip": "جارٍ الحصول على معلومات السعر...", "userGroupRatio": "نسبة مجموعة المستخدمين الخاصة بك هي: {{ratio}}", "readFailed": "فشل في القراءة", "billingFormula": "تكلفة الفوترة حسب الكمية = معدل التحويل × مضاعف المجموعة × مضاعف النموذج × (عدد توكنات التلميح + عدد توكنات الإكمال × مضاعف الإكمال) / 500000 (الوحدة: دولار أمريكي)", "billingFormula1": "معدل التحويل = (نسبة الشحن الجديدة / نسبة الشحن الأصلية) × (نسبة التجميع الجديدة / نسبة التجميع الأصلية)", "generatedBy": "تم إنشاء هذه الصفحة بواسطة {{systemName}} تلقائيًا", "modalTitle": "تفاصيل الأسعار", "perMillionTokens": "/1M رموز", "close": "إغلاق", "searchPlaceholder": "ابحث عن اسم النموذج", "viewGroups": "عرض المجموعات", "copiedToClipboard": "تم نسخه إلى الحافظة", "copyFailed": "فشل النسخ", "groupName": "اسم المجموعة", "availableGroups": "النماذج المتاحة للمجموعة {{model}}", "noGroupsAvailable": "لا توجد مجموعات متاحة", "modelGroupsErrorMessage": "فشل في الحصول على بيانات مجموعة النموذج", "currentGroup": "المجموعة الحالية", "copyModelName": "نسخ اسم النموذج", "groupRatio": "نسبة المجموعات", "closeModal": "إغلاق", "groupsForModel": "نموذج متاح للمجموعات", "actions": "عملية", "filterByGroup": "تصفية حسب المجموعة", "groupSwitched": "تم التبديل إلى المجموعة: {{group}}", "showAdjustedPrice": "عرض الأسعار بعد تعديل المجموعة (نسبة الحالية: {{ratio}})"}, "guestQuery": {"usageTime": "استخدام الوقت", "modelName": "اسم النموذج", "promptTooltip": "إدخال استهلاك الرموز", "completionTooltip": "استهلاك الرموز", "quotaConsumed": "ح<PERSON> الاستهلاك", "pasteConfirm": "تم الكشف عن وجود رمز صالح في الحافظة، هل ترغب في لصقه؟", "queryFailed": "فشل الاستعلام", "tokenExpired": "تم انتهاء صلاحية هذه الرمز", "tokenExhausted": "لقد تم استنفاد حصة هذه الرمز.", "invalidToken": "ير<PERSON>ى إدخال الرمز الصحيح", "focusRequired": "يرجى التأكد من أن الصفحة في حالة تركيز.", "queryFirst": "يرجى البحث أولاً", "tokenInfoText": "إجمالي الرمز: {{totalQuota}}  \nاستهلاك الرمز: {{usedQuota}}  \nرصيد الرمز: {{remainQuota}}  \nعدد المكالمات: {{callCount}}  \nتاريخ انتهاء الصلاحية: {{validUntil}}", "unlimited": "<PERSON>ير محدود", "neverExpire": "لا تنتهي أبداً", "infoCopied": "تم نسخ معلومات الرمز إلى الحافظة", "copyFailed": "فشل النسخ", "noDataToExport": "لا توجد بيانات يمكن تصديرها.", "prompt": "تلميح", "completion": "إكمال", "disabled": "استعلام الزوار غير مفعل", "tokenQuery": "استعلام عن الرمز", "tokenPlaceholder": "ير<PERSON>ى إدخال الرمز المطلوب (sk-xxx)", "tokenInfo": "معلومات الرمز", "copyInfo": "نسخ المعلومات", "totalQuota": "إجمالي الرموز", "usedQuota": "استهلاك الرمز", "remainQuota": "<PERSON><PERSON>ي<PERSON> الر<PERSON>ز", "callCount": "عد<PERSON> الاستدعاءات", "validUntil": "تاريخ انتهاء الصلاحية", "currentRPM": "RPM الحالي", "currentTPM": "TPM الحالي", "callLogs": "سجل الاستدعاءات", "exportLogs": "تصدير السجل"}, "agencyProfile": {"fetchError": "فشل في الحصول على معلومات الوكيل", "fetchCommissionError": "فشل في الحصول على قائمة العمولات", "systemPreset": "إعدادات النظام الافتراضية", "lowerRatioWarning": "الرسوم أقل من الإعدادات المسبقة للنظام", "lowerRatioMessage": "تكون الأسعار التالية أقل من القيم المحددة مسبقًا في النظام، يرجى تعديلها في الوقت المناسب:", "cancelRatioEdit": "إلغاء تعديل الرسوم", "updateSuccess": "تم التحديث بنجاح", "updateError": "فشل تحديث معلومات الوكيل:", "updateFailed": "فشل التحديث:", "customPriceUpdateSuccess": "تم تحديث السعر المخصص بنجاح", "customPriceUpdateError": "فشل تحديث السعر المخصص:", "time": "الوقت", "type": "نوع", "agencyCommission": "عمولة الوكلاء", "unknownType": "نوع غير معروف", "amount": "المبلغ", "balance": "الرصيد", "description": "وصف", "group": "مجموعة", "customRate": "معدل مخصص", "systemDefaultRate": "معدل الرسوم الافتراضي للنظام", "action": "عملية", "save": "<PERSON><PERSON><PERSON>", "cancel": "إلغاء", "edit": "<PERSON><PERSON><PERSON><PERSON>", "agencyConsole": "لوحة تحكم الوكلاء", "agencyInfo": "معلومات الوكيل", "editInfo": "تحرير المعلومات", "agencyName": "اسم الوكيل", "agencyLevel": "مستوى الوكيل", "level1": "المستوى 1", "subordinateUsers": "المستخدمين من المستوى الأدنى", "totalSales": "إجمالي المبيعات", "commissionIncome": "دخل العمولة", "cumulativeEarnings": "الإيرادات التراكمية", "agencyFunctions": "وظيفة الوكالة", "hideSubordinateUsers": "إخفاء المستخدمين الفرعيين", "viewSubordinateUsers": "عرض المستخدمين الفرعيين", "hideCommissionDetails": "إخفاء تفاصيل العمولة", "viewCommissionDetails": "عرض تفاصيل العمولة", "hideCustomPrice": "إخفاء السعر المخصص", "setCustomPrice": "تعيين سعر مخصص", "subordinateUsersList": "قائمة المستخدمين الفرعيين", "commissionRecords": "سجل العمولات", "customPriceSettings": "إعدادات الأسعار المخصصة", "saveChanges": "حفظ التغييرات", "editAgencyInfo": "تحرير معلومات الوكالة", "logo": "شعار", "setAgencyLogo": "تعيين شعار الوكيل", "customHomepage": "الصفحة الرئيسية المخصصة", "aboutContent": "<PERSON>و<PERSON> المح<PERSON>وى", "newHomepageConfig": "تكوين الصفحة الرئيسية الجديدة", "customAnnouncement": "إشعار مخصص", "customRechargeGroupRateJson": "تخصيص رسوم مجموعة الشحن JSON", "customRechargeRate": "معدل إعادة الشحن المخصص", "viewSystemDefaultRate": "عرض سعر النظام الافتراضي", "rateComparison": "مقارنة الأسعار", "comparisonResult": "نتائج المقارنة", "higherThanSystem": "أعلى من النظام", "lowerThanSystem": "أقل من النظام", "equalToSystem": "يساوي النظام", "unknown": "غير معروف", "notAnAgentYet": "أنت لست وكيلاً بعد.", "becomeAnAgent": "كن وكيلاً", "startYourOnlineBusiness": "🌟 ابدأ عملك عبر الإنترنت بسهولة", "becomeOurAgent": "كن وكيلنا واستمتع بتجربة ريادة أعمال خالية من الضغوط:", "noInventory": "💼 لا حاجة لتخزين البضائع، ضغط دوران رأس المال صفر", "instantCommission": "💰 توزيع الأرباح الفورية من المبيعات، والحصول على عوائد كبيرة بنسبة معينة.", "easyManagement": "🖥️ لا حاجة لتقنيات بناء المواقع، إدارة متجركم الإلكتروني بسهولة", "flexibleDomainChoice": "اختيار مرن لأسماء النطاقات", "youCan": "يمكنك:", "useOwnDomain": "🏠 استخدم اسم النطاق الخاص بك", "orUseOurSubdomain": "🎁 أو يمكننا تقديم نطاق فرعي مخصص لك", "convenientStart": "🔥 سواء كنت ذو خبرة أو بدأت للتو، نحن نقدم لك طرقًا سهلة للبدء.", "actNow": "Text", "contactAdmin": "اتصل بمدير الموقع لبدء رحلتك كوكيل! 📞", "applyNow": "قدّم طلبك الآن", "contactCooperation": "استشارة التعاون", "understandPolicy": "فهم سياسة الوكلاء وتفاصيل التعاون", "provideDomain": "توفير اسم النطاق", "configDomain": "قدم اسم النطاق الخاص بك، وسنساعدك في الإعداد.", "promoteAndEarn": "الترويج للربح", "startPromoting": "ابدأ في الترويج لموقع الوكالة الخاص بك وكسب العمولات.", "noDeploymentWorries": "لا داعي للقلق بشأن تعقيدات نشر خدمات السحابة، أو قنوات الدفع، أو مشاكل المخزون.", "easySetup": "ما عليك سوى تقديم اسم النطاق، واتباع الدليل للإعداد، يمكنك بسهولة بدء عمل وكيل API على مستوى المؤسسات.", "customizeContent": "يمكنك تخصيص الأسعار ومعلومات الموقع وSEO والشعار وغيرها من المحتويات.", "commissionBenefits": "كوكيل، ستحصل على نسبة من إعادة شحن المستخدمين، حيث يتم خصم التكاليف تلقائيًا، ويمكن سحب المبلغ المتبقي في أي وقت.", "joinNowBenefit": "انضم إلينا الآن واستمتع بمكاسب عصر الذكاء الاصطناعي!", "groups": {"student": "طالب جامعي", "studentDesc": "لدي وقت كافٍ، وآمل أن أتمكن من زيادة الدخل بسهولة من خلال أنشطة الترويج، لتحمل جزء من نفقات المعيشة والترفيه.", "partTime": "عم<PERSON> جزئي أو عمل إضافي", "partTimeDesc": "لا تحتاج إلى استثمار الكثير من الوقت، فقط قم بالترويج بشكل بسيط في أوقات فراغك، وستتمكن بسهولة من كسب دخل إضافي.", "mediaWorker": "ممارس وسائل الإعلام الذاتية", "mediaWorkerDesc": "امتلاك قاعدة جماهيرية معينة، يكفي إضافة رابط في نهاية المقال أو المنشور لتحقيق دخل إضافي بسهولة.", "freelancer": "مستقل", "freelancerDesc": "امتلاك وقت مرن كبير، يمكن بسهولة زيادة الدخل الإضافي من خلال المشاركة في الأنشطة البيعية."}, "stories": {"story1": {"name": "السيد تشانغ", "role": "طالب جامعي"}, "story2": {"name": "السيدة لي", "role": "معلم مدرسة ثانوية"}, "story3": {"name": "السيد ليو", "role": "التجارة الإلكترونية"}, "story4": {"name": "السيد تشنغ", "role": "وسائل الإعلام الذاتية"}, "story5": {"name": "السيد تشو", "role": "ممارس البحث العلمي"}, "story6": {"name": "السيدة وانغ", "role": "Text"}, "story7": {"name": "السيدة هوانغ", "role": "وسائل الإعلام الذاتية"}, "story8": {"name": "السيد ليو", "role": "صناعة تكنولوجيا المعلومات"}}, "earnedAmount": "لقد كسبت {{amount}}", "applyForAgentNow": "قدّم طلبك الآن لتصبح وكيلًا.", "businessLinesConnected": "تم ربط أكثر من 40 خط عمل.", "agencyJoin": "الوكالة والانضمام", "becomeExclusiveAgent": "كن وكيلنا الحصري", "startBusinessJourney": "ابدأ رحلتك التجارية بسهولة~", "welcomeToAgencyPage": "مرحبًا بكم في صفحة الوكلاء الخاصة بنا!", "earningsTitle": "أكثر من مئة شخص قد ربحوا أكثر من 3000 يوان.", "becomeAgentSteps": "خطوات أن تصبح وكيلًا", "agencyRules": "قواعد الوكالة", "suitableGroups": "الفئات المستهدفة", "agencyImages": {"becomeAgent": "كن وكيلاً", "agencyBusiness": "وكالة الأعمال"}, "rules": {"howToEstablishRelation": "كيف يمكن للمستخدم إقامة علاقة وكيل معي؟", "howToEstablishRelationAnswer": "قم بالتسجيل في موقع الوكالة الخاص بك، ليكون لديك مستخدمين.", "canSetPrice": "هل يمكنني تحديد سعر البيع؟", "canSetPriceAnswer": "يمكن! لكن يجب أن يكون سعر بيعك أعلى من سعر التكلفة بنسبة 10%.", "commissionShare": "كم يمكنني الحصول على نسبة من الأرباح؟", "commissionShareAnswer": {"assumption": "افترض: سعر الشراء لديك هو 1 دولار = 1 يوان، وسعر البيع لديك هو 1 دولار = 2 يوان، ونسبة العمولة لديك هي 90%.", "example": "اشترى المستخدم من موقعك بمبلغ 10 دولارات، وأنفق 20 يوانًا.", "calculation": "يمكنك الحصول على: (2-1)*10*0.9 = 9 يوان", "explanation": "تفسير: (سعر البيع - سعر الشراء) * حجم المعاملات * نسبة العمولة"}}}, "error": {"title": "خطأ", "content": "<PERSON><PERSON><PERSON>"}, "loading": {"title": "جارٍ التحميل", "content": "جارٍ التحميل..."}, "notfound": {"title": "404", "content": "لم يتم العثور على الصفحة"}, "servererror": {"title": "٥٠٠", "content": "خطأ في الخادم"}, "unauthorized": {"title": "٤٠١", "content": "غير مصرح به"}, "forbidden": {"title": "٤٠٣", "content": "ممنوع الدخول"}, "networkerror": {"title": "خطأ في الشبكة", "content": "خطأ في الشبكة"}, "timeout": {"title": "تجاوز الوقت", "content": "انتهت مهلة الطلب"}, "noresult": {"title": "لا نتيجة", "content": "لا نتيجة"}, "nopermission": {"title": "لا توجد صلاحيات", "content": "لا توجد صلاحيات"}, "channelBridge": {"title": "تواصل سريع مع القنوات", "channelPlatform": "منصة القنوات", "billingMethod": "طريقة الفوترة", "channelName": "اسم القناة", "remark": "ملاحظات", "availableGroups": "المجموعات المتاحة", "availableModels": "النماذج المتاحة", "channelKey": "مف<PERSON><PERSON><PERSON> القناة", "proxyAddress": "عنوان الاتصال", "cancel": "إلغاء", "submit": "تقديم", "gpt35Models": "نموذج GPT-3.5", "gpt4Models": "نموذج GPT-4", "clear": "إفراغ", "customModelName": "اسم النموذج المخصص", "add": "إضافة", "moreConfigReminder": "للمزيد من الإعدادات، يرجى حفظ القناة ثم التعديل.", "quickIntegration": "توصيل بنقرة واحدة", "selectBillingMethod": "يرجى اختيار طريقة الدفع", "enterChannelName": "ير<PERSON>ى إدخال اسم القناة", "enterChannelRemark": "ير<PERSON>ى إدخال ملاحظات القناة", "selectAvailableGroups": "يرجى اختيار المجموعة التي يمكنها استخدام هذه القناة.", "selectAvailableModels": "اختر/ابحث عن النماذج المتاحة في هذه القناة", "enterChannelKey": "ير<PERSON>ى إدخال مفتاح القناة", "proxyAddressPlaceholder": "هذا الخيار اختياري، يُستخدم لإجراء استدعاءات API من خلال موقع الوكيل، يرجى إدخال عنوان موقع الوكيل.", "includes16kModels": "يتضمن نموذج 16k", "excludes32kModels": "لا يحتوي على نموذج 32k", "cleared": "تم تفريغه", "addCustomModel": "إضافة نموذج مخصص", "clipboardTokenDetected": "تم الكشف عن وجود رمز صالح في الحافظة، هل ترغب في لصقه؟", "channelIntegrationSuccess": "تمت عملية الربط مع القناة بنجاح!", "channelIntegrationFailed": "فشل في توصيل القناة:"}, "about": {"loading": "الحصول على المحتوى الأحدث...", "noContent": "لم يقم المسؤول بتعيين محتوى صفحة \"حول\".", "loadFailed": "فشل تحميل المحتوى..."}, "onlineTopupRecord": {"title": "سجل الشحن", "columns": {"id": "معرف", "username": "مستخدم", "amount": "<PERSON><PERSON><PERSON><PERSON> الشحن", "money": "مب<PERSON>غ الدفع", "paymentMethod": "طرق الدفع", "tradeNo": "رق<PERSON> الطلب", "status": "الحالة", "createTime": "تاريخ الإنشاء"}, "status": {"success": "نجاح", "pending": "قيد المعالجة", "failed": "فشل"}, "paymentMethod": {"alipay": "أليباي", "wxpay": "وي شين", "USDT_TRC20": "تيثر", "EVM_ETH_ETH": "إيثريوم", "paypal": "باي بال"}}, "logContentDetail": {"description": "وصف المعلومات", "downstreamError": "خطأ في المصب", "originalError": "<PERSON><PERSON><PERSON> أصلي", "requestParams": "معلمات الطلب", "copy": "نسخ"}, "viewMode": {"switchTo": "التبديل إلى منظور {{mode}}", "cost": "التكلفة", "usage": "استخدام"}, "agenciesTable": {"title": "إدارة الوكلاء", "addAgency": "وكلاء جدد", "columns": {"id": "معرف", "userId": "معرف المستخدم", "name": "اسم", "domain": "اسم النطاق", "commissionRate": "نسبة العمولة", "salesVolume": "إيرادات المبيعات", "userCount": "عدد المستخدمين", "commissionIncome": "دخل العمولة", "historicalCommission": "الإيرادات التراكمية", "actions": "عملية"}, "confirm": {"deleteTitle": "هل أنت متأكد أنك تريد حذف هذا الوكيل؟", "updateName": "جارٍ تحديث اسم الوكيل...", "updateSuccess": "تم التحديث بنجاح", "updateFailed": "فشل التحديث", "deleteSuccess": "تم الحذف بنجاح!"}, "messages": {"getListFailed": "فشل في الحصول على قائمة الوكلاء: {{message}}", "deleteSuccess": "تم الحذف بنجاح!", "loadingData": "جارٍ التحميل..."}}, "units": {"times": "التالي", "percentage": "{{value}}%", "formatUsage": "{{name}}: {{value}} مرة ({{percent}}%)"}, "dailyUsage": {"total": "الإجمالي", "totalCost": "إجمالي التكلفة", "tooltipTitle": {"cost": "حالة التكلفة", "usage": "حالة الاستخدام"}, "yAxisName": {"cost": "التكلفة (دولار أمريكي)", "usage": "استخدام (دولار أمريكي)"}}, "dailyUsageByModel": {"total": "الإجمالي", "tooltipTotal": "الإجمالي: $ {{value}}", "switchTo": "التبديل إلى", "cost": "التكلفة", "usage": "استخدام", "perspective": "وجهة نظر", "granularity": {"hour": "بالساعة", "day": "<PERSON><PERSON><PERSON> اليوم", "week": "حسب الأسبوع", "month": "شهرياً"}}, "checkinModal": {"title": "ير<PERSON>ى إكمال التحقق", "captchaPlaceholder": "<PERSON><PERSON><PERSON> التحقق", "confirm": "تأكيد", "close": "إغلاق"}, "balanceTransfer": {"title": "تحويل بين الحسابات", "accountInfo": {"balance": "ر<PERSON>ي<PERSON> الحساب", "transferFee": "رسوم تحويل الأموال", "groupNote": "يمكن التحويل فقط بين مجموعات المستخدمين المتطابقة."}, "form": {"receiverId": "معرف المستلم", "receiverUsername": "اسم مستخدم المستلم", "remark": "معلومات ملاحظات", "amount": "مب<PERSON>غ التحويل", "expectedFee": "توقع الخصم", "submit": "بدء التحويل"}, "result": {"success": "تم التحويل بنجاح", "continueTransfer": "استمر في التحويل", "viewRecord": "عرض السجلات"}, "warning": {"disabled": "لم يقم المسؤول بتمكين ميزة التحويل، لذا لا يمكن استخدامها مؤقتًا."}, "placeholder": {"autoCalculate": "ملء مبلغ التحويل يحسب تلقائيًا"}}, "channelsTable": {"title": "إدارة القنوات", "columns": {"id": "معرف", "name": "اسم", "type": "نوع", "key": "مفتاح", "base": "عنوان الواجهة", "models": "نموذج", "weight": "الوزن", "priority": "الأولوية", "retryInterval": "فترة إعادة المحاولة", "responseTime": "وقت الاستجابة", "rpm": "RPM", "status": "الحالة", "quota": "الرصيد", "expireTime": "تاريخ انتهاء الصلاحية", "group": "مجموعة", "billingType": "نوع الفوترة", "actions": "عملية", "fusing": "انقطاع", "sort": "الأولوية", "createdTime": "وقت الإنشاء", "disableReason": "سبب التعطيل"}, "status": {"all": "الجميع", "normal": "طبيعي", "enabled": "الحالة الطبيعية", "manualDisabled": "تعطيل يدوي", "waitingRetry": "انتظر إعادة التشغيل", "suspended": "إيقا<PERSON> الاستخدام", "specified": "<PERSON><PERSON><PERSON> الحالة", "allDisabled": "ممنوع", "specifiedDisabled": "حدد نوع الحظر", "partiallyDisabled": "Status"}, "placeholder": {"selectGroup": "اختر/ابحث عن مجموعة", "selectStatus": "اختيار حالة القناة", "inputSelectModel": "أدخل/اختر اسم النموذج", "selectFusingStatus": "اختيار حالة الانقطاع التلقائي"}, "quota": {"usageAmount": "الاستهلاك: {amount}", "remainingAmount": "المتبقي: {amount}", "customTotalAmount": "المبلغ المخصص: {amount}", "updateNotSupported": "لا يدعم تحديث الرصيد في الوقت الحالي، يرجى استخدام الرصيد المخصص.", "details": "تفاصيل", "sufficient": "كافٍ"}, "actions": {"edit": "<PERSON><PERSON><PERSON><PERSON>", "copy": "قناة الاستنساخ", "delete": "<PERSON>ذ<PERSON> القناة", "enable": "تفعيل", "disable": "ممنوع", "test": "اختبار", "advancedTest": "اختبار متقدم", "viewLog": "سجل القناة", "viewAbility": "عرض القدرات", "cleanUsage": "إفراغ المستخدمة", "updateBalance": "تحديث الرصيد", "copyKey": "نسخ المفتاح"}, "confirm": {"deleteTitle": "تأكيد الحذف", "deleteContent": "هل أنت متأكد أنك تريد حذف القناة {{name}} (#{{id}})؟", "cleanUsageTitle": "تأكيد إفراغ الاستخدام", "cleanUsageContent": "هل أنت متأكد أنك تريد تصفية المبلغ المستهلك لقناة {{name}} (#{{id}})؟", "testTitle": "تأكيد الاختبار", "testContent": "هل أنت متأكد أنك تريد اختبار قناة {{status}}؟", "testNote": "ملاحظة: هذه الوظيفة تتطلب التوافق مع [الإعدادات] -> [الوسيط] -> [إعدادات المراقبة] -> [تعطيل القناة عند الفشل، وتمكين القناة عند النجاح]. إذا لم يتم تفعيل الإعدادات ذات الصلة، فلن يتم تعطيل أو تمكين القناة تلقائيًا بعد الانتهاء من الاختبار.", "deleteDisabledTitle": "تأكيد الحذف", "deleteDisabledContent": "هل أنت متأكد أنك تريد حذف جميع قنوات {{type}}؟"}, "messages": {"operationSuccess": "تمت العملية بنجاح", "operationSuccessWithSort": "تمت العملية بنجاح، قد يتغير ترتيب القنوات، يُنصح بالترتيب حسب المعرف!", "operationFailed": "فشل العملية: {{message}}", "testRunning": "قناة {{name}}(#{{id}}) قيد التشغيل للاختبار، يرجى الانتظار...", "testSuccess": "تم اختبار القناة \"{{name}}(#{{id}})\" {{model}} بنجاح، وقت الاستجابة {{time}} ثانية.", "testFailed": "فشل اختبار القناة「{{name}}(#{{id}})」{{model}}. رمز الحالة: {{code}}، السبب: {{reason}}، انقر لرؤية التفاصيل.", "testStarted": "ابدأ اختبار قناة {{status}}، يرجى التحديث لاحقًا لرؤية النتائج. تعتمد تطبيقات نتائج الاختبار على إعدادات المراقبة الخاصة بك.", "testOperationFailed": "اختبار فاشل", "deleteSuccess": "تم حذف {{count}} قناة بنجاح", "deleteFailed": "فشل الحذف: {{message}}", "modelPrefix": "نموذج {{model}}", "channelInfo": "معلومات القناة", "channelDetail": "{{name}}(#{{id}}){{modelInfo}}", "updateBalanceSuccess": "تم تحديث رصيد القناة \"{{name}}\" بنجاح", "updateBalanceFailed": "فشل تحديث رصيد القناة \"{{name}}\": {{message}}", "updateAllBalanceStarted": "بدء تحديث أرصدة القنوات ذات الحالة الطبيعية جميعها", "updateAllBalanceSuccess": "تم تحديث رصيد جميع القنوات بنجاح", "fetchGroupError": "حدث خطأ في الحصول على بيانات مجموعة القنوات: {{response}}", "fetchChannelError": "فشل في الحصول على بيانات القناة: {{message}}", "selectChannelFirst": "يرجى أولاً اختيار القناة التي ترغب في حذفها.", "deleteDisabledSuccess": "تم حذف جميع قنوات {{type}}، بإجمالي {{count}} قناة.", "deleteOperationFailed": "فشل الحذف", "copySuccess": "نسخ ناجح", "copyFailed": "فشل النسخ: {{message}}", "emptyKey": "المفتاح فارغ", "testSuccessWithWarnings": "نجح الاختبار مع تحذيرات", "viewDetails": "عرض التفاصيل", "fetchChannelDetailError": "فشل في الحصول على تفاصيل القناة", "topupSuccess": "تم الشحن بنجاح", "topupFailed": "فشل الشحن"}, "popover": {"channelInfo": "معلومات القناة"}, "menu": {"deleteManualDisabled": "إزالة القنوات المعطلة يدويًا", "deleteWaitingRetry": "إزالة قناة الانتظار لإعادة التشغيل", "deleteSuspended": "حذف قنوات التوقف عن الاستخدام", "testAll": "اختبار جميع القنوات", "testNormal": "اختبار القنوات العادية", "testManualDisabled": "اختبار تعطيل القناة يدويًا", "testWaitingRetry": "اختبار انتظار إعادة تشغيل القناة", "testSuspended": "اختبار توقف استخدام القنوات", "deleteDisabledAccount": "حذ<PERSON> قنوات الحساب المعطل", "deleteQuotaExceeded": "حذف قنوات تجاوز الحصة", "deleteRateLimitExceeded": "حذ<PERSON> قنوات تجاوز حد المعدل", "deleteInvalidKey": "حذف قنوات المفتاح غير الصالح", "deleteConnectionError": "حذ<PERSON> قنوا<PERSON> خطأ الاتصال"}, "tooltip": {"testNote": "يجب التعاون مع [الإعدادات] -> [الوسيط] -> [إعدادات المراقبة] -> [تعطيل القناة عند الفشل، وتمكين القناة عند النجاح] لاستخدامه. إذا لم يتم تفعيله، فلن يتم تعطيل أو تمكين القناة تلقائيًا بعد الانتهاء من اختبار السرعة."}, "disableReasons": {"account_deactivated": "تم إلغاء تفعيل الحساب", "quota_exceeded": "تجاوز الحصة", "rate_limit_exceeded": "تجاوز حد المعدل", "invalid_key": "مفتا<PERSON> غير صالح", "connection_error": "خطأ في الاتصال"}, "topup": {"reminder1": "يرجى التأكد من صحة معلومات الدفع قبل المتابعة", "reminder2": "إذا واجهت أي مشاكل، يرجى الاتصال بخدمة العملاء"}}, "billingTypes": {"quota": "الحدّ", "times": "<PERSON><PERSON><PERSON> المرات"}, "serverLogViewer": {"title": "عارض سجلات الخادم", "connecting": "جارٍ الاتصال بالخادم...", "downloadSelect": "اختر تحميل ملف السجل", "nginxConfig": "توجيه إعدادات Nginx لـ WebSocket", "directAccess": "إذا كنت تستخدم اسم النطاق للوصول ولم تقم بتكوين دعم WebSocket، فلن يعمل عارض السجلات. في هذه الحالة، يمكنك الوصول مباشرة عبر عنوان IP الخاص بالخادم والمنفذ (على سبيل المثال: http://your-ip:9527).", "domainAccess": "إذا كنت ترغب في الوصول عبر اسم النطاق، تحتاج إلى إضافة التكوين التالي في إعدادات Nginx لدعم WebSocket:", "buttons": {"pause": "إيقاف مؤقت", "resume": "استمر", "clear": "إفراغ"}, "errors": {"fetchFailed": "فشل في الحصول على قائمة ملفات السجل", "downloadFailed": "فشل في تنزيل ملف السجل", "wsError": "خطأ في اتصال WebSocket"}}, "channelScore": {"score": "النتيجة", "successRate": "نسبة النجاح", "avgResponseTime": "متوسط وقت الاستجابة", "title": "نقاط القناة", "hourlyTitle": "نقاط الساعة للقناة", "dailyTitle": "نقاط يومية للقناة", "weeklyTitle": "نقاط الأسبوع للقناة", "monthlyTitle": "نقاط القناة الشهرية", "allTimeTitle": "درجة القناة الإجمالية", "infoTooltip": "تُحسب درجة القناة بناءً على معدل النجاح ووقت الاستجابة.", "tableView": "عرض الجدول", "chartView": "عرض الرسم البياني", "refresh": "تحديث", "selectModel": "اختيار النموذج", "allModels": "جميع النماذج", "sortByScore": "ترتيب حسب النقاط", "sortBySuccessRate": "ترتيب حس<PERSON> معدل النجاح", "sortByResponseTime": "ترتيب حسب وقت الاستجابة", "noData": "لا توجد بيانات حالياً", "totalItems": "إجمالي {{total}} عنصر", "fetchError": "فشل في الحصول على بيانات نقاط القناة", "aboutScoring": "<PERSON>و<PERSON> حساب النقاط", "scoringExplanation": "تُحسب درجة القناة بناءً على معدل النجاح ووقت الاستجابة، وتكون الدرجة الكاملة 1.", "successRateWeight": "وزن معدل النجاح (70%)", "successRateExplanation": "كلما زادت نسبة النجاح، زادت النقاط.", "responseTimeWeight": "وزن وقت الاستجابة (30%)", "responseTimeExplanation": "إذا كانت زمن الاستجابة أقل من 1000 مللي ثانية، يتم منح الدرجة الكاملة، وإذا تجاوزت ذلك يتم خصم النقاط بشكل نسبي.", "columns": {"rank": "ترتيب", "channelId": "معرّف القناة", "channelName": "اسم القناة", "model": "نموذج", "totalRequests": "إجما<PERSON>ي عدد الطلبات", "successRequests": "ع<PERSON><PERSON> الطلبات الناجحة", "failedRequests": "<PERSON><PERSON><PERSON> الطلبات الفاشلة", "successRate": "نسبة النجاح", "avgResponseTime": "متوسط وقت الاستجابة", "score": "الدرجة الإجمالية", "actions": "عملية"}, "actions": {"viewDetails": "عرض التفاصيل", "test": "اختبار القناة", "edit": "قناة التحرير"}, "tooltips": {"excellent": "مم<PERSON><PERSON><PERSON>", "good": "<PERSON>ي<PERSON>", "average": "عام", "poor": "رديء", "veryPoor": "سيء جدًا"}, "scoringExplanation100": "تُحسب درجة القناة بناءً على معدل النجاح ووقت الاستجابة، والدرجة الكاملة هي 100 نقطة."}, "menu": {"channelScores": "نقاط القناة"}, "relay": {"dispatchOptions": "خيارات الجدولة", "preciseWeightCalculation": "حساب دقيق للوزن", "preciseWeightCalculationTip": "عند التفعيل، سيتم استخدام خوارزميات أكثر دقة لحساب وزن القنوات، مما قد يزيد من استهلاك وحدة المعالجة المركزية.", "channelMetricsEnabled": "تفعيل إحصائيات مؤشرات القنوات", "channelMetricsEnabledTip": "عند التفعيل، سيتم جمع مؤشرات مثل معدل نجاح القنوات ووقت الاستجابة، وذلك لتقييم أداء القنوات. وعند الإيقاف، لن يتم جمع هذه البيانات، مما يقلل من استهلاك موارد النظام.", "channelScoreRoutingEnabled": "تفعيل التوجيه المعتمد على درجة القناة", "channelScoreRoutingEnabledTip": "عند التشغيل، سيقوم النظام تلقائيًا بضبط أولوية توزيع الطلبات بناءً على الأداء التاريخي للقنوات، وستحصل القنوات ذات الأداء الأفضل على احتمالية أعلى لتوزيع الطلبات.", "globalIgnoreBillingTypeFilteringEnabled": "تجاهل تصفية نوع الفوترة عالمياً", "globalIgnoreBillingTypeFilteringEnabledTip": "عند التفعيل، سيتم تجاهل تصفية نوع الفوترة في جميع القنوات", "globalIgnoreFunctionCallFilteringEnabled": "تجاهل تصفية استدعاء الوظائف عالمياً", "globalIgnoreFunctionCallFilteringEnabledTip": "عند التفعيل، سيتم تجاهل تصفية استدعاء الوظائف في جميع القنوات", "globalIgnoreImageSupportFilteringEnabled": "تجاهل تصفية دعم الصور عالمياً", "globalIgnoreImageSupportFilteringEnabledTip": "عند التفعيل، سيتم تجاهل تصفية دعم الصور في جميع القنوات"}, "dynamicRouter": {"title": "إدارة التوجيه الديناميكي", "reloadRoutes": "إعادة تحميل المسار", "exportConfig": "تصدير الإعدادات", "clearConfig": "م<PERSON><PERSON> الإعدادات", "importantNotice": "تنبيه هام", "reloadLimitation": "1. إعادة تحميل التوجيه يمكن أن تقوم فقط بتحديث إعدادات التوجيه الحالية، ولا يمكنها إضافة أو حذف التوجيهات. إذا كنت بحاجة إلى إعادة تحميل هيكل التوجيه بالكامل، يرجى إعادة تشغيل التطبيق.", "exportDescription": "2. ستقوم تصدير الإعدادات بتصدير الإعدادات الحالية من قاعدة البيانات إلى ملف router.json، مع تصفية القيم الفارغة والقيم الصفرية.", "clearDescription": "3. ستؤدي إعادة تعيين التكوين إلى حذف جميع تكوينات المسار الديناميكي من قاعدة البيانات، وسيتم إعادة تحميلها من ملف router.json بعد إعادة تشغيل التطبيق.", "routeGroups": "مجموعة التوجيه", "upstreamConfig": "التكوين العلوي", "endpointConfig": "تكوين النقاط النهائية", "editRouteGroup": "تحرير مجموعة التوجيه", "editUpstream": "تحرير إعدادات المصدر", "editEndpoint": "تكوين نقطة التحرير", "editJSON": "تحرير JSON", "confirmClear": "تأكيد مسح الإعدادات", "confirmClearMessage": "ستؤدي هذه العملية إلى مسح جميع تكوينات المسارات الديناميكية في قاعدة البيانات، وسيتم إعادة تحميلها من ملف التكوين بعد إعادة تشغيل التطبيق. هل ترغب في المتابعة؟", "configCleared": "تم مسح تكوين التوجيه الديناميكي، يرجى إعادة تشغيل التطبيق لتطبيق التغييرات.", "configExported": "تم تصدير الإعدادات بنجاح إلى ملف", "configReloaded": "تم إعادة تحميل تكوين التوجيه بنجاح"}, "notification": {"title": "إعدادات الإشعارات", "subscriptionEvents": "الأحداث المشتركة", "notificationMethods": "طرق الإشعار", "alertSettings": "إعدادات التنبيه", "emailConfig": "إعداد البريد الإلكتروني", "customEmails": "رسائل بريد إلكتروني مخصصة", "addEmail": "إضافة بريد إلكتروني", "removeEmail": "إزالة البريد الإلكتروني", "emailPlaceholder": "أدخل عنوان البريد الإلكتروني", "emailTooltip": "أدخل عنوان بريد إلكتروني صالح", "emailDescription": "سيتم إرسال الإشعارات إلى هذا البريد الإلكتروني", "balanceThreshold": "<PERSON><PERSON> الر<PERSON>يد", "balanceThresholdTooltip": "سيتم إرسال تنبيه عندما ينخفض الرصيد عن هذا الحد", "balanceThresholdDescription": "أ<PERSON><PERSON><PERSON> حد الرصيد للتنبيه", "alertExplanationTitle": "شرح التنبيه", "alertExplanation": "سيتم إرسال تنبيهات عند حدوث الأحداث المحددة", "selectEvents": "اختر الأحداث", "eventsDescription": "اختر الأحداث التي تريد تلقي إشعارات عنها", "selectMethods": "اختر طرق تلقي الإشعارات", "methodsDescription": "اختر كيفية تلقي الإشعارات", "description": "الوصف", "recommended": "موصى به", "important": "مهم", "testRecommendation": "يُنصح بإجراء اختبار قبل الحفظ", "testNotification": "اختبار الإشعار", "testMessage": "هذه رسالة اختبار", "testSuccess": "تم إرسال إشعار الاختبار بنجاح", "testFailed": "فشل إرسال إشعار الاختبار", "saveSuccess": "تم حفظ إعدادات الإشعار بنجاح", "saveFailed": "فشل حفظ إعدادات الإشعار", "validation": {"invalidEmail": "Message", "emailRequired": "邮箱地址不能为空", "invalidUrl": "Message", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "请输入WxPusher APP Token", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "请输入飞书机器人Webhook URL", "webhookUrlRequired": "Message", "telegramTokenRequired": "请输入Telegram Bot Token", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "ير<PERSON>ى إدخال رمز بوت التليجرام"}, "qywxbotConfig": "إعداد بوت WeChat للمؤسسات", "qywxbotGuide": "Message", "wxpusherConfig": "WxPusher配置", "wxpusherGuide": "Message", "wxpusherUid": "用户UID", "dingtalkConfig": "إعداد بوت DingTalk", "dingtalkGuide": "Message", "feishuConfig": "إعداد بوت Feishu", "feishuGuide": "Message", "webhookConfig": "Webhook配置", "webhookGuide": "Message", "webhookUrl": "调用地址", "webhookToken": "Message", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "Bear<PERSON>（可选）", "telegramConfig": "إعداد بوت Telegram", "telegramGuide": "Message", "telegramChatIdPlaceholder": "********* 或 @username", "events": {"account_balance_low": "Message", "account_quota_expiry": "额度即将过期", "security_alert": "Message", "system_announcement": "系统公告", "promotional_activity": "Message", "model_pricing_update": "模型价格更新", "anti_loss_contact": "Message"}, "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "当账户额度即将过期时提前通知您", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "模型价格变动和计费规则更新通知", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "通过邮件接收通知消息", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "通过企业微信机器人接收通知", "dingtalk": "Description", "feishu": "通过飞书机器人接收通知"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "Webhook通知", "wxpusher": "Message", "qywxbot": "企业微信机器人", "dingtalk": "Message", "feishu": "飞书机器人"}, "configurationSteps": "Message", "detailedDocumentation": "详细文档：", "qywxbotConfigurationGuide": "Message", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "Message", "qywxbotDocumentationLink": "企业微信群机器人配置说明", "wxpusherConfiguration": "Message", "wxpusherConfigurationGuide": "Message", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "Message", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Message", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "قم بزيارة الموقع الرسمي لـ WxPusher لتسجيل حساب", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "Message", "wxpusherOfficialWebsite": "Message", "dingtalkConfigurationGuide": "Message", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "钉钉自定义机器人接入", "feishuConfigurationGuide": "Message", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "设置机器人名称和描述", "feishuStep4": "Message", "feishuStep5": "复制生成的Webhook URL", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Message", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "复制获得的Bot Token", "telegramStep5": "Message", "telegramStep6": "访问 https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "Telegram Bot API 官方文档", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "Message", "dingtalkNoticeTitle": "注意事项：", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "Message", "feishuMessageFormatsTitle": "消息格式支持：", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Title", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• 建议启用签名校验以提高安全性", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "Message", "telegramNoticeTitle": "注意事项：", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "Chat ID格式说明：", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "• 群组中需要先将机器人添加为成员", "telegramChannelPermission": "Message", "webhookCallUrl": "عنوان الاستدعاء", "webhookConfigurationGuide": "دليل تكوين Webhook", "webhookDataFormatExample": "مثال على تنسيق البيانات：", "webhookConfigurationInstructions": "تعليمات التكوين：", "webhookRequestMethod": "• طريقة الطلب：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• طريقة المصادقة：Bearer <PERSON>（اختياري، بعد الملء سيضيف Authorization: Bearer {token} في رأس الطلب）", "webhookTimeout": "• وقت انتهاء المهلة：30 ثانية", "webhookRetryMechanism": "• آلية إعادة المحاولة：يعيد المحاولة مرتين بعد الفشل", "webhookTip": "💡 نصيحة：تأكد من أن نقطة نهاية Webhook الخاصة بك يمكنها استقبال طلبات POST وإرجاع رموز حالة 2xx", "telegramStep3Detailed": "Message", "telegramPersonalChatDetailed": "Message", "telegramGroupChatDetailed": "Message", "telegramChannelDetailed": "Message", "telegramQuickChatIdTitle": "Message", "telegramQuickStep1": "Message", "telegramQuickStep2": "Message", "telegramQuickStep3": "Message"}, "legal": {"privacyPolicy": {"title": "Title", "lastUpdated": "Time", "sections": {"informationCollection": {"title": "信息收集", "description": "Description", "items": {"accountInfo": "Text", "usageData": "Description", "technicalInfo": "Text"}}, "informationUsage": {"title": "Title", "description": "Description", "items": ["提供和维护我们的服务", "Description", "改进服务质量和用户体验", "Description", "防止欺诈和滥用"]}, "informationSharing": {"title": "Title", "description": "Description", "items": ["Description", "Description", "Description"]}, "dataSecurity": {"title": "数据安全", "description": "Description", "items": ["数据加密传输和存储", "Text", "定期安全审计和更新", "Text"]}, "dataRetention": {"title": "数据保留", "description": "Description", "items": ["Text", "Text", "Text"]}, "userRights": {"title": "您的权利", "description": "Description", "items": ["User", "删除您的账户和相关数据", "User", "User"]}, "cookieUsage": {"title": "Title", "description": "Description", "items": ["维持用户会话", "Text", "Text"]}, "thirdPartyServices": {"title": "第三方服务", "description": "Description", "items": ["Text", "GitHub OAuth：用于用户身份验证", "Text"]}, "childrenPrivacy": {"title": "儿童隐私", "description": "Description"}, "policyUpdates": {"title": "Title", "description": "Description"}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text"}}}, "termsOfService": {"title": "Title", "lastUpdated": "Time", "importantNotice": "Message", "sections": {"serviceDescription": {"title": "服务描述", "description": "Description", "items": ["API 密钥管理", "Description", "使用统计和监控", "Description", "Description"]}, "userAccount": {"title": "用户账户", "description": "Description", "items": ["Text", "User", "User", "及时更新账户信息", "User"]}, "usageRules": {"title": "使用规则", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "prohibitedBehavior": {"title": "禁止行为", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "serviceAvailability": {"title": "服务可用性", "description": "Description", "items": ["Text", "Text", "Text", "会提前通知计划内的维护"]}, "feesAndPayment": {"title": "Title", "description": "Description", "items": ["Text", "高级功能可能需要付费", "Text", "Text"]}, "intellectualProperty": {"title": "知识产权", "description": "Description", "items": ["Text", "您获得有限的使用许可", "Text", "Text"]}, "privacyProtection": {"title": "隐私保护", "description": "Description", "items": ["Text", "采取合理措施保护数据安全", "Text"]}, "disclaimer": {"title": "免责声明", "description": "Description", "items": ["Text", "Text", "不对间接损失承担责任", "Text"]}, "serviceTermination": {"title": "服务终止", "description": "Description", "items": ["您违反这些条款", "Text", "Text", "法律要求"]}, "termsModification": {"title": "Title", "description": "Description", "items": ["重大变更会提前通知", "Text", "Text"]}, "disputeResolution": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "contactUs": {"title": "联系我们", "description": "Description", "email": "邮箱", "address": "Text", "serviceHours": "Text"}}}, "common": {"copyright": "Text", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "Text"}}}