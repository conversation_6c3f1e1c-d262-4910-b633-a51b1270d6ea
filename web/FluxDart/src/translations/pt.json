{"message": {"copyModelSuccess": "Modelo copiado para a área de transferência!", "copyFailed": "<PERSON><PERSON><PERSON> fal<PERSON>, por favor copie manualmente.", "logoutSuccess": "Logout bem-sucedido.", "loginSuccess": {"default": "<PERSON>gin bem-sucedido", "welcomeBack": "Bem-vindo de volta."}, "removeLocalStorage": {"confirm": "Você deseja limpar o cache local?", "success": "Limpeza do cache local concluída com sucesso."}, "loadData": {"error": "Falha ao carregar os dados de {{name}}."}, "noNotice": "Nenhum conteúdo de an<PERSON>cio disponível.", "verification": {"turnstileChecking": "Turnstile está verificando o ambiente do usuário!", "pleaseWait": "Por favor, tente novamente mais tarde."}, "clipboard": {"inviteCodeDetected": "Código de convite detectado, preenchido automaticamente!", "clickToCopy": "Clique para copiar", "copySuccess": "Cópia be<PERSON>-sucedida"}}, "common": {"yes": "sim", "no": "não", "copyAll": "Copiar tudo", "all": "<PERSON><PERSON>", "more": "mais", "unlimited": "sem restrições", "enabled": "Abrir", "disabled": "fechar", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "create": "criação", "usd": "dólar americano", "day": "{{count}} dias", "day_plural": "{{count}} dias", "days": "céu", "seconds": "segundo", "times": "próximo", "submit": "Submeter", "bind": "vinculação", "unknown": "Desconhecido", "loading": "Carregando...", "copyFailed": "Cópia falhou.", "people": "pessoa", "ok": "Certo", "close": "fechar", "copied": "Copiado.", "expand": "<PERSON><PERSON><PERSON><PERSON>", "collapse": "recolher", "none": "Text", "remark": "<PERSON>a", "selectPlaceholder": "Por favor, escolha {{name}}.", "on": "abrir", "off": "fechar", "name": "Identificação", "displayName": "Nome a ser exibido", "description": "Descrição", "ratio": "ampliação", "unnamed": "Canal sem nome", "groups": "Grupo", "captchaPlaceholder": "Por favor, insira o código de verificação.", "confirm": "Confirmação", "permissions": "Permissões", "actions": "Ações", "createdTime": "Hora de criação", "expiredTime": "Hora de expiração", "search": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Redefinir", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "pagination": {"total": "Total {{total}} itens"}}, "currency": {"symbol": "$"}, "floatButton": {"clickToOpen": "Clique para abrir o link."}, "userRole": {"normal": "usuário comum", "agent": "agente", "admin": "Administrador", "superAdmin": "super administrador", "loading": "Carregando..."}, "channelStatus": {"enabled": "Ativar", "disabled": "Desativar", "waitingRestart": "<PERSON><PERSON><PERSON><PERSON>", "waiting": "esperar", "autoStoppedTitle": "O reenvio automático do canal excedeu o número máximo de tentativas ou acionou a condição de desativação automática.", "stopped": "Desativar", "partiallyDisabled": "Parte desativada", "unknown": "Desconhecido", "reason": "razão"}, "channelBillingTypes": {"payAsYouGo": "Cobrança por quantidade", "payPerRequest": "Cobrança por uso", "unknown": "man<PERSON> desconhecida"}, "tokenStatus": {"normal": "normal", "disabled": "Desativar", "expired": "expirado", "exhausted": "esgotar", "unknown": "Desconhecido"}, "userStatus": {"normal": "normal", "banned": "banimento", "unknown": "Desconhecido"}, "redemptionStatus": {"normal": "normal", "disabled": "Desativar", "redeemed": "<PERSON><PERSON> re<PERSON>gata<PERSON>", "expired": "expirado", "unknown": "Desconhecido"}, "duration": {"request": "pedido", "firstByte": "primeiro byte", "total": "Total", "seconds": "segundo", "lessThanOneSecond": "<1 segundo"}, "streamType": {"stream": "fluxo", "nonStream": "não streaming"}, "noSet": {"title": "O administrador não configurou {{name}}.", "name": {"about": "sobre", "chat": "Diálogo"}}, "buttonText": {"add": "Novo", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmação", "delete": "Excluir", "edit": "editar", "save": "<PERSON><PERSON>", "updateBalance": "<PERSON><PERSON><PERSON><PERSON> saldo", "test": "teste", "multiple": "múltipla escolha"}, "channelPage": {"title": "Gestão de canais"}, "channelStatusCount": {"title": "Estatísticas de status do canal", "summary": "Ativado {{enabled}} | Desativado {{disabled}} | Tentando novamente {{retry}} | Parado {{stopped}}", "statusEnabled": "<PERSON><PERSON>do", "statusDisabled": "<PERSON><PERSON>.", "statusRetry": "Tentando novamente.", "statusStopped": "Parado", "statusPartially": "Parcialmente desabilitado"}, "header": {"routes": {"status": "estado", "home": "Página inicial", "chat": "Diálogo", "pptGen": "Geração de PPT", "chart": "estatística", "agency": "agente", "channel": "canal", "ability": "capacidade de canal", "channelGroup": "Grupo de canais", "token": "token", "log": "registro", "logDetail": "de<PERSON><PERSON>", "midjourney": "<PERSON><PERSON><PERSON>", "user": "<PERSON><PERSON><PERSON><PERSON>", "config": "Configuração", "packagePlanAdmin": "pacote", "redemption": "código de troca", "group": "Grupo", "query": "consulta", "about": "sobre", "setting": {"default": "Configurações", "operation": "Configurações de operação", "system": "Configurações do sistema", "global": "Configurações globais", "advance": "Configuração de características", "sensitive": "Configuração de palavras sensíveis", "verification": "Configuração do código de verificação", "update": "Verificar atualizações"}, "account": {"default": "conta", "profile": "Centro Pessoal", "cardTopup": "Troca de código de cartão", "onlineTopup": "Recarga online", "recharge": "Recarga de saldo", "balanceTransfer": "Transferência de saldo", "pricing": "Descrição das taxas", "packagePlan": {"list": "Compra de pacotes", "record": "Registro de compras"}, "notificationSettings": "Configurações de notificação"}, "tools": {"default": "ferramenta", "fileUpload": "Upload de arquivo", "keyExtraction": "Extração de chave", "multiplierCalculator": "Calculadora de multiplicação", "shortLink": "Geração de link curto", "testConnection": "Teste de acesso", "customPrompts": "Gerenciamento de palavras-chave", "redis": "Visualização do Redis", "ratioCompare": "Title", "serverLog": "Visualizador de logs do servidor"}, "onlineTopupRecord": "Registro de recarga", "channelScores": "Pontuação do canal", "dynamicRouter": "Roteamento dinâmico", "task": "<PERSON><PERSON><PERSON><PERSON>", "agencyJoin": "Parceria de agência"}, "dropdownMenu": {"profile": "Centro Pessoal", "recharge": "Recarga de saldo", "agencyCenter": "Centro de Agentes", "checkin": "Registro", "darkMode": {"enable": "<PERSON><PERSON> es<PERSON>ro", "disable": "<PERSON><PERSON>o"}, "fullScreen": {"default": "Alternar para tela cheia", "enable": "<PERSON>do de tela cheia", "disable": "Sair do modo de tela cheia"}, "logout": "<PERSON><PERSON> da conta"}, "checkin": {"default": "Registro", "success": "Check-in bem-sucedido.", "failed": "Falha no registro de presença.", "verification": "Por favor, complete a verificação."}, "avatarProps": {"login": "<PERSON><PERSON>"}}, "settings": {"public": {"titles": {"default": "Configurações públicas"}, "SystemName": "Nome do sistema", "ServerAddress": "Endereço de serviço", "TopUpLink": "Link de recarga", "ChatLink": "Link de diálogo", "Logo": "Logotipo do sistema", "HomePageContent": "<PERSON><PERSON><PERSON><PERSON> da página inicial", "About": "Sobre o conteúdo", "Notice": "Conteúdo do anúncio", "Footer": "Conteúdo do rodapé", "RegisterInfo": "Notificação de Registro", "HeaderScript": "Cabeçalho personalizado", "SiteDescription": "Descrição do site", "PrivacyPolicy": "Política de Privacidade", "ServiceAgreement": "Acordo de Serviço", "FloatButton": {"FloatButtonEnabled": "Abrir", "DocumentInfo": "Informações do documento", "WechatInfo": "Mensagem do WeChat", "QqInfo": "Informações do QQ"}, "CustomThemeConfig": "<PERSON><PERSON> personalizado", "AppList": "Links de amizade"}}, "home": {"default": {"title": "Bem-vindo!", "subtitle": "Desenvolvimento secundário baseado na One API, oferecendo funcionalidades mais completas.", "start": "Comece a usar", "description": {"title": "Novas características:", "part1": "Interface de usuário totalmente nova, conveniente e rápida.", "part2": "Otimizar o mecanismo de agendamento, de forma eficiente e estável.", "part3": "Desenvolvido para empresas, seguro e confiável.", "part4": "<PERSON>s recursos avançados, esperando por você para descobrir."}}}, "dailyUsageChart": {"title": "Uso diário do modelo", "yAxisName": "Uso (USD)", "loadingTip": "<PERSON><PERSON>", "fetchError": "Erro ao obter dados de uso diário:"}, "modelUsageChart": {"title": "Uso do modelo", "hourlyTitle": "Uso do modelo por hora", "dailyTitle": "Uso diário do modelo", "weeklyTitle": "Uso do modelo por semana", "monthlyTitle": "Uso do modelo por mês"}, "granularity": {"hour": "por hora", "day": "todos os dias", "week": "<PERSON><PERSON><PERSON><PERSON>", "month": "todo mês", "all": "<PERSON><PERSON>"}, "abilitiesTable": {"title": "capacidade de canal", "export": "Exportar", "group": "Grupo", "model": "modelo", "channelId": "Número do canal", "enabled": "<PERSON><PERSON>do", "weight": "peso", "priority": "prioridade", "billingType": "Tipo de cobrança", "functionCallEnabled": "Ativação de chamada de função", "imageSupported": "Suporte para imagem", "yes": "sim", "no": "não", "perToken": "Cobrança por token", "perRequest": "Cobrança sob demanda", "noDataToExport": "Não há dados para exportar.", "exportConfirm": "Você tem certeza de que deseja exportar os dados da página atual?", "exportSuccess": "Exportação bem-sucedida", "toggleSuccess": "Mudança bem-sucedida", "toggleError": "Mudança falhou.", "selectOrInputGroup": "Escolha ou insira grupos de usuários."}, "logsTable": {"retry": "Tente novamente", "retryChannelList": "Lista de canais de re-teste", "retryDurations": "Detalhes do tempo de reexecução", "channel": "canal", "duration": "tempo gasto", "startTime": "Hora de início", "endTime": "<PERSON>ra de término", "retryCount": "Número de tentativas", "retryDetails": "Detalhes da nova tentativa", "totalRetryTime": "Tempo total de tentativas", "seconds": "segundo", "tokenGroup": "Grupo de tokens", "selectGroup": "Escolher grupo", "dailyModelUsageStats": "Visão geral da chamada de dados", "time": "tempo", "moreInfo": "Mais informaç<PERSON>", "ip": "IP", "remoteIp": "IP remoto", "ipTooltip": "IP: {{ip}}  \nIP remoto: {{remoteIp}}", "requestId": "ID de solicitação", "username": "nome de usuário", "userId": "ID do usuário", "tokenName": "Nome do token", "token": "token", "type": "tipo", "typeUnknown": "Desconhecido", "type充值": "recarga", "type消费": "consumo", "type管理": "gestão", "type系统": "sistema", "type邀请": "<PERSON><PERSON><PERSON>", "type提示": "Dica", "type警告": "Aviso", "type错误": "erro", "type签到": "Registro", "type日志": "registro", "type退款": "reembolso", "type邀请奖励金划转": "Transferência de bônus de convite", "type代理奖励": "Text", "type下游错误": "Erro a montante", "type测试渠道": "Canal de teste", "typeRecharge": "recarregar", "typeConsumption": "consumo", "typeManagement": "gestão", "typeSystem": "sistema", "typeInvitation": "<PERSON><PERSON><PERSON>", "typePrompt": "Dica", "typeWarning": "Aviso", "typeError": "erro", "typeCheckin": "Registro", "typeLog": "registro", "typeRefund": "reembolso", "typeInviteReward": "Transferência de bônus de convite", "typeAgencyBonus": "Text", "typeDownstreamError": "Erro a montante", "typeChannelTest": "Canal de teste", "channelId": "ID do canal", "channelName": "Nome do canal", "model": "modelo", "modelPlaceholder": "Insira/Selecione o nome do modelo", "info": "informação", "isStream": "fluxo", "isStreamPlaceholder": "Entrada/Selecione se é em fluxo", "prompt": "Dica", "completion": "completar", "consumption": "consumo", "consumptionRange": "Faixa de limite de consumo", "description": "Explicação", "action": "Operação", "details": "<PERSON><PERSON><PERSON>", "tokenKey": "Chave do token", "requestDuration": "Tempo de solicitação", "firstByteDuration": "Tempo de carregamento do primeiro byte", "totalDuration": "Tempo total gasto", "lessThanOneSecond": "<1 segundo", "modelInvocation": "Chamada de modelo", "modelUsage": "Uso do modelo", "totalQuota": "Limite total de consumo: {{quota}}", "totalRpm": "Número de solicitações/minuto: {{rpm}}", "totalTpm": "Número de tokens/minuto: {{tpm}}", "totalMpm": "Valor/minuto: {{mpm}}", "dailyEstimate": "Consumo diário estimado: {{estimate}}", "currentStats": "RPM atual: {{rpm}} TPM atual: {{tpm}} MPM atual: ${{mpm}} Estimativa de consumo diário: ${{dailyEstimate}}", "statsTooltip": "Apenas estatísticas de logs não arquivados, RPM: número de solicitações por minuto, TPM: número de tokens por minuto, MPM: dinheiro consumido por minuto, a estimativa diária de consumo é inferida com base no MPM atual.", "showAll": "<PERSON><PERSON>r tudo", "exportConfirm": "Exportar o log desta página?", "export": "Exportar", "statsData": "dados estatísticos", "today": "no dia", "lastHour": "1 hora", "last3Hours": "3 horas", "lastDay": "1 dia", "last3Days": "3 dias", "last7Days": "7 dias", "lastMonth": "1 mês", "last3Months": "3 meses", "excludeModels": "Modelo de exclusão", "selectModelsToExclude": "Escolha o modelo a ser excluído.", "excludeErrorCodes": "Excluir código de erro", "excludeErrorCodesPlaceholder": "Escolha os códigos de erro a serem excluídos.", "errorCode": "<PERSON><PERSON><PERSON> de erro", "errorCodePlaceholder": "Erro de entrada/seleção de código.", "timezoneTip": "<PERSON><PERSON> ho<PERSON> atual: {timezone}", "timezoneNote": "Aviso de fuso horário", "timezoneDescription": "Os dados estatísticos são agrupados por data de acordo com o seu fuso horário atual. Fusos horários diferentes podem resultar em períodos de agrupamento de dados distintos. Para ajustar, vá ao seu centro pessoal e modifique as configurações de fuso horário.", "goToProfile": "Ir para o centro pessoal", "realtimeQuota": "Time", "viewTotalQuota": "Verificar o consumo total", "viewTotalQuotaTip": "Verifique o valor total de consumo histórico (a consulta pode levar alguns segundos).", "loadingTotalQuota": "Consultando o valor total de consumo, por favor aguarde...", "totalQuotaTitle": "Estatísticas de consumo total histórico", "loadTotalQuotaError": "Falha ao obter o valor total de consumo.", "requestLogs": "Registro de solicitação - {{requestId}}", "noRequestLogs": "Nenhum registro de solicitação disponível.", "metricsExplanation": "Apenas estatísticas de logs não arquivados, RPM: número de requisições por minuto, TPM: número de tokens por minuto, MPM: dinhe<PERSON> consumido por minuto, o consumo diário estimado é inferido com base no MPM atual.", "autoRefresh": "Atualização automática", "autoRefreshTip": "Clique para ativar/desativar a atualização automática. <PERSON>uando ativado, os dados serão atualizados automaticamente a cada número especificado de segundos.", "autoRefreshOn": "A atualização automática foi ativada.", "autoRefreshOff": "Atualização automática desativada.", "refreshInterval": "Intervalo de atualização", "stopRefresh": "<PERSON><PERSON> de atualizar.", "secondsWithValue": "{{seconds}} segundos", "minutesWithValue": "{{minutes}} minutos"}, "mjLogs": {"logId": "ID do log", "submitTime": "Data de submissão", "type": "tipo", "channelId": "ID do canal", "userId": "ID do usuário", "taskId": "ID da tarefa", "submit": "Submeter", "status": "estado", "progress": "progresso", "duration": "tempo gasto", "result": "resultado", "prompt": "Prompt", "promptEn": "PromptEn", "failReason": "Razões para o fracasso", "startTime": "Hora de início", "endTime": "<PERSON>ra de término", "today": "no dia", "lastHour": "1 hora", "last3Hours": "3 horas", "lastDay": "1 dia", "last3Days": "3 dias", "last7Days": "7 dias", "lastMonth": "1 mês", "last3Months": "3 meses", "selectTaskType": "Escolha o tipo de tarefa", "selectSubmitStatus": "Escolha a situação de envio.", "submitSuccess": "<PERSON><PERSON> be<PERSON>", "queueing": "está na fila", "duplicateSubmit": "Submissão duplicada", "selectTaskStatus": "Escolher o estado da tarefa", "success": "sucesso", "waiting": "esperar", "queued": "<PERSON><PERSON>", "executing": "Executar", "failed": "falha", "seconds": "segundo", "unknown": "Desconhecido", "viewImage": "Clique para ver", "markdownFormat": "Formato Markdown", "midjourneyTaskId": "ID da tarefa Midjourney", "copiedAsMarkdown": "Copiado como formato Markdown.", "copyFailed": "Cópia falhou.", "copiedMidjourneyTaskId": "ID da tarefa do Midjourney copiado.", "drawingLogs": "Registro de <PERSON>ho", "onlyUnarchived": "Apenas contabilizar os logs não arquivados.", "imagePreview": "Pré-visualização da imagem", "copiedImageUrl": "Imagem do endereço copiado.", "copy": "copiar", "download": "baixar", "resultImage": "imagem do resultado", "downloadError": "<PERSON>alha ao baixar a imagem.", "mode": "modo", "selectMode": "<PERSON><PERSON><PERSON><PERSON> modo", "relax": "<PERSON><PERSON>", "fast": "<PERSON><PERSON>", "turbo": "Modo de alta velocidade", "actions": "Operação", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "refreshSuccess": "Atualização do status da tarefa bem-sucedida.", "refreshFailed": "Falha ao atualizar o status da tarefa.", "refreshError": "Ocorreu um erro ao atualizar o status da tarefa.", "tasks": {"title": "Lista de tarefas", "taskId": "ID da tarefa", "platform": "Plataforma", "type": "Tipo", "status": "Status", "progress": "Progresso", "submitTime": "<PERSON><PERSON> de en<PERSON>", "startTime": "Hora de início", "endTime": "<PERSON>ra de término", "duration": "Duração", "result": "<PERSON><PERSON><PERSON><PERSON>", "taskIdPlaceholder": "Digite o <PERSON> da tarefa", "platformPlaceholder": "Selecione a plataforma", "typePlaceholder": "Selecione o tipo", "statusPlaceholder": "Selecione o status", "videoGeneration": "Geração de vídeo", "imageGeneration": "Geração de imagem", "musicGeneration": "Text", "textGeneration": "geração de texto", "unknown": "Text", "success": "sucesso", "failed": "Error", "inProgress": "Text", "submitted": "enviado", "queued": "Text", "notStarted": "não iniciado", "viewResult": "Text", "viewError": "ver erro", "taskDetails": "Text", "errorDetails": "detalhes do erro", "loadError": "Error"}, "viewVideo": "ver vídeo", "videoPreview": "Text", "copyVideoUrl": "Text", "copiedVideoUrl": "Text", "downloadVideo": "baixar vídeo", "videoNotSupported": "Text", "videoUrl": "endereço do vídeo", "videoUrls": "Text"}, "mjTaskType": {"IMAGINE": "<PERSON><PERSON><PERSON> imagem", "UPSCALE": "ampliar", "VARIATION": "transformação", "REROLL": "regenerar", "DESCRIBE": "Imagem gera texto", "BLEND": "mistura de imagens", "OUTPAINT": "zoom", "DEFAULT": "Desconhecido"}, "mjCode": {"submitSuccess": "<PERSON><PERSON> be<PERSON>", "queueing": "está na fila", "duplicateSubmit": "Submissão duplicada", "unknown": "Desconhecido"}, "mjStatus": {"success": "sucesso", "waiting": "esperar", "queued": "<PERSON><PERSON>", "executing": "Executar", "failed": "falha", "unknown": "Desconhecido"}, "tokensTable": {"title": "Gerenciamento de tokens", "table": {"title": "Gerenciamento de tokens", "toolBar": {"add": "Criar novo token", "delete": "Excluir token", "deleteConfirm": "Está a eliminar em massa {{count}} tokens, esta operação não pode ser desfeita.", "export": "Exportar", "exportConfirm": "Exportar o token da página atual?"}, "action": "Operação"}, "modal": {"title": {"add": "Novo token", "edit": "Editar token"}, "field": {"name": "Nome do token", "description": "Descrição do token", "type": {"default": "Método de cobrança", "type1": "Cobrança por quantidade", "type2": "Cobrança por uso", "type3": "Cobrança mista", "type4": "Prioridade por quantidade", "type5": "Por ordem de prioridade secundária."}, "status": "estado", "statusEnabled": "normal", "statusDisabled": "Desativar", "statusExpired": "expirado", "statusExhausted": "esgotar", "models": "Modelos disponíveis", "usedQuota": "limite de consumo", "remainQuota": "<PERSON><PERSON> restante", "createdTime": "Data de criação", "expiredTime": "data de validade", "all": "<PERSON><PERSON>", "more": "mais", "notEnabled": "<PERSON><PERSON>", "unlimited": "sem restrições", "daysLeft": "{{days}} dias após a expiração", "expired": "Text", "userId": "ID do usuário", "key": "Chave API", "neverExpire": "nunca expira"}, "delete": {"title": "Excluir", "content": "Você tem certeza de que deseja excluir a chave API {{name}}?"}, "footer": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmação", "update": "atualização"}, "bridge": {"title": "Integração rápida de canais", "placeholder": "Por favor, insira o endereço de serviço do {{name}}."}, "copy": {"title": "Cópia manual"}}, "dropdown": {"onlineChat": "Conversa online", "disableToken": "Desativar token", "enableToken": "Ativar token", "editToken": "Editar token", "requestExample": "Exemplo de solicitação", "tokenLog": "Registro de token", "shareToken": "Token de compartilhamento", "quickIntegration": "Conexão com um clique"}, "error": {"fetchModelsFailed": "Falha ao obter o modelo: {{message}}", "batchDeleteFailed": "Falha ao excluir em massa: {{message}}", "deleteTokenFailed": "Falha ao remover o token: {{message}}", "refreshTokenFailed": "Falha ao atualizar o token: {{message}}", "enableTokenFailed": "Falha ao ativar o token: {{message}}", "disableTokenFailed": "Falha ao desativar o token: {{message}}", "fetchDataFailed": "Falha ao obter dados: {{message}}"}, "success": {"batchDelete": "Sucesso ao remover {{count}} tokens.", "shareTextCopied": "O texto compartilhado foi copiado para a área de transferência.", "tokenCopied": "O token foi copiado para a área de transferência.", "deleteToken": "Token excluído com sucesso.", "refreshToken": "Token de atualização bem-sucedido.", "enableToken": "Token ativado com sucesso.", "disableToken": "Token desativado com sucesso.", "export": "Exportação do token da página atual bem-sucedida."}, "warning": {"copyFailed": "<PERSON><PERSON><PERSON> fal<PERSON>, por favor copie manualmente.", "invalidServerAddress": "Por favor, insira o endereço do servidor correto."}, "info": {"openingBridgePage": "Abrindo a página de integração, o token foi copiado para você."}, "export": {"name": "Nome", "key": "chave", "billingType": "Método de cobrança", "status": "estado", "models": "Modelos disponíveis", "usedQuota": "limite de consumo", "remainQuota": "<PERSON><PERSON> restante", "createdTime": "Data de criação", "expiredTime": "data de validade", "unlimited": "sem restrições", "neverExpire": "nunca expira"}, "billingType": {"1": "Cobrança por quantidade", "2": "Cobrança por uso", "3": "Cobrança mista", "4": "Prioridade por quantidade", "5": "Por ordem de prioridade secundária."}, "bridge": {"quickIntegration": "Conexão com um clique"}}, "editTokenModal": {"editTitle": "Editar token", "createTitle": "Criar token", "defaultTokenName": "Token de {{username}} {{date}}", "tokenName": "Nome do token", "unlimitedQuota": "limite infinito", "remainingQuota": "<PERSON><PERSON> restante", "authorizedQuota": "Limite de autorização", "quotaLimitNote": "O limite máximo de uso do token é restrito ao saldo da conta.", "quickOptions": "Opções rápidas", "neverExpire": "nunca expira", "expiryTime": "data de validade", "billingMode": "Modelo de cobrança", "selectGroup": "Escolher grupo", "switchGroup": "Escolher grupo", "switchGroupTooltip": "Escolha o grupo ao qual o token pertence, pois diferentes grupos têm diferentes preços e permissões de funcionalidade. Se não escolher, o grupo atual do usuário será usado por padrão.", "switchGroupHint": "A escolha do grupo afetará a taxa de cobrança dos tokens e os modelos disponíveis, por favor, escolha de acordo com suas necessidades reais.", "importantFeature": "Importante", "tokenRemark": "Nota do token", "discordProxy": "Proxy do Discord", "enableAdvancedOptions": "Ativar opções avançadas", "generationAmount": "Quantidade gerada", "availableModels": "Modelos disponíveis", "selectModels": "Sele<PERSON>e/pesquise/adicione modelos disponíveis, deixar em branco significa sem restrições.", "activateOnFirstUse": "Ativação inicial", "activateOnFirstUseTooltip": "Se esta opção estiver ativada e a ativação for feita na primeira utilização, o período de validade do token configurado acima será substituído.", "activationValidPeriod": "período de validade da ativação", "activationValidPeriodTooltip": "O período de validade do token após a ativação na primeira utilização (unidade: dias)", "ipWhitelist": "Lista branca de IP", "ipWhitelistPlaceholder": "Endereço IP (intervalo), suporta IPV4 e IPV6, múltiplos separados por vírgula.", "rateLimiter": "limitador de corrente", "rateLimitPeriod": "ciclo de limitação de fluxo", "rateLimitPeriodTooltip": "Período de limitação de fluxo (unidade: segundos)", "rateLimitCount": "limite de fluxo", "rateLimitCountTooltip": "Número de vezes disponíveis durante o período de limitação.", "promptMessage": "Mensagem de aviso", "promptMessageTooltip": "Mensagem de aviso quando o limite de fluxo é excedido.", "promotionPosition": "Posição de promoção", "promotionPositionStart": "Começo", "promotionPositionEnd": "finalização", "promotionPositionRandom": "aleatório", "promotionContent": "Conteúdo promocional", "currentGroup": "Grupo atual", "searchGroupPlaceholder": "Pesquisar nome do grupo, descrição ou multiplicador...", "mjTranslateConfig": "Configuração de tradução MJ", "mjTranslateConfigTip": "Configuração de tradução válida apenas para palavras-chave do Midjourney.", "mjTranslateBaseUrlPlaceholder": "Por favor, insira a URL base do serviço de tradução.", "mjTranslateApiKeyPlaceholder": "Por favor, insira a chave da API do serviço de tradução.", "mjTranslateModelPlaceholder": "Por favor, insira o nome do modelo utilizado para o serviço de tradução.", "mjTranslateBaseUrlRequired": "Ao ativar a tradução, é necessário fornecer a URL base.", "mjTranslateApiKeyRequired": "É necessário fornecer uma chave de API ao ativar a tradução.", "mjTranslateModelRequired": "Ao ativar a tradução, é necessário fornecer o nome do modelo."}, "addTokenQuotaModal": {"title": "Gerenciamento de saldo de token {{username}}", "defaultReason": "Operação do administrador", "enterRechargeAmount": "Por favor, insira o valor do recarregamento.", "enterRemark": "Por favor, insira uma mensagem de observação.", "confirmOperation": "Confirmar operação", "confirmContent": "Você confirma {{username}}{{action}}{{amount}} dólares {{updateExpiry}}?", "recharge": "recarregar", "deduct": "dedu<PERSON>", "andUpdateExpiry": "e atualize o prazo de validade do saldo para {{days}} dias.", "alertMessage": "A entrada de números negativos pode reduzir o saldo do usuário.", "rechargeAmount": "Limite de recarga", "operationReason": "Razão da operação", "finalBalance": "Saldo final"}, "billingType": {"1": "Cobrança por quantidade", "2": "Cobrança por uso", "3": "Cobrança mista", "4": "Prioridade por quantidade", "5": "Por ordem de prioridade secundária.", "payAsYouGo": "Cobrança por quantidade", "payPerRequest": "Cobrança por uso", "hybrid": "Cobrança mista", "payAsYouGoPriority": "Prioridade por quantidade", "payPerRequestPriority": "Por ordem de prioridade secundária.", "unknown": "man<PERSON> desconhecida"}, "packagePlanAdmin": {"title": "pacote", "table": {"title": "Gerenciamento de pacotes", "toolBar": {"add": "Novo pacote", "delete": "Excluir pacote"}, "action": {"edit": "editar", "delete": "Excluir", "detail": "<PERSON><PERSON><PERSON>", "recovery": "Disponível para venda", "offline": "Retirar do ar"}}, "modal": {"title": {"add": "Novo pacote", "edit": "Pacote de edição"}, "field": {"name": "Nome do pacote", "type": {"default": "<PERSON><PERSON><PERSON> de paco<PERSON>", "type1": "Pacote de limite", "type2": "pacote por sessão", "type3": "Pacote de duração"}, "group": "Grupo de pacotes", "description": "Descrição do pacote", "price": "Preço do pacote", "valid_period": "Data de validade", "first_buy_discount": "Desconto para primeira compra", "rate_limit_num": "limitar o número de vezes", "rate_limit_duration": "período de restrição", "inventory": "estoque de pacotes", "available_models": "Modelos disponíveis", "quota": "Limite do pacote", "times": "Númer<PERSON>"}, "footer": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmação", "update": "atualização"}}}, "login": {"title": "<PERSON><PERSON>", "username": "nome de usuário", "password": "<PERSON><PERSON>a", "login": "<PERSON><PERSON>", "otherLoginMethods": "Outras formas de login", "register": "Registrar uma conta", "accountLogin": "Login na conta", "phoneLogin": "Login com número de telefone", "usernamePlaceholder": "nome de usuário", "usernameRequired": "Por favor, insira o nome de usuário!", "passwordPlaceholder": "<PERSON><PERSON>a", "passwordRequired": "Por favor, insira a senha!", "passwordMaxLength": "A senha não pode ter mais de 20 caracteres!", "phonePlaceholder": "número de telefone celular", "phoneRequired": "Por favor, insira o número de telefone!", "phoneFormatError": "Formato de número de telefone incorreto!", "smsCodePlaceholder": "Código de verificação por SMS", "smsCodeCountdown": "<PERSON><PERSON><PERSON> em {{count}} segundos.", "getSmsCode": "Obter código de verificação", "agreementText": "Eu concordo.", "privacyPolicy": "Política de Privacidade", "and": "e", "serviceAgreement": "\"Contrato de Serviço\"", "alreadyLoggedIn": "Você está logado.", "weakPasswordWarning": "Sua senha é muito simples, por favor, altere-a imediatamente!", "welcomeMessage": "Bem-vindo!", "captchaError": "Código de verificação incorreto.", "credentialsError": "Nome de usuário ou senha incorretos", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "captchaExpired": "O código de verificação não existe ou expirou.", "loginFailed": "Falha ao fazer login: {{message}}", "captchaRequired": "Por favor, insira o código de verificação!", "captchaPlaceholder": "código de verificação", "smsSent": "O código de verificação por SMS foi enviado com sucesso.", "smsSendFailed": "Falha no envio do código de verificação por SMS.", "agreementWarning": "Por favor, concorde primeiro com a \"Política de Privacidade\" e o \"Acordo de Serviço\".", "turnstileWarning": "Por favor, tente novamente mais tarde, o Turnstile está verificando o ambiente do usuário!", "loginSuccess": "<PERSON>gin bem-sucedido"}, "register": {"title": "Registro", "usernameRequired": "Por favor, insira o nome de usuário!", "usernameNoAt": "O nome de usuário não pode conter o símbolo @.", "usernameNoChinese": "O nome de usuário não pode conter caracteres chineses.", "usernameLength": "O comprimento do nome de usuário deve ser de 4 a 12 caracteres.", "usernamePlaceholder": "nome de usuário", "passwordRequired": "Por favor, insira a senha!", "passwordLength": "A senha deve ter entre 8 e 20 caracteres.", "passwordPlaceholder": "<PERSON><PERSON>a", "confirmPasswordRequired": "Por favor, confirme a senha!", "passwordMismatch": "As senhas digitadas duas vezes não são consistentes!", "confirmPasswordPlaceholder": "Confirmar <PERSON><PERSON><PERSON>", "emailInvalid": "Por favor, insira um endereço de e-mail válido!", "emailRequired": "Por favor, insira o e-mail!", "emailPlaceholder": "endereço de e-mail", "emailCodeRequired": "Por favor, insira o código de verificação do e-mail!", "emailCodePlaceholder": "Código de verificação do e-mail", "enterCaptcha": "Por favor, insira o código de verificação.", "resendEmailCode": "{{seconds}} segundos para reenviar.", "getEmailCode": "Obter código de verificação", "phoneRequired": "Por favor, insira o número de telefone!", "phoneInvalid": "O formato do número de telefone está incorreto!", "phonePlaceholder": "número de telefone celular", "smsCodeRequired": "Por favor, insira o código de verificação por SMS!", "smsCodePlaceholder": "Código de verificação por SMS", "resendSmsCode": "Reenviar em {{seconds}} segundos.", "getSmsCode": "Obter código de verificação", "captchaRequired": "Por favor, insira o código de verificação!", "captchaPlaceholder": "código de verificação", "inviteCodePlaceholder": "Código de convite (opcional)", "submit": "Registro", "successMessage": "Registro bem-sucedido", "failMessage": "<PERSON><PERSON> falhou.", "emailCodeSent": "O código de verificação do e-mail foi enviado.", "smsCodeSent": "O código de verificação por SMS foi enviado.", "confirm": "Confirmação", "emailVerifyTitle": "Verificação de e-mail", "smsVerifyTitle": "Verificação por SMS", "registerVerifyTitle": "Verificação de registro"}, "profile": {"timezone": "fuso hor<PERSON><PERSON>", "phoneNumber": "número de telefone celular", "emailAddress": "endereço de e-mail", "wechatAccount": "Conta do WeChat", "telegramAccount": "Conta do Telegram", "bindTelegram": "Vincular o Telegram", "balanceValidPeriod": "Validade do saldo", "lastLoginIP": "Último IP de login", "lastLoginTime": "<PERSON><PERSON><PERSON> ho<PERSON> de login", "inviteCode": "código de convite", "inviteLink": "Link de convite", "generate": "gerar", "pendingEarnings": "Rendimento a ser utilizado", "transfer": "Transferência", "totalEarnings": "Receita total", "accountBalance": "Sal<PERSON> da conta", "totalConsumption": "consumo acumulado", "callCount": "Número de <PERSON>", "invitedUsers": "<PERSON><PERSON><PERSON> us<PERSON>", "promotionInfo": "Informação promocional", "inviteDescription": "Um convite, reembol<PERSON>; quanto mais convites, mais reembolso.", "userInfo": "Informações do usuário", "availableModels": "Modelos disponíveis", "modelNameCopied": "<PERSON><PERSON> copiado.", "noAvailableModels": "Nenhum modelo disponível no momento.", "accountOptions": "Opções de conta", "changePassword": "<PERSON><PERSON><PERSON><PERSON>", "systemToken": "token do sistema", "unsubscribe": "<PERSON><PERSON><PERSON>", "educationCertification": "Certificação educacional", "timezoneUpdateSuccess": "Atualização de fuso horário bem-sucedida.", "inviteLinkCopied": "O link de convite foi copiado.", "inviteLinkCopyFailed": "Falha ao copiar o link de convite.", "inviteLinkGenerationFailed": "Falha na geração do link de convite.", "allModelsCopied": "Todos os modelos foram copiados para a área de transferência.", "copyAllModels": "Copiar todos os modelos", "totalModels": "Número de modelos disponíveis", "expired": "<PERSON><PERSON> expirado", "validPeriod": "prazo de validade", "longTermValid": "Válido por um longo período.", "failedToLoadModels": "Falha ao carregar a lista de modelos.", "accessTokens": "Text", "accessTokensManagement": "Text", "accessTokenDescription": "Text", "tokenNameLabel": "Title", "tokenNamePlaceholder": "Title", "presetPermissions": "permissões predefinidas", "detailPermissions": "Text", "validityPeriod": "Text", "validityPeriodExtra": "Text", "remarkLabel": "observação", "remarkPlaceholder": "Please enter...", "createNewToken": "Text", "tokenCreatedSuccess": "Success", "tokenSavePrompt": "Text", "copyToken": "copiar token", "readPermission": "Text", "writePermission": "permissão de escrita", "deletePermission": "Text", "tokenManagement": "gerenciamento de tokens", "channelManagement": "Text", "logView": "visualização de logs", "statisticsView": "Text", "userManagement": "gerenciamento de usuários", "quotaManagement": "Text", "readOnlyPermission": "permissão somente leitura", "writeOnlyPermission": "Text", "readWritePermission": "permissão de leitura e escrita", "standardPermission": "Text", "fullPermission": "permissão completa", "selectPermission": "Option", "tokenStatus": "status", "tokenEnabled": "Text", "tokenDisabled": "desabilitado", "enableToken": "Text", "disableToken": "desabilitado", "deleteToken": "Text", "deleteTokenConfirm": "Action", "disableTokenConfirm": "Action", "enableTokenConfirm": "Text", "tokenExpiryNever": "Text", "accessTokensInfo": "Description", "accessTokensInfoDetail1": "Text", "accessTokensInfoDetail2": "Description", "accessTokensInfoDetail3": "Text", "accessTokensInfoDetail4": "Description", "accessTokensInfoDetail5": "Description", "noPermission": "sem permissão para esta operação"}, "topup": {"onlineRecharge": "Recarga online", "cardRedemption": "Código de resgate", "accountBalance": "Sal<PERSON> da conta", "rechargeReminder": "Lembrete de recarga", "reminder1": "1. O saldo pode ser utilizado para chamadas de modelo, compra de pacotes, etc.", "reminder2": "2. Se o valor não for creditado após o pagamento, entre em contato com o atendimento ao cliente para resolver.", "reminder3": "3. O saldo não pode ser retirado, mas pode ser transferido dentro do mesmo grupo de usuários.", "reminder4WithTransfer": "4. <PERSON><PERSON><PERSON> o recarregamento bem-sucedido, a validade do saldo da conta será redefinida para", "reminder4WithoutTransfer": "3. A<PERSON><PERSON> a recarga bem-sucedida, o prazo de validade do saldo da conta será redefinido para", "days": "céu", "paymentSuccess": "Pagamento bem-sucedido", "paymentError": "Erro ao efetuar o pagamento", "paymentAmount": "Valor a ser pago:", "purchaseAmount": "Limite de compra: $", "yuan": "yuan", "or": "ou", "usd": "dólar americano", "cny": "yuan", "enterAmount": "Por favor, insira o valor do recarregamento!", "amountPlaceholder": "Por favor, insira o valor do depósito, a partir de {{min}} d<PERSON>lar<PERSON>.", "amountUpdateError": "Erro ao atualizar o valor.", "alipay": "Alipay", "wechat": "WeChat", "visaMastercard": "Visa / Mastercard", "cardFormatError": "Formato do código de troca está incorreto.", "redeemSuccess": "{{amount}} trocado com sucesso!", "redeemError": "<PERSON>rro na troca, por favor tente novamente mais tarde.", "enterCardKey": "Por favor, insira o código de resgate.", "cardKeyPlaceholder": "Por favor, insira o código de troca.", "buyCardKey": "Comprar código de troca de cartão", "redeem": "Liquidação imediata", "record": {"title": "Registro de recarga", "amount": "Limite de recarga", "payment": "Valor a ser pago", "paymentMethod": "Método de pagamento", "orderNo": "Número do pedido", "status": "estado", "createTime": "Data de criação", "statusSuccess": "sucesso", "statusPending": "Processando", "statusFailed": "falha"}, "paymentMethodAlipay": "Alipay", "paymentMethodWxpay": "WeChat", "paymentMethodUSDT_TRC20": "USDT", "paymentMethodEVM_ETH_ETH": "ETH", "paymentMethodPaypal": "PayPal", "paymentMethodAdmin": "Administrador", "paymentMethodRedeem": "código de troca", "alipayF2F": "Text"}, "pricing": {"fetchErrorMessage": "Ocorreu uma anomalia ao obter informações de preço, por favor, entre em contato com o administrador.", "availableModelErrorMessage": "Ocorreu uma exceção ao obter o modelo disponível, por favor, entre em contato com o administrador.", "modelName": "Nome do modelo", "billingType": "Tipo de cobrança", "price": "preço", "ratio": "ampliação", "promptPriceSame": "Preço sugerido: igual à tarifa original", "completionPriceSame": "Preço de complementação: igual à tarifa original.", "promptPrice": "Preço sugerido: $ {{price}} / 1M tokens", "completionPrice": "Preço complementar: $ {{price}} / 1M tokens", "promptRatioSame": "Taxa de sugestão: igual à taxa original.", "completionRatioSame": "Taxa de complemento: igual à taxa original.", "promptRatio": "Taxa de sugestão: {{ratio}}", "completionRatio": "Taxa de complementação: {{ratio}}", "payAsYouGo": "Pagamento por uso - Chat", "fixedPrice": "$ {{preço}} / vez", "payPerRequest": "Pagamento por uso - Chat", "dynamicPrice": "$ {{price}} / vez", "payPerRequestAPI": "Pagamento por uso - API", "loadingTip": "Obtendo informações de preços...", "userGroupRatio": "Sua taxa de agrupamento de usuários é: {{ratio}}", "readFailed": "Falha na leitura", "billingFormula": "Custo por uso = Taxa de conversão × Multiplicador de grupo × Multiplicador de modelo × (Número de tokens de aviso + Número de tokens de conclusão × Multiplicador de conclusão) / 500000 (unidade: dólares)", "billingFormula1": "Taxa de conversão = (nova taxa de recarga / taxa de recarga original) × (nova taxa de agrupamento / taxa de agrupamento original)", "generatedBy": "Esta página foi gerada automaticamente pelo {{systemName}}.", "modalTitle": "Detalhes do preço", "perMillionTokens": "/1M tokens", "close": "fechar", "searchPlaceholder": "Pesquisar nome do modelo", "viewGroups": "Ver grupos", "copiedToClipboard": "Copiado para a área de transferência.", "copyFailed": "Cópia falhou.", "groupName": "Nome do grupo", "availableGroups": "Grupos disponíveis para o modelo {{model}}", "noGroupsAvailable": "Não há grupos disponíveis.", "modelGroupsErrorMessage": "Falha ao obter dados do grupo de modelos.", "currentGroup": "Grupo atual", "copyModelName": "Copiar nome do modelo", "groupRatio": "Taxa de agrupamento", "closeModal": "fechar", "groupsForModel": "Modelo disponível em grupos", "actions": "Operação", "filterByGroup": "Filtrar por grupo", "groupSwitched": "Mudou para o grupo: {{group}}", "showAdjustedPrice": "Mostrar preço após ajuste de agrupamento (taxa atual: {{ratio}})"}, "guestQuery": {"usageTime": "Uso do tempo", "modelName": "Nome do modelo", "promptTooltip": "Entrada de consumo de Tokens", "completionTooltip": "Saída de consumo de tokens", "quotaConsumed": "limite de consumo", "pasteConfirm": "Detectou um token válido na área de transferência, deseja colar?", "queryFailed": "Consulta falhou.", "tokenExpired": "O token expirou.", "tokenExhausted": "O limite deste token foi atingido.", "invalidToken": "Por favor, insira o token correto.", "focusRequired": "Por favor, certifique-se de que a página esteja em estado de foco.", "queryFirst": "Por favor, consulte primeiro.", "tokenInfoText": "Total de tokens: {{totalQuota}}  \nTokens consumidos: {{usedQuota}}  \nSaldo de tokens: {{remainQuota}}  \nNúmero de chamadas: {{callCount}}  \nVálido até: {{validUntil}}", "unlimited": "sem restrições", "neverExpire": "nunca expira", "infoCopied": "As informações do token foram copiadas para a área de transferência.", "copyFailed": "Cópia falhou.", "noDataToExport": "Não há dados para exportar.", "prompt": "Dica", "completion": "completar", "disabled": "Consulta de visitantes não ativada.", "tokenQuery": "Consulta de token", "tokenPlaceholder": "Por favor, insira o token que deseja consultar (sk-xxx).", "tokenInfo": "Informações do token", "copyInfo": "Copiar informações", "totalQuota": "Total de tokens", "usedQuota": "Consumo de tokens", "remainQuota": "<PERSON>do do <PERSON>", "callCount": "Número de <PERSON>", "validUntil": "Data de validade até", "currentRPM": "RPM atual", "currentTPM": "Atualmente, TPM", "callLogs": "Registro de chamadas", "exportLogs": "Exportar log."}, "agencyProfile": {"fetchError": "Falha ao obter informações do agente.", "fetchCommissionError": "Falha ao obter a lista de comissões.", "systemPreset": "Configuração do sistema", "lowerRatioWarning": "A taxa é inferior à pré-definição do sistema.", "lowerRatioMessage": "As tarifas a seguir estão abaixo do valor predefinido pelo sistema, por favor, faça a alteração o mais rápido possível:", "cancelRatioEdit": "Cancelar a taxa de modificação", "updateSuccess": "Atualização bem-sucedida", "updateError": "Falha na atualização das informações do agente:", "updateFailed": "Atualização falhou:", "customPriceUpdateSuccess": "Atualização de preço personalizada bem-sucedida.", "customPriceUpdateError": "Atualização de preço personalizado falhou:", "time": "tempo", "type": "tipo", "agencyCommission": "Comissão de retorno para agentes.", "unknownType": "<PERSON><PERSON><PERSON> desconhecido", "amount": "quantia", "balance": "saldo", "description": "Descrição", "group": "Grupo", "customRate": "Taxa personalizada", "systemDefaultRate": "Taxa padrão do sistema", "action": "Operação", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "edit": "editar", "agencyConsole": "Console <PERSON>", "agencyInfo": "Informações do agente", "editInfo": "Editar informações", "agencyName": "Nome do agente", "agencyLevel": "Nível do agente", "level1": "Nível 1", "subordinateUsers": "usu<PERSON>rio subordinado", "totalSales": "<PERSON><PERSON><PERSON> to<PERSON>s", "commissionIncome": "Renda de comissão", "cumulativeEarnings": "rendimento acumulado", "agencyFunctions": "Text", "hideSubordinateUsers": "Ocultar usuários subordinados", "viewSubordinateUsers": "Ver usuários subordinados", "hideCommissionDetails": "<PERSON><PERSON>lta<PERSON> de<PERSON>hes da comissão", "viewCommissionDetails": "<PERSON><PERSON> de<PERSON>hes da comissão", "hideCustomPrice": "Ocultar preço personalizado", "setCustomPrice": "Definir preço personalizado", "subordinateUsersList": "Lista de usuários subordinados", "commissionRecords": "Registro de comissões", "customPriceSettings": "Configuração de preço personalizado", "saveChanges": "<PERSON><PERSON>", "editAgencyInfo": "Editar informações do agente.", "logo": "Logotipo", "setAgencyLogo": "Configurar o logo do agente.", "customHomepage": "Página inicial personalizada", "aboutContent": "Sobre o conteúdo", "newHomepageConfig": "Nova configuração da página inicial", "customAnnouncement": "<PERSON><PERSON><PERSON>", "customRechargeGroupRateJson": "Taxa de grupo de recarga personalizada em JSON", "customRechargeRate": "Taxa de recarga personalizada", "viewSystemDefaultRate": "Verificar a taxa padrão do sistema.", "rateComparison": "Comparação de taxas", "comparisonResult": "Resultados da comparação", "higherThanSystem": "Acima do sistema", "lowerThanSystem": "Abaixo do sistema", "equalToSystem": "igual ao sistema", "unknown": "Desconhecido", "notAnAgentYet": "Você ainda não é um agente.", "becomeAnAgent": "<PERSON><PERSON>-se um agente.", "startYourOnlineBusiness": "🌟 Comece facilmente o seu negócio online", "becomeOurAgent": "Torne-se nosso agente e desfrute de uma experiência de empreendedorismo sem pressão:", "noInventory": "💼 Sem necessidade de estoque, pressão de giro de capital zero.", "instantCommission": "💰 Venda de divisão instantânea, obtenha retornos substanciais proporcionalmente.", "easyManagement": "🖥️ Sem necessidade de tecnologia de construção de sites, gerencie sua loja online com facilidade.", "flexibleDomainChoice": "🌐 Escolha de domínio flexível", "youCan": "Você pode:", "useOwnDomain": "🏠 Usar seu pró<PERSON><PERSON>", "orUseOurSubdomain": "🎁 Ou podemos fornecer um subdomínio exclusivo para você.", "convenientStart": "Text", "actNow": "🚀 Aja agora!", "contactAdmin": "Entre em contato com o administrador do site e comece sua jornada como agente! 📞", "applyNow": "Candidate-se agora", "contactCooperation": "Consultoria e colaboração", "understandPolicy": "<PERSON><PERSON>reender as políticas de agentes e os detalhes da colaboração.", "provideDomain": "<PERSON><PERSON><PERSON>", "configDomain": "Forneça seu domínio e nós o ajudaremos a configurá-lo.", "promoteAndEarn": "Promoção de lucros", "startPromoting": "Comece a promover seu site de afiliado e ganhe comissões.", "noDeploymentWorries": "Não se preocupe com a complexidade da implantação de serviços em nuvem, canais de pagamento e problemas de estoque.", "easySetup": "Basta fornecer o nome de domínio e configurar conforme o tutorial para iniciar facilmente o negócio de revenda de API em nível empresarial.", "customizeContent": "Você pode personalizar preços, informações do site, SEO, logotipo e outros conteúdos.", "commissionBenefits": "Como agente, você receberá uma participação nos recargas dos usuários, o sistema deduz automaticamente os custos, e o valor restante pode ser retirado a qualquer momento.", "joinNowBenefit": "Junte-se a nós agora e aproveite os benefícios da era da IA!", "groups": {"student": "estudante universitário", "studentDesc": "Com tempo de sobra, espero aumentar a renda de forma fácil através de atividades promocionais, para cobrir parte das despesas de vida e entretenimento.", "partTime": "Time", "partTimeDesc": "Não é necessário investir muito tempo, basta promover de forma simples durante o tempo livre do trabalho para ganhar uma renda extra com facilidade.", "mediaWorker": "Profissional de mídia independente", "mediaWorkerDesc": "Com uma base de fãs certa, basta adicionar um link ao final do artigo ou postagem para facilmente gerar uma renda extra.", "freelancer": "freelancer", "freelancerDesc": "Ter muito tempo flexível e aumentar facilmente a renda extra apenas participando de atividades de vendas."}, "stories": {"story1": {"name": "<PERSON><PERSON>", "role": "estudante universitário"}, "story2": {"name": "Sen<PERSON>", "role": "professor de <PERSON><PERSON><PERSON>"}, "story3": {"name": "<PERSON><PERSON>", "role": "comércio eletrônico"}, "story4": {"name": "<PERSON><PERSON>", "role": "mídia independente"}, "story5": {"name": "<PERSON><PERSON>", "role": "pesquisador científico"}, "story6": {"name": "<PERSON><PERSON>", "role": "Blogueiro do Xiaohongshu"}, "story7": {"name": "<PERSON><PERSON>", "role": "mídia independente"}, "story8": {"name": "<PERSON><PERSON>", "role": "Indústria de TI"}}, "earnedAmount": "<PERSON><PERSON> ganhou {{amount}}", "applyForAgentNow": "Candidate-se agora para se tornar um agente.", "businessLinesConnected": "Mais de 40 linhas de negócios já foram integradas.", "agencyJoin": "Franquia de representação", "becomeExclusiveAgent": "Torne-se nosso agente exclusivo.", "startBusinessJourney": "Comece sua jornada de negócios com facilidade~", "welcomeToAgencyPage": "Bem-vindo à nossa página de agentes!", "earningsTitle": "Title", "becomeAgentSteps": "Tornar-se um agente: etapas.", "agencyRules": "Regras de representação", "suitableGroups": "Público-alvo", "agencyImages": {"becomeAgent": "<PERSON><PERSON>-se um agente.", "agencyBusiness": "Serviço de representação"}, "rules": {"howToEstablishRelation": "Text", "howToEstablishRelationAnswer": "Registrar-se no seu site deagente é ser seu usuário.", "canSetPrice": "<PERSON><PERSON> definir o preço de venda?", "canSetPriceAnswer": "Claro! Mas seu preço de venda deve ser pelo menos 10% superior ao preço de custo.", "commissionShare": "Quanto eu posso ganhar de comissão?", "commissionShareAnswer": {"assumption": "Suponha: o seu preço de custo é $1=1 yuan, o seu preço de venda é $1=2 yuan, e a sua taxa de comissão é de 90%.", "example": "O usuário comprou $10 no seu site, gastando 20 yuan.", "calculation": "Você pode obter: (2-1)*10*0,9 = 9 yuans", "explanation": "Interpretação: (Preço de venda - Preço de custo) * Volume de transações * Taxa de comissão"}}}, "error": {"title": "erro", "content": "Ocorreu um erro."}, "loading": {"title": "Carregando...", "content": "Carregando..."}, "notfound": {"title": "404", "content": "Página não encontrada"}, "servererror": {"title": "500", "content": "Erro do servidor"}, "unauthorized": {"title": "401", "content": "Não autorizado"}, "forbidden": {"title": "403", "content": "<PERSON><PERSON> proibido"}, "networkerror": {"title": "Erro de rede", "content": "Erro de rede"}, "timeout": {"title": "tempo excedido", "content": "Tempo de solicitação esgotado."}, "noresult": {"title": "Sem resultado", "content": "Sem resultados"}, "nopermission": {"title": "<PERSON><PERSON> permis<PERSON>", "content": "<PERSON><PERSON> permis<PERSON>"}, "channelBridge": {"title": "Integração rápida de canais", "channelPlatform": "plataforma de canal", "billingMethod": "Método de cobrança", "channelName": "Nome do canal", "remark": "<PERSON>a", "availableGroups": "Grupos disponíveis", "availableModels": "Modelos disponíveis", "channelKey": "Chave de canal", "proxyAddress": "Endereço de interface", "cancel": "<PERSON><PERSON><PERSON>", "submit": "Submeter", "gpt35Models": "Modelo GPT-3.5", "gpt4Models": "Modelo GPT-4", "clear": "Limpar", "customModelName": "Nome do modelo personalizado", "add": "<PERSON><PERSON><PERSON><PERSON>", "moreConfigReminder": "<PERSON><PERSON> configura<PERSON>, por favor, salve o canal e depois edite.", "quickIntegration": "Conexão com um clique", "selectBillingMethod": "Por favor, escolha o método de cobrança.", "enterChannelName": "Por favor, insira o nome do canal.", "enterChannelRemark": "Por favor, insira a observação do canal.", "selectAvailableGroups": "Selecione o grupo que pode usar este canal.", "selectAvailableModels": "Escolher/pesquisar modelos disponíveis neste canal.", "enterChannelKey": "Por favor, insira a chave do canal.", "proxyAddressPlaceholder": "Esta opção é opcional e serve para realizar chamadas de API através de um proxy. Por favor, insira o endereço do proxy.", "includes16kModels": "Inclui modelo de 16k.", "excludes32kModels": "Não inclui o modelo de 32k.", "cleared": "<PERSON><PERSON>o.", "addCustomModel": "Adicionar modelo personalizado", "clipboardTokenDetected": "Detectou um token válido na área de transferência, deseja colar?", "channelIntegrationSuccess": "Success", "channelIntegrationFailed": "Falha na integração do canal:"}, "about": {"loading": "Obter conteúdo mais recente...", "noContent": "O administrador não definiu o conteúdo da página sobre.", "loadFailed": "Falha ao carregar o conteúdo..."}, "onlineTopupRecord": {"title": "Registro de recarga", "columns": {"id": "ID", "username": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Limite de recarga", "money": "Valor a ser pago", "paymentMethod": "Método de pagamento", "tradeNo": "Número do pedido", "status": "estado", "createTime": "Data de criação"}, "status": {"success": "sucesso", "pending": "Processando", "failed": "falha"}, "paymentMethod": {"alipay": "Alipay", "wxpay": "WeChat", "USDT_TRC20": "USDT", "EVM_ETH_ETH": "ETH", "paypal": "PayPal"}}, "logContentDetail": {"description": "Descrição da informação", "downstreamError": "Erro a montante", "originalError": "Erro original", "requestParams": "Parâmetros de solicitação", "copy": "copiar"}, "viewMode": {"switchTo": "<PERSON><PERSON> para a perspectiva {{mode}}.", "cost": "custo", "usage": "<PERSON><PERSON>"}, "agenciesTable": {"title": "Gestão de representantes", "addAgency": "Novo agente autorizado", "columns": {"id": "ID", "userId": "ID do usuário", "name": "Nome", "domain": "<PERSON><PERSON><PERSON><PERSON>", "commissionRate": "Taxa de comissão", "salesVolume": "vendas", "userCount": "Número de usuários", "commissionIncome": "Renda de comissão", "historicalCommission": "rendimento acumulado", "actions": "Operação"}, "confirm": {"deleteTitle": "Você tem certeza de que deseja excluir este agente?", "updateName": "Atualizando o nome do agente...", "updateSuccess": "Atualização bem-sucedida", "updateFailed": "Atualização falhou", "deleteSuccess": "Excluído com sucesso!"}, "messages": {"getListFailed": "Falha ao obter a lista de agentes: {{message}}", "deleteSuccess": "Exclusão bem-sucedida!", "loadingData": "Carregando..."}}, "units": {"times": "próximo", "percentage": "{{value}}%", "formatUsage": "{{name}}: {{value}} vezes ({{percent}}%)"}, "dailyUsage": {"total": "Total", "totalCost": "Custo total", "tooltipTitle": {"cost": "Situação de custos", "usage": "<PERSON><PERSON>"}, "yAxisName": {"cost": "Custo (USD)", "usage": "Uso (USD)"}}, "dailyUsageByModel": {"total": "Total", "tooltipTotal": "Total: $ {{value}}", "switchTo": "mudar para", "cost": "custo", "usage": "<PERSON><PERSON>", "perspective": "perspectiva", "granularity": {"hour": "por hora", "day": "por dia", "week": "por semana", "month": "por mês"}}, "checkinModal": {"title": "Por favor, complete a verificação.", "captchaPlaceholder": "código de verificação", "confirm": "Certo", "close": "fechar"}, "balanceTransfer": {"title": "Transferência entre contas", "accountInfo": {"balance": "Sal<PERSON> da conta", "transferFee": "Taxa de transferência", "groupNote": "A transferência só pode ser feita entre grupos de usuários iguais."}, "form": {"receiverId": "ID do receptor", "receiverUsername": "Nome de usuário do destinatário", "remark": "Informações adicionais", "amount": "Valor da transferência", "expectedFee": "Previsão de cobrança", "submit": "Iniciar transfer<PERSON><PERSON>"}, "result": {"success": "Transferência bem-sucedida", "continueTransfer": "Continuar a transferência", "viewRecord": "Ver registros"}, "warning": {"disabled": "O administrador não ativou a função de transferência, portanto, não está disponível no momento."}, "placeholder": {"autoCalculate": "O valor da transferência é calculado automaticamente."}}, "channelsTable": {"title": "Gestão de canais", "columns": {"id": "ID", "name": "Nome", "type": "tipo", "key": "chave", "base": "Endere<PERSON><PERSON> da interface", "models": "modelo", "weight": "peso", "priority": "prioridade", "retryInterval": "Intervalo de retentativa", "responseTime": "Tempo de resposta", "rpm": "RPM", "status": "Status", "quota": "saldo", "expireTime": "data de validade", "group": "Grupo", "billingType": "Tipo de cobrança", "actions": "Operação", "fusing": "fusão", "sort": "prioridade", "createdTime": "Time", "disableReason": "motivo da desabilitação"}, "status": {"all": "todos", "normal": "normal", "enabled": "Estado normal", "manualDisabled": "Desativar manualmente", "waitingRetry": "<PERSON><PERSON><PERSON><PERSON>", "suspended": "Suspensão de uso", "specified": "Estado designado", "allDisabled": "Desativar", "specifiedDisabled": "Especificar tipo de desativação", "partiallyDisabled": "Status"}, "placeholder": {"selectGroup": "Selecione/Pesquise Grupo", "selectStatus": "Selecionar o estado do canal", "inputSelectModel": "Insira/Selecione o nome do modelo", "selectFusingStatus": "Selecionar o estado de fusão automática."}, "quota": {"usageAmount": "Consumo: {amount}", "remainingAmount": "Restante: {amount}", "customTotalAmount": "Total personalizado: {amount}", "updateNotSupported": "<PERSON><PERSON><PERSON><PERSON>, não é possível atualizar o saldo, por favor, utilize o saldo personalizado.", "details": "<PERSON><PERSON><PERSON>", "sufficient": "suficiente"}, "actions": {"edit": "editar", "copy": "Canal de clonagem", "delete": "Excluir canal", "enable": "Ativar", "disable": "Desativar", "test": "teste", "advancedTest": "<PERSON>e <PERSON>", "viewLog": "Registro de canal", "viewAbility": "Ver capacidade", "cleanUsage": "Limpar o usado", "updateBalance": "<PERSON><PERSON><PERSON><PERSON> saldo", "copyKey": "Copiar chave"}, "confirm": {"deleteTitle": "Confirmação de exclusão", "deleteContent": "Você tem certeza de que deseja excluir o canal {{name}} (#{{id}})?", "cleanUsageTitle": "Confirmação de uso de limpeza", "cleanUsageContent": "Você tem certeza de que deseja limpar o valor consumido do canal {{name}} (#{{id}})?", "testTitle": "Confirmação de teste", "testContent": "Você tem certeza de que deseja testar o canal {{status}}?", "testNote": "Atenção: esta função precisa ser usada em conjunto com [Configuração] -> [Intermediário] -> [Configurações de Monitoramento] -> [Desativar canal em caso de falha, ativar canal em caso de sucesso]. Se as configurações relacionadas não estiverem ativadas, o canal não será desativado ou ativado automaticamente após a conclusão do teste.", "deleteDisabledTitle": "Confirmação de exclusão", "deleteDisabledContent": "Você tem certeza de que deseja excluir todos os canais {{type}}?"}, "messages": {"operationSuccess": "Operação bem-sucedida", "operationSuccessWithSort": "Operação bem-sucedida, a classificação dos canais pode ter mudado, recomenda-se classificar por ID!", "operationFailed": "Operação falhou: {{message}}", "testRunning": "Canal {{name}}(#{{id}}) em execução de teste, por favor aguarde...", "testSuccess": "Canal \"{{name}}(#{{id}})\" {{model}} teste bem-sucedido, tempo de resposta {{time}} segundos.", "testFailed": "O teste do canal \"{{name}}(#{{id}})\" {{model}} falhou. Código de status: {{code}}, Motivo: {{reason}}, clique para ver detalhes.", "testStarted": "Comece a testar o canal {{status}}, por favor, atualize a página mais tarde para ver os resultados. A aplicação dos resultados do teste depende das suas configurações de monitoramento.", "testOperationFailed": "<PERSON>e falhou.", "deleteSuccess": "Sucesso ao remover {{count}} canais.", "deleteFailed": "Falha ao deletar: {{message}}", "modelPrefix": "Modelo {{model}}", "channelInfo": "Informações de canal", "channelDetail": "{{nome}}(#{{id}}){{infoModelo}}", "updateBalanceSuccess": "Atualização de saldo do canal \"{{name}}\" bem-sucedida.", "updateBalanceFailed": "Atualização de saldo do canal \"{{name}}\" falhou: {{message}}", "updateAllBalanceStarted": "Comece a atualizar o saldo de todos os canais em estado normal.", "updateAllBalanceSuccess": "Todos os saldos dos canais foram atualizados com sucesso.", "fetchGroupError": "Erro ao obter dados do grupo de canais: {{response}}", "fetchChannelError": "Falha ao obter dados do canal: {{message}}", "selectChannelFirst": "Por favor, selecione o canal que deseja excluir.", "deleteDisabledSuccess": "Removidos todos os canais {{type}}, totalizando {{count}}.", "deleteOperationFailed": "Falha ao excluir.", "copySuccess": "Cópia be<PERSON>-sucedida", "copyFailed": "Cópia falhou: {{message}}", "emptyKey": "A chave está vazia.", "testSuccessWithWarnings": "Message", "viewDetails": "Message", "fetchChannelDetailError": "Message", "topupSuccess": "recarga bem-sucedida", "topupFailed": "Message"}, "popover": {"channelInfo": "Informações de canal"}, "menu": {"deleteManualDisabled": "Remover canais desativados manualmente", "deleteWaitingRetry": "Remover canal de espera para reinício.", "deleteSuspended": "Remover canais suspensos.", "testAll": "Testar todos os canais", "testNormal": "Teste de canal normal", "testManualDisabled": "Teste de desativação manual do canal", "testWaitingRetry": "Teste de espera para reiniciar o canal.", "testSuspended": "Teste de suspensão de uso do canal", "deleteDisabledAccount": "Text", "deleteQuotaExceeded": "Text", "deleteRateLimitExceeded": "excluir canais com limite de frequência", "deleteInvalidKey": "Text", "deleteConnectionError": "excluir canais com erro de conexão"}, "tooltip": {"testNote": "É necessário configurar [Configurações] -> [Revezamento] -> [Configurações de Monitoramento] -> [Desativar canal em caso de falha, ativar canal em caso de sucesso] para usar. Se não estiver ativado, não desativará ou ativará automaticamente após a conclusão do teste de velocidade."}, "disableReasons": {"account_deactivated": "Text", "quota_exceeded": "cota excedida", "rate_limit_exceeded": "Text", "invalid_key": "chave inválida", "connection_error": "Error"}, "topup": {"reminder1": "Text", "reminder2": "Text"}}, "billingTypes": {"quota": "limite", "times": "vezes"}, "serverLogViewer": {"title": "Visualizador de logs do servidor", "connecting": "Conectando ao servidor...", "downloadSelect": "Escolher arquivo de log para download", "nginxConfig": "Instruções de configuração do Nginx WebSocket", "directAccess": "Se você acessar pelo nome de domínio e não tiver configurado o suporte a WebSocket, o visualizador de logs não funcionará. <PERSON><PERSON><PERSON> caso, você pode acessar diretamente pelo IP e porta do servidor (por exemplo: http://seu-ip:9527).", "domainAccess": "Para acessar através do nome de domínio, é necessário adicionar a seguinte configuração no Nginx para suportar WebSocket:", "buttons": {"pause": "Pausa", "resume": "<PERSON><PERSON><PERSON><PERSON>", "clear": "Limpar"}, "errors": {"fetchFailed": "Falha ao obter a lista de arquivos de log.", "downloadFailed": "Falha ao baixar o arquivo de log.", "wsError": "Erro de conexão WebSocket"}}, "channelScore": {"score": "pontuação", "successRate": "taxa de sucesso", "avgResponseTime": "Tempo médio de resposta", "title": "Pontuação do canal", "hourlyTitle": "Pontuação horária do canal", "dailyTitle": "Pontuação diária do canal", "weeklyTitle": "Pontuação semanal do canal", "monthlyTitle": "Pontuação mensal do canal", "allTimeTitle": "Pontuação geral do canal", "infoTooltip": "A pontuação do canal é uma pontuação composta calculada com base na taxa de sucesso e no tempo de resposta.", "tableView": "Visão em tabela", "chartView": "Visão do gráfico", "refresh": "<PERSON><PERSON><PERSON><PERSON>", "selectModel": "<PERSON><PERSON><PERSON><PERSON> modelo", "allModels": "todos os modelos", "sortByScore": "Ordenado por pontuação", "sortBySuccessRate": "Ordenado por taxa de sucesso", "sortByResponseTime": "Ordenar por tempo de resposta", "noData": "Sem dados disponíveis.", "totalItems": "Total de {{total}} itens", "fetchError": "Falha ao obter dados de pontuação do canal.", "aboutScoring": "Sobre o cálculo de pontuação", "scoringExplanation": "A pontuação do canal é uma pontuação composta calculada com base na taxa de sucesso e no tempo de resposta, com uma pontuação máxima de 1 ponto.", "successRateWeight": "Peso da taxa de sucesso (70%)", "successRateExplanation": "Quanto maior a taxa de sucesso, maior a pontuação.", "responseTimeWeight": "Peso do tempo de resposta (30%)", "responseTimeExplanation": "Um tempo de resposta inferior a 1000 ms recebe a pontuação máxima, enquanto tempos superiores resultam em deduções proporcionais.", "columns": {"rank": "classificação", "channelId": "ID do canal", "channelName": "Nome do canal", "model": "modelo", "totalRequests": "Total de solicitações", "successRequests": "Número de solicitações bem-sucedidas", "failedRequests": "Número de solicitações falhadas", "successRate": "taxa de sucesso", "avgResponseTime": "Tempo médio de resposta", "score": "Pontuação geral", "actions": "Operação"}, "actions": {"viewDetails": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "test": "Canal de teste", "edit": "Canal de edição"}, "tooltips": {"excellent": "excelente", "good": "Bo<PERSON>", "average": "geral", "poor": "pior", "veryPoor": "<PERSON><PERSON> rui<PERSON>."}, "scoringExplanation100": "A pontuação do canal é uma pontuação composta calculada com base na taxa de sucesso e no tempo de resposta, com uma pontuação máxima de 100 pontos."}, "menu": {"channelScores": "Pontuação do canal"}, "relay": {"dispatchOptions": "Opções de agendamento", "preciseWeightCalculation": "Cál<PERSON>lo preciso de pesos", "preciseWeightCalculationTip": "Ao ativar, será utilizado um algoritmo mais preciso para calcular o peso dos canais, o que pode aumentar o uso da CPU.", "channelMetricsEnabled": "Ativar estatísticas de indicadores de canal", "channelMetricsEnabledTip": "Ao ser ativado, coletará indicadores como a taxa de sucesso e o tempo de resposta do canal, utilizados para avaliar o desempenho do canal. Se desativado, esses dados não serão coletados, o que pode reduzir o uso de recursos do sistema.", "channelScoreRoutingEnabled": "Ativar o roteamento baseado na pontuação do canal", "channelScoreRoutingEnabledTip": "Após ser ativado, o sistema ajustará automaticamente a prioridade de alocação de solicitações com base no desempenho histórico dos canais, e os canais com melhor desempenho terão uma maior probabilidade de alocação de solicitações.", "globalIgnoreBillingTypeFilteringEnabled": "Text", "globalIgnoreBillingTypeFilteringEnabledTip": "Text", "globalIgnoreFunctionCallFilteringEnabled": "Text", "globalIgnoreFunctionCallFilteringEnabledTip": "Text", "globalIgnoreImageSupportFilteringEnabled": "Text", "globalIgnoreImageSupportFilteringEnabledTip": "Text"}, "dynamicRouter": {"title": "Gerenciamento de Roteamento Dinâmico", "reloadRoutes": "Recarregar a rota", "exportConfig": "Exportar configuração", "clearConfig": "Limpar configuração", "importantNotice": "Aviso importante", "reloadLimitation": "1. O recarregamento da rota só pode atualizar a configuração das rotas existentes, não podendo adicionar ou remover rotas. Para recarregar completamente a estrutura de rotas, reinicie o aplicativo.", "exportDescription": "2. A exportação de configuração irá exportar as configurações do banco de dados atual para o arquivo router.json, filtrando valores nulos e zeros.", "clearDescription": "3. <PERSON><PERSON> a configuração irá excluir todas as configurações de rotas dinâmicas no banco de dados, e após reiniciar o aplicativo, será recarregado a partir do arquivo router.json.", "routeGroups": "Grupo de roteadores", "upstreamConfig": "Configuração de upstream", "endpointConfig": "Configuração de ponto final", "editRouteGroup": "Editar grupo de roteadores", "editUpstream": "Editar a configuração upstream", "editEndpoint": "Editar a configuração do endpoint", "editJSON": "<PERSON>ar <PERSON>", "confirmClear": "Action", "confirmClearMessage": "Esta operação irá limpar todas as configurações de rotas dinâmicas no banco de dados. Na próxima reinicialização do aplicativo, as configurações serão recarregadas a partir do arquivo de configuração. Você tem certeza de que deseja continuar?", "configCleared": "A configuração de roteamento dinâmico foi limpa, por favor reinicie o aplicativo para aplicar as alterações.", "configExported": "A configuração foi exportada com sucesso para o arquivo.", "configReloaded": "A configuração do roteador foi recarregada com sucesso."}, "notification": {"title": "Title", "subscriptionEvents": "eventos de assinatura", "notificationMethods": "Message", "alertSettings": "configurações de alerta", "emailConfig": "Message", "customEmails": "Message", "addEmail": "adicionar email", "removeEmail": "Message", "emailPlaceholder": "Message", "emailTooltip": "Message", "emailDescription": "Message", "balanceThreshold": "Message", "balanceThresholdTooltip": "Message", "balanceThresholdDescription": "Description", "alertExplanationTitle": "explicação de alertas", "alertExplanation": "Message", "selectEvents": "Message", "eventsDescription": "Description", "selectMethods": "selecionar método de recebimento de notificações", "methodsDescription": "Description", "description": "Description", "recommended": "Message", "important": "Message", "testRecommendation": "Message", "testNotification": "notificação de teste", "testMessage": "Message", "testSuccess": "notificação de teste enviada com sucesso", "testFailed": "Message", "saveSuccess": "configurações de notificação salvas com sucesso", "saveFailed": "Message", "validation": {"invalidEmail": "Message", "emailRequired": "endereço de email não pode estar vazio", "invalidUrl": "Message", "qywxWebhookRequired": "Message", "wxpusherTokenRequired": "por favor, insira o WxPusher APP Token", "wxpusherUidRequired": "Message", "dingtalkWebhookRequired": "Message", "feishuWebhookRequired": "por favor, insira a URL do Webhook do bot Feishu", "webhookUrlRequired": "Message", "telegramTokenRequired": "por favor, insira o Token do Bot Telegram", "telegramChatIdRequired": "Message", "telegramBotTokenRequired": "Por favor, insira o Token do Bot do Telegram"}, "qywxbotConfig": "Configuração do bot WeChat Enterprise", "qywxbotGuide": "Message", "wxpusherConfig": "configuração WxPusher", "wxpusherGuide": "Message", "wxpusherUid": "UID do usuário", "dingtalkConfig": "Configuração do bot DingTalk", "dingtalkGuide": "Message", "feishuConfig": "Configuração do bot Feishu", "feishuGuide": "Message", "webhookConfig": "configuração Webhook", "webhookGuide": "Message", "webhookUrl": "endereço de chamada", "webhookToken": "Message", "webhookTokenTooltip": "Message", "webhookTokenPlaceholder": "<PERSON><PERSON> (opcional)", "telegramConfig": "Configuração do bot Telegram", "telegramGuide": "Message", "telegramChatIdPlaceholder": "********* ou @username", "events": {"account_balance_low": "Message", "account_quota_expiry": "cota prestes a expirar", "security_alert": "Message", "system_announcement": "anúncio do sistema", "promotional_activity": "Message", "model_pricing_update": "atualização de preços do modelo", "anti_loss_contact": "Message"}, "eventDescriptions": {"account_balance_low": "Description", "account_quota_expiry": "notificar com antecedência quando a cota da conta estiver prestes a expirar", "security_alert": "Description", "system_announcement": "Description", "promotional_activity": "Description", "model_pricing_update": "notificação de mudanças de preços do modelo e atualizações de regras de cobrança", "anti_loss_contact": "Description"}, "methodDescriptions": {"email": "receber mensagens de notificação por email", "telegram": "Description", "webhook": "Description", "wxpusher": "Description", "qywxbot": "receber notificações através do bot WeChat Enterprise", "dingtalk": "Description", "feishu": "receber notificações através do bot Feishu"}, "methods": {"email": "Message", "telegram": "Message", "webhook": "notificação Webhook", "wxpusher": "Message", "qywxbot": "bot WeChat Enterprise", "dingtalk": "Message", "feishu": "bot <PERSON><PERSON><PERSON>"}, "configurationSteps": "Message", "detailedDocumentation": "documentação detalhada:", "qywxbotConfigurationGuide": "Message", "qywxbotStep1": "Message", "qywxbotStep2": "Message", "qywxbotStep3": "Message", "qywxbotStep4": "Message", "qywxbotStep5": "Message", "qywxbotDocumentationLink": "instruções de configuração do bot de grupo WeChat Enterprise", "wxpusherConfiguration": "Message", "wxpusherConfigurationGuide": "Message", "wxpusherAppToken": "APP Token", "wxpusherUserUID": "Message", "wxpusherAppTokenPlaceholder": "AT_xxx", "wxpusherUserUIDPlaceholder": "UID_xxx", "wxpusherAppTokenRequired": "Message", "wxpusherUserUIDRequired": "Message", "wxpusherStep1": "Visite o site oficial do WxPusher para registrar uma conta", "wxpusherStep2": "Message", "wxpusherStep3": "Message", "wxpusherStep4": "Message", "wxpusherOfficialWebsite": "Message", "dingtalkConfigurationGuide": "Message", "dingtalkStep1": "Message", "dingtalkStep2": "Message", "dingtalkStep3": "Message", "dingtalkStep4": "Message", "dingtalkStep5": "Message", "dingtalkSecurityNote": "Message", "dingtalkPrivacyNote": "Message", "dingtalkDocumentationLink": "integração de bot personalizado <PERSON>alk", "feishuConfigurationGuide": "Message", "feishuStep1": "Message", "feishuStep2": "Message", "feishuStep3": "definir nome e descrição do bot", "feishuStep4": "Message", "feishuStep5": "copiar URL do Webhook gerada", "feishuStep6": "Message", "feishuStep7": "Message", "feishuSecurityNote": "Message", "feishuPrivacyNote": "Message", "feishuDocumentationLink": "Message", "telegramConfigurationGuide": "Message", "telegramStep1": "Message", "telegramStep2": "Message", "telegramStep3": "Message", "telegramStep4": "copiar o Token do Bot obtido", "telegramStep5": "Message", "telegramStep6": "acessar https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates", "telegramStep7": "Message", "telegramStep8": "Message", "telegramSecurityNote": "Message", "telegramDocumentationLink": "documentação oficial da API do Bot Telegram", "dingtalkStep3a": "Message", "dingtalkStep3b": "Message", "dingtalkStep6": "Message", "dingtalkNoticeTitle": "observações:", "dingtalkRateLimit": "Message", "dingtalkKeywordNote": "Message", "feishuStep3Detailed": "Message", "feishuStep4Detailed": "Message", "feishuSignatureVerification": "Message", "feishuKeywordVerification": "Message", "feishuStep5Detailed": "Message", "feishuStep6Detailed": "Message", "feishuStep7Detailed": "Message", "feishuMessageFormatsTitle": "suporte a formatos de mensagem:", "feishuTextMessage": "Message", "feishuRichTextMessage": "Message", "feishuCardMessage": "Message", "feishuImageMessage": "Message", "feishuNoticeTitle": "Title", "feishuRateLimit": "Message", "feishuKeywordNote": "Message", "feishuSecurityRecommendation": "• recomenda-se ativar a verificação de assinatura para melhorar a segurança", "telegramStep6Detailed": "Message", "telegramStep7Detailed": "Message", "telegramStep8Detailed": "Message", "telegramNoticeTitle": "observações:", "telegramRateLimit": "Message", "telegramMessageFormats": "Message", "telegramMarkdownSupport": "Message", "telegramInlineKeyboard": "Message", "telegramPrivacyNote": "Message", "telegramSecurityTip": "Message", "telegramStep4Detailed": "Message", "telegramStep5Detailed": "Message", "telegramPersonalChat": "Message", "telegramGroupChat": "Message", "telegramChannel": "Message", "telegramChatIdFormatsTitle": "explicação do formato do Chat ID:", "telegramPersonalChatId": "Message", "telegramGroupChatId": "Message", "telegramSuperGroupChatId": "Message", "telegramUsernameFormat": "Title", "telegramInteractionRequired": "Action", "telegramGroupMembership": "• no grupo, é necessário primeiro adicionar o bot como membro", "telegramChannelPermission": "Message", "webhookCallUrl": "Endereço de chamada", "webhookConfigurationGuide": "Guia de configuração do Webhook", "webhookDataFormatExample": "Exemplo de formato de dados：", "webhookConfigurationInstructions": "Instruções de configuração：", "webhookRequestMethod": "• Método de solicitação：POST", "webhookContentType": "• Content-Type: application/json", "webhookAuthMethod": "• Método de autenticação：<PERSON><PERSON>（opcional, após preenchimento adicionará Authorization: Bearer {token} no cabeçalho da solicitação）", "webhookTimeout": "• Tempo limite：30 segundos", "webhookRetryMechanism": "• Mecanismo de repetição：Tenta novamente 2 vezes após falha", "webhookTip": "💡 Dica：Certifique-se de que seu endpoint Webhook pode receber solicitações POST e retornar códigos de status 2xx", "telegramStep3Detailed": "Message", "telegramPersonalChatDetailed": "Message", "telegramGroupChatDetailed": "Message", "telegramChannelDetailed": "Message", "telegramQuickChatIdTitle": "Message", "telegramQuickStep1": "Message", "telegramQuickStep2": "Message", "telegramQuickStep3": "Message"}, "legal": {"privacyPolicy": {"title": "Title", "lastUpdated": "Time", "sections": {"informationCollection": {"title": "coleta de informações", "description": "Description", "items": {"accountInfo": "Text", "usageData": "Description", "technicalInfo": "Text"}}, "informationUsage": {"title": "Title", "description": "Description", "items": ["fornecer e manter nossos serviços", "Description", "melhorar a qualidade do serviço e experiência do usuário", "Description", "prevenir fraude e abuso"]}, "informationSharing": {"title": "Title", "description": "Description", "items": ["Description", "Description", "Description"]}, "dataSecurity": {"title": "segurança de dados", "description": "Description", "items": ["transmissão e armazenamento de dados criptografados", "Text", "auditorias de segurança regulares e atualizações", "Text"]}, "dataRetention": {"title": "retenção de dados", "description": "Description", "items": ["Text", "Text", "Text"]}, "userRights": {"title": "seus direitos", "description": "Description", "items": ["User", "excluir sua conta e dados relacionados", "User", "User"]}, "cookieUsage": {"title": "Title", "description": "Description", "items": ["manter sessões de usuário", "Text", "Text"]}, "thirdPartyServices": {"title": "serviços de terceiros", "description": "Description", "items": ["Text", "GitHub OAuth: para autenticação de usuário", "Text"]}, "childrenPrivacy": {"title": "privacidade infantil", "description": "Description"}, "policyUpdates": {"title": "Title", "description": "Description"}, "contactUs": {"title": "entre em contato conosco", "description": "Description", "email": "email", "address": "Text"}}}, "termsOfService": {"title": "Title", "lastUpdated": "Time", "importantNotice": "Message", "sections": {"serviceDescription": {"title": "descrição do serviço", "description": "Description", "items": ["gerenciamento de chaves API", "Description", "estatísticas de uso e monitoramento", "Description", "Description"]}, "userAccount": {"title": "conta de usuário", "description": "Description", "items": ["Text", "User", "User", "atualizar informações da conta em tempo hábil", "User"]}, "usageRules": {"title": "regras de uso", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "prohibitedBehavior": {"title": "comportamento proibido", "description": "Description", "items": ["Text", "Text", "Text", "Text", "Text"]}, "serviceAvailability": {"title": "disponibilidade do serviço", "description": "Description", "items": ["Text", "Text", "Text", "manutenção programada será notificada com antecedência"]}, "feesAndPayment": {"title": "Title", "description": "Description", "items": ["Text", "recursos avançados podem exigir pagamento", "Text", "Text"]}, "intellectualProperty": {"title": "propriedade intelectual", "description": "Description", "items": ["Text", "você obtém uma licença de uso limitada", "Text", "Text"]}, "privacyProtection": {"title": "proteção de privacidade", "description": "Description", "items": ["Text", "tomar medidas razoáveis para proteger a segurança dos dados", "Text"]}, "disclaimer": {"title": "isenção de responsabilidade", "description": "Description", "items": ["Text", "Text", "não é responsável por perdas indiretas", "Text"]}, "serviceTermination": {"title": "término do serviço", "description": "Description", "items": ["você viola estes termos", "Text", "Text", "requisitos legais"]}, "termsModification": {"title": "Title", "description": "Description", "items": ["mudanças importantes serão notificadas com antecedência", "Text", "Text"]}, "disputeResolution": {"title": "Title", "description": "Description", "items": ["Text", "Text", "Text"]}, "contactUs": {"title": "entre em contato conosco", "description": "Description", "email": "email", "address": "Text", "serviceHours": "Text"}}}, "common": {"copyright": "Text", "contactEmail": "<EMAIL>", "supportEmail": "<EMAIL>", "companyAddress": "Text"}}}