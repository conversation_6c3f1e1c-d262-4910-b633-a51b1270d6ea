import {useContext, useState, useMemo} from 'react';
import ReactDOM from 'react-dom/client';
import {BrowserRouter} from 'react-router-dom';
import {UserProvider} from './context/User';
import {StatusContext, StatusProvider} from './context/Status';
import {GroupProvider} from './context/Group';
import {AdminStatusProvider} from './context/AdminStatus';
import Header from './pages/Header/Header';
import './i18n';
import zhCN from "antd/locale/zh_CN";
import enGB from "antd/locale/en_GB";
import jaJP from "antd/locale/ja_JP";
import koKR from "antd/locale/ko_KR";
import viVN from "antd/locale/vi_VN";
import {safeParseJSON} from "./helpers";
import {App, ConfigProvider} from "antd";
import i18n from "i18next";
import {ProConfigProvider} from "@ant-design/pro-components";
import './index.css';

const Index = () => {
    const [statusState, statusDispatch] = useContext(StatusContext);
    const [darkMode, setDarkMode] = useState(statusState.customConfig.darkMode);

    const getLocale = () => {
        switch (i18n.language) {
            case 'zh-CN':
                return zhCN;
            case 'ja':
                return jaJP;
            case 'ko':
                return koKR;
            case 'vi':
                return viVN;
            default:
                return enGB;
        }
    };

    // 根据当前模式动态选择主题配置
    const themeConfig = useMemo(() => {
        // 如果是暗黑模式且有对应的暗黑主题配置，则使用暗黑主题
        if (darkMode && statusState.status.CustomDarkThemeConfig) {
            return safeParseJSON(statusState.status.CustomDarkThemeConfig, undefined);
        }
        // 否则使用普通主题配置
        return safeParseJSON(statusState.status.CustomThemeConfig, undefined);
    }, [statusState.status.CustomThemeConfig, statusState.status.CustomDarkThemeConfig, darkMode]);

    return (
        // <StrictMode>
        <StatusProvider>
            <UserProvider>
                <GroupProvider>
                    <AdminStatusProvider>
                        <ConfigProvider
                            locale={getLocale()}
                            theme={themeConfig}
                        >
                            <ProConfigProvider dark={darkMode}>
                                <App>
                                    <BrowserRouter>
                                        <Header darkMode={darkMode} setDarkMode={setDarkMode}/>
                                    </BrowserRouter>
                                </App>
                            </ProConfigProvider>
                        </ConfigProvider>
                    </AdminStatusProvider>
                </GroupProvider>
            </UserProvider>
        </StatusProvider>
        // </StrictMode>
    );
}

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);

root.render(<Index/>);
