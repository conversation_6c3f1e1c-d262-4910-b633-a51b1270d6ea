import React, { useState, useEffect } from 'react';
import {
    Card,
    Row,
    Col,
    Switch,
    Input,
    Button,
    Form,
    message,
    Space,
    Typography,
    Divider,
    Tooltip,
    Alert,
    Tag,
    InputNumber,
    Spin
} from 'antd';
import {
    BellOutlined,
    MailOutlined,
    MessageOutlined,
    ApiOutlined,
    ExperimentOutlined,
    InfoCircleOutlined,
    SendOutlined,
    SettingOutlined,
    DollarOutlined,
    CheckCircleOutlined,
    SaveOutlined,
    WechatOutlined,
    RobotOutlined,
    PhoneOutlined,
    PlusOutlined,
    MinusCircleOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import { API } from '../../../helpers';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface NotificationEvent {
    key: string;
    name: string;
    description: string;
    enabled: boolean;
}

interface NotificationMethod {
    type: string;
    enabled: boolean;
    config?: string;
}

interface NotificationSetting {
    id: number;
    user_id: number;
    subscription_events: NotificationEvent[];
    notification_methods: NotificationMethod[];
    webhook_url: string;
    webhook_token: string;
    custom_email: string;        // 兼容旧版本
    custom_emails: string[];     // 新的多邮箱字段
    email_enabled: boolean;
    telegram_enabled: boolean;
    webhook_enabled: boolean;
    wxpusher_enabled: boolean;
    qywxbot_enabled: boolean;
    dingtalk_enabled: boolean;
    feishu_enabled: boolean;
    balance_threshold: number;
    qywx_webhook_url: string;
    dingtalk_webhook_url: string;
    feishu_webhook_url: string;
    wxpusher_app_token: string;
    wxpusher_uid: string;
    telegram_bot_token: string;
    telegram_chat_id: string;
}

const NotificationSettingsPage: React.FC = () => {
    const { t } = useTranslation();
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [testLoading, setTestLoading] = useState<string>('');
    const [settings, setSettings] = useState<NotificationSetting | null>(null);

    // 获取事件的本地化名称和描述
    const getEventInfo = (key: string) => {
        const eventNames: Record<string, string> = {
            'account_balance_low': t('notification.events.account_balance_low', '余额不足预警'),
            'account_quota_expiry': t('notification.events.account_quota_expiry', '额度即将过期'),
            'security_alert': t('notification.events.security_alert', '安全警报'),
            'system_announcement': t('notification.events.system_announcement', '系统公告'),
            'promotional_activity': t('notification.events.promotional_activity', '促销活动通知'),
            'model_pricing_update': t('notification.events.model_pricing_update', '模型价格更新'),
            'anti_loss_contact': t('notification.events.anti_loss_contact', '防失联-定期通知')
        };

        const eventDescriptions: Record<string, string> = {
            'account_balance_low': t('notification.eventDescriptions.account_balance_low', '当账户余额低于设定阈值时通知您及时充值'),
            'account_quota_expiry': t('notification.eventDescriptions.account_quota_expiry', '当账户额度即将过期时提前通知您'),
            'security_alert': t('notification.eventDescriptions.security_alert', '账户异常登录、密码修改等安全相关提醒'),
            'system_announcement': t('notification.eventDescriptions.system_announcement', '重要的系统更新、维护通知和功能发布'),
            'promotional_activity': t('notification.eventDescriptions.promotional_activity', '新的优惠活动、折扣信息和特殊促销'),
            'model_pricing_update': t('notification.eventDescriptions.model_pricing_update', '模型价格变动和计费规则更新通知'),
            'anti_loss_contact': t('notification.eventDescriptions.anti_loss_contact', '定期发送通知确保联系方式有效')
        };

        return {
            name: eventNames[key] || key,
            description: eventDescriptions[key] || ''
        };
    };

    // 默认订阅事件 - 添加防失联通知
    const defaultEvents = [
        {
            key: 'account_balance_low',
            ...getEventInfo('account_balance_low'),
            enabled: true,
            important: true,
            recommended: true
        },
        {
            key: 'account_quota_expiry',
            ...getEventInfo('account_quota_expiry'),
            enabled: true,
            important: true,
            recommended: false
        },
        {
            key: 'security_alert',
            ...getEventInfo('security_alert'),
            enabled: true,
            important: true,
            recommended: true
        },
        {
            key: 'system_announcement',
            ...getEventInfo('system_announcement'),
            enabled: false,
            important: false,
            recommended: false
        },
        {
            key: 'promotional_activity',
            ...getEventInfo('promotional_activity'),
            enabled: false,
            important: false,
            recommended: false
        },
        {
            key: 'model_pricing_update',
            ...getEventInfo('model_pricing_update'),
            enabled: false,
            important: false,
            recommended: false
        },
        {
            key: 'anti_loss_contact',
            ...getEventInfo('anti_loss_contact'),
            enabled: false,
            important: false,
            recommended: false
        }
    ];

    // 通知方式配置 - 添加新的通知方式
    const notificationMethods = [
        {
            type: 'email',
            name: t('notification.methods.email'),
            description: t('notification.methodDescriptions.email', '通过邮件接收通知消息'),
            icon: <MailOutlined style={{ color: '#1890ff' }} />,
            color: '#1890ff'
        },
        {
            type: 'telegram',
            name: t('notification.methods.telegram'),
            description: t('notification.methodDescriptions.telegram', '通过Telegram机器人接收通知'),
            icon: <MessageOutlined style={{ color: '#0088cc' }} />,
            color: '#0088cc'
        },
        {
            type: 'webhook',
            name: t('notification.methods.webhook'),
            description: t('notification.methodDescriptions.webhook', '通过HTTP请求发送通知到您的服务'),
            icon: <ApiOutlined style={{ color: '#52c41a' }} />,
            color: '#52c41a'
        },
        {
            type: 'qywxbot',
            name: t('notification.methods.qywxbot'),
            description: t('notification.methodDescriptions.qywxbot', '通过企业微信机器人发送通知'),
            icon: <WechatOutlined style={{ color: '#07c160' }} />,
            color: '#07c160'
        },
        {
            type: 'wxpusher',
            name: t('notification.methods.wxpusher'),
            description: t('notification.methodDescriptions.wxpusher', '通过WxPusher微信推送服务'),
            icon: <MessageOutlined style={{ color: '#1DA57A' }} />,
            color: '#1DA57A'
        },
        {
            type: 'dingtalk',
            name: t('notification.methods.dingtalk'),
            description: t('notification.methodDescriptions.dingtalk', '通过钉钉机器人发送通知'),
            icon: <PhoneOutlined style={{ color: '#1890ff' }} />,
            color: '#1890ff'
        },
        {
            type: 'feishu',
            name: t('notification.methods.feishu'),
            description: t('notification.methodDescriptions.feishu', '通过飞书机器人发送通知'),
            icon: <RobotOutlined style={{ color: '#00D4AA' }} />,
            color: '#00D4AA'
        }
    ];

    // 获取通知设置
    const fetchNotificationSettings = async () => {
        try {
            setLoading(true);
            const response = await API.get('/api/user/notification_setting');
            const data = response.data;
            if (data.success) {
                setSettings(data.data);
                // 设置表单值
                const formValues: any = {
                    webhook_url: data.data.webhook_url,
                    webhook_token: data.data.webhook_token,
                    custom_emails: data.data.custom_emails || (data.data.custom_email ? [data.data.custom_email] : []),
                    email_enabled: data.data.email_enabled,
                    telegram_enabled: data.data.telegram_enabled,
                    telegram_bot_token: data.data.telegram_bot_token || '',
                    telegram_chat_id: data.data.telegram_chat_id || '',
                    webhook_enabled: data.data.webhook_enabled,
                    wxpusher_enabled: data.data.wxpusher_enabled,
                    qywxbot_enabled: data.data.qywxbot_enabled,
                    dingtalk_enabled: data.data.dingtalk_enabled || false,
                    feishu_enabled: data.data.feishu_enabled || false,
                    balance_threshold: data.data.balance_threshold || 30,
                    qywx_webhook_url: data.data.qywx_webhook_url || '',
                    dingtalk_webhook_url: data.data.dingtalk_webhook_url || '',
                    feishu_webhook_url: data.data.feishu_webhook_url || '',
                    wxpusher_app_token: data.data.wxpusher_app_token || '',
                    wxpusher_uid: data.data.wxpusher_uid || '',
                };

                // 设置事件订阅状态
                if (data.data.subscription_events && data.data.subscription_events.length > 0) {
                    data.data.subscription_events.forEach((event: NotificationEvent) => {
                        formValues[`event_${event.key}`] = event.enabled;
                    });
                } else {
                    // 使用默认值
                    defaultEvents.forEach(event => {
                        formValues[`event_${event.key}`] = event.enabled;
                    });
                }

                form.setFieldsValue(formValues);
            } else {
                message.error(data.message || t('notification.saveFailed'));
            }
        } catch (error) {
            message.error(t('notification.saveFailed'));
            console.error('获取通知设置失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 保存设置
    const handleSave = async () => {
        try {
            const values = await form.validateFields();
            setSaving(true);

            // 构建订阅事件数据
            const subscriptionEvents = defaultEvents.map(event => ({
                ...event,
                enabled: values[`event_${event.key}`] !== undefined ? values[`event_${event.key}`] : event.enabled
            }));

            // 构建通知方式数据
            const notificationMethodsData = [
                { type: 'email', enabled: values.email_enabled || false },
                { type: 'telegram', enabled: values.telegram_enabled || false },
                { type: 'webhook', enabled: values.webhook_enabled || false },
                { type: 'wxpusher', enabled: values.wxpusher_enabled || false },
                { type: 'qywxbot', enabled: values.qywxbot_enabled || false },
                { type: 'dingtalk', enabled: values.dingtalk_enabled || false },
                { type: 'feishu', enabled: values.feishu_enabled || false },
            ];

            const requestData = {
                subscription_events: subscriptionEvents,
                notification_methods: notificationMethodsData,
                webhook_url: values.webhook_url || '',
                webhook_token: values.webhook_token || '',
                custom_emails: values.custom_emails || [],
                email_enabled: values.email_enabled || false,
                telegram_enabled: values.telegram_enabled || false,
                telegram_bot_token: values.telegram_bot_token || '',
                telegram_chat_id: values.telegram_chat_id || '',
                webhook_enabled: values.webhook_enabled || false,
                wxpusher_enabled: values.wxpusher_enabled || false,
                qywxbot_enabled: values.qywxbot_enabled || false,
                dingtalk_enabled: values.dingtalk_enabled || false,
                feishu_enabled: values.feishu_enabled || false,
                balance_threshold: values.balance_threshold || 30,
                qywx_webhook_url: values.qywx_webhook_url || '',
                dingtalk_webhook_url: values.dingtalk_webhook_url || '',
                feishu_webhook_url: values.feishu_webhook_url || '',
                wxpusher_app_token: values.wxpusher_app_token || '',
                wxpusher_uid: values.wxpusher_uid || '',
            };

            const response = await API.put('/api/user/notification_setting', requestData);
            const data = response.data;
            if (data.success) {
                message.success(t('notification.saveSuccess'));
                await fetchNotificationSettings(); // 重新获取设置
            } else {
                message.error(data.message || t('notification.saveFailed'));
            }
        } catch (error) {
            message.error(t('notification.saveFailed'));
            console.error('保存设置失败:', error);
        } finally {
            setSaving(false);
        }
    };

    // 测试通知
    const handleTestNotification = async (type: string) => {
        try {
            setTestLoading(type);
            const response = await API.post('/api/user/notification_test', {
                type: type,
                message: t('notification.testMessage')
            });

            const data = response.data;
            if (data.success) {
                message.success(t('notification.testSuccess'));
            } else {
                message.error(data.message || t('notification.testFailed'));
            }
        } catch (error) {
            message.error(t('notification.testFailed'));
            console.error('测试通知失败:', error);
        } finally {
            setTestLoading('');
        }
    };

    // 获取通知方式名称
    const getNotificationName = (type: string) => {
        const method = notificationMethods.find(m => m.type === type);
        return method ? method.name : type;
    };

    useEffect(() => {
        fetchNotificationSettings();
    }, []);

    if (loading) {
        return (
            <div style={{ textAlign: 'center', padding: '50px' }}>
                <Spin size="large" />
            </div>
        );
    }

    return (
        <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
            {/* 页面标题 */}
            <div style={{ marginBottom: '24px' }}>
                <Title level={2}>
                    <Space>
                        <BellOutlined />
                        {t('notification.title')}
                    </Space>
                </Title>
                <Text type="secondary">
                    {t('notification.description', '管理您的通知偏好，选择接收哪些类型的通知以及通过何种方式接收')}
                </Text>
            </div>

            <Form form={form} layout="vertical">
                <Row gutter={[24, 24]}>
                    {/* 订阅事件配置 */}
                    <Col span={24}>
                        <Card 
                            title={
                                <Space>
                                    <SettingOutlined />
                                    <span>{t('notification.subscriptionEvents')}</span>
                                </Space>
                            }
                        >
                            <Alert
                                message={t('notification.selectEvents', '选择您感兴趣的事件类型')}
                                description={t('notification.eventsDescription', '当这些事件发生时，系统将通过您选择的方式向您发送通知')}
                                type="info"
                                showIcon
                                style={{ marginBottom: 16 }}
                            />
                            
                            <Row gutter={[16, 16]}>
                                {defaultEvents.map((event) => (
                                    <Col span={24} key={event.key}>
                                        <div style={{ 
                                            padding: '16px', 
                                            border: '1px solid #f0f0f0', 
                                            borderRadius: '8px',
                                            backgroundColor: event.important ? '#fff7e6' : '#fafafa'
                                        }}>
                                            <Row justify="space-between" align="middle">
                                                <Col span={20}>
                                                    <div>
                                                        <Space>
                                                            <Text strong style={{ fontSize: '14px' }}>
                                                                {event.name}
                                                            </Text>
                                                            {event.recommended && (
                                                                <Tag color="orange">{t('notification.recommended', '推荐开启')}</Tag>
                                                            )}
                                                            {event.important && (
                                                                <Tag color="red">{t('notification.important', '重要')}</Tag>
                                                            )}
                                                        </Space>
                                                    </div>
                                                    <div style={{ marginTop: 4 }}>
                                                        <Text type="secondary" style={{ fontSize: '13px' }}>
                                                            {event.description}
                                                        </Text>
                                                    </div>
                                                </Col>
                                                <Col span={4} style={{ textAlign: 'right' }}>
                                                    <Form.Item 
                                                        name={`event_${event.key}`} 
                                                        valuePropName="checked"
                                                        style={{ margin: 0 }}
                                                    >
                                                        <Switch />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                        </div>
                                    </Col>
                                ))}
                            </Row>
                        </Card>
                    </Col>

                    {/* 通知方式配置 */}
                    <Col span={24}>
                        <Card 
                            title={
                                <Space>
                                    <SendOutlined />
                                    <span>{t('notification.notificationMethods')}</span>
                                </Space>
                            }
                        >
                            <Alert
                                message={t('notification.selectMethods', '选择接收通知的方式')}
                                description={t('notification.methodsDescription', '您可以同时启用多种通知方式，系统将通过所有启用的方式发送通知')}
                                type="info"
                                showIcon
                                style={{ marginBottom: 16 }}
                            />

                            <Row gutter={[16, 16]}>
                                {notificationMethods.map((method) => (
                                    <Col span={12} key={method.type}>
                                        <div style={{ 
                                            padding: '16px', 
                                            border: '1px solid #f0f0f0', 
                                            borderRadius: '8px',
                                            height: '100%'
                                        }}>
                                            <Row justify="space-between" align="middle" style={{ marginBottom: 8 }}>
                                                <Col>
                                                    <Space>
                                                        {method.icon}
                                                        <Text strong>{method.name}</Text>
                                                    </Space>
                                                </Col>
                                                <Col>
                                                    <Form.Item name={`${method.type}_enabled`} valuePropName="checked" style={{ margin: 0 }}>
                                                        <Switch />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                            <Text type="secondary" style={{ fontSize: '12px' }}>
                                                {method.description}
                                            </Text>
                                            <div style={{ marginTop: 8 }}>
                                                <Form.Item 
                                                    noStyle 
                                                    shouldUpdate={(prevValues, currentValues) => 
                                                        prevValues[`${method.type}_enabled`] !== currentValues[`${method.type}_enabled`]
                                                    }
                                                >
                                                    {({ getFieldValue }) =>
                                                        getFieldValue(`${method.type}_enabled`) ? (
                                                            <Button 
                                                                size="small" 
                                                                type="link" 
                                                                icon={<ExperimentOutlined />}
                                                                loading={testLoading === method.type}
                                                                onClick={() => handleTestNotification(method.type)}
                                                                style={{ padding: 0, height: 'auto' }}
                                                            >
                                                                {t('notification.testNotification')}
                                                            </Button>
                                                        ) : null
                                                    }
                                                </Form.Item>
                                            </div>
                                        </div>
                                    </Col>
                                ))}
                            </Row>

                            {/* 邮件配置 */}
                            <Form.Item
                                noStyle
                                shouldUpdate={(prevValues, currentValues) =>
                                    prevValues.email_enabled !== currentValues.email_enabled
                                }
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('email_enabled') ? (
                                        <Card
                                            size="small"
                                            title={t('notification.emailConfig')}
                                            style={{ marginTop: 16 }}
                                            type="inner"
                                        >
                                            <Form.List name="custom_emails">
                                                {(fields, { add, remove }) => (
                                                    <>
                                                        {fields.map(({ key, name, ...restField }) => (
                                                            <Space key={key} style={{ display: 'flex', marginBottom: 8, width: '100%' }} align="baseline">
                                                                <Form.Item
                                                                    {...restField}
                                                                    name={[name]}
                                                                    label={fields.length === 1 && name === 0 ? (
                                                                        <Space>
                                                                            {t('notification.customEmails')}
                                                                            <Tooltip title={t('notification.emailTooltip')}>
                                                                                <InfoCircleOutlined />
                                                                            </Tooltip>
                                                                        </Space>
                                                                    ) : undefined}
                                                                    rules={[
                                                                        { type: 'email', message: t('notification.validation.invalidEmail') }
                                                                    ]}
                                                                    style={{ flex: 1, marginBottom: 0 }}
                                                                >
                                                                    <Input
                                                                        placeholder={t('notification.emailPlaceholder')}
                                                                        prefix={<MailOutlined />}
                                                                    />
                                                                </Form.Item>
                                                                {fields.length > 1 && (
                                                                    <Button
                                                                        type="link"
                                                                        danger
                                                                        icon={<MinusCircleOutlined />}
                                                                        onClick={() => remove(name)}
                                                                    >
                                                                        {t('notification.removeEmail')}
                                                                    </Button>
                                                                )}
                                                            </Space>
                                                        ))}
                                                        <Form.Item>
                                                            <Button
                                                                type="dashed"
                                                                onClick={() => add()}
                                                                block
                                                                icon={<PlusOutlined />}
                                                            >
                                                                {t('notification.addEmail')}
                                                            </Button>
                                                        </Form.Item>
                                                    </>
                                                )}
                                            </Form.List>
                                            <Alert
                                                message={t('notification.emailConfig')}
                                                description={t('notification.emailDescription')}
                                                type="info"
                                                showIcon
                                                style={{ marginTop: 8 }}
                                            />
                                        </Card>
                                    ) : null
                                }
                            </Form.Item>

                            {/* 企业微信机器人配置 */}
                            <Form.Item 
                                noStyle 
                                shouldUpdate={(prevValues, currentValues) => 
                                    prevValues.qywxbot_enabled !== currentValues.qywxbot_enabled
                                }
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('qywxbot_enabled') ? (
                                        <Card
                                            size="small"
                                            title={t('notification.qywxbotConfig')}
                                            style={{ marginTop: 16 }}
                                            type="inner"
                                        >
                                            <Row gutter={16}>
                                                <Col span={24}>
                                                    <Form.Item
                                                        name="qywx_webhook_url"
                                                        label="Webhook URL"
                                                        rules={[
                                                            { required: true, message: t('notification.validation.qywxWebhookRequired', '请输入企业微信机器人Webhook URL') },
                                                            { type: 'url', message: t('notification.validation.invalidUrl', '请输入有效的URL') }
                                                        ]}
                                                    >
                                                        <Input placeholder="https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=..." />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                            <Alert
                                                message={t('notification.qywxbotConfigurationGuide')}
                                                description={
                                                    <div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.configurationSteps')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            1. {t('notification.qywxbotStep1')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            2. {t('notification.qywxbotStep2')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            3. {t('notification.qywxbotStep3')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            4. {t('notification.qywxbotStep4')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            5. {t('notification.qywxbotStep5')}
                                                        </div>
                                                        <div>
                                                            📖 {t('notification.detailedDocumentation')}<a href="https://developer.work.weixin.qq.com/document/path/91770" target="_blank" rel="noopener noreferrer">{t('notification.qywxbotDocumentationLink')}</a>
                                                        </div>
                                                    </div>
                                                }
                                                type="info"
                                                showIcon
                                            />
                                        </Card>
                                    ) : null
                                }
                            </Form.Item>

                            {/* WxPusher配置 */}
                            <Form.Item 
                                noStyle 
                                shouldUpdate={(prevValues, currentValues) => 
                                    prevValues.wxpusher_enabled !== currentValues.wxpusher_enabled
                                }
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('wxpusher_enabled') ? (
                                        <Card
                                            size="small"
                                            title={t('notification.wxpusherConfiguration')}
                                            style={{ marginTop: 16 }}
                                            type="inner"
                                        >
                                            <Row gutter={16}>
                                                <Col span={12}>
                                                    <Form.Item
                                                        name="wxpusher_app_token"
                                                        label={t('notification.wxpusherAppToken')}
                                                        rules={[
                                                            { required: true, message: t('notification.wxpusherAppTokenRequired') }
                                                        ]}
                                                    >
                                                        <Input placeholder={t('notification.wxpusherAppTokenPlaceholder')} />
                                                    </Form.Item>
                                                </Col>
                                                <Col span={12}>
                                                    <Form.Item
                                                        name="wxpusher_uid"
                                                        label={t('notification.wxpusherUserUID')}
                                                        rules={[
                                                            { required: true, message: t('notification.wxpusherUserUIDRequired') }
                                                        ]}
                                                    >
                                                        <Input placeholder={t('notification.wxpusherUserUIDPlaceholder')} />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                            <Alert
                                                message={t('notification.wxpusherConfigurationGuide')}
                                                description={
                                                    <div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.configurationSteps')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            1. {t('notification.wxpusherStep1')} - <a href="https://wxpusher.zjiecode.com" target="_blank" rel="noopener noreferrer">{t('notification.wxpusherOfficialWebsite')}</a>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            2. {t('notification.wxpusherStep2')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            3. {t('notification.wxpusherStep3')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            4. {t('notification.wxpusherStep4')}
                                                        </div>
                                                        <div>
                                                            📖 {t('notification.detailedDocumentation')}<a href="https://wxpusher.zjiecode.com/docs/#/" target="_blank" rel="noopener noreferrer">https://wxpusher.zjiecode.com/docs/#/</a>
                                                        </div>
                                                    </div>
                                                }
                                                type="info"
                                                showIcon
                                            />
                                        </Card>
                                    ) : null
                                }
                            </Form.Item>

                            {/* 钉钉机器人配置 */}
                            <Form.Item 
                                noStyle 
                                shouldUpdate={(prevValues, currentValues) => 
                                    prevValues.dingtalk_enabled !== currentValues.dingtalk_enabled
                                }
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('dingtalk_enabled') ? (
                                        <Card
                                            size="small"
                                            title={t('notification.dingtalkConfig')}
                                            style={{ marginTop: 16 }}
                                            type="inner"
                                        >
                                            <Row gutter={16}>
                                                <Col span={24}>
                                                    <Form.Item
                                                        name="dingtalk_webhook_url"
                                                        label="Webhook URL"
                                                        rules={[
                                                            { required: true, message: t('notification.validation.dingtalkWebhookRequired') },
                                                            { type: 'url', message: t('notification.validation.invalidUrl') }
                                                        ]}
                                                    >
                                                        <Input placeholder="https://oapi.dingtalk.com/robot/send?access_token=xxx" />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                            <Alert
                                                message={t('notification.dingtalkConfigurationGuide')}
                                                description={
                                                    <div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.configurationSteps')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            1. {t('notification.dingtalkStep1')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            2. {t('notification.dingtalkStep2')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            3. {t('notification.dingtalkStep3')}
                                                        </div>
                                                        <div style={{ marginLeft: 16, marginBottom: 4 }}>
                                                            {t('notification.dingtalkStep3a')}
                                                        </div>
                                                        <div style={{ marginLeft: 16, marginBottom: 4 }}>
                                                            {t('notification.dingtalkStep3b')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            4. {t('notification.dingtalkStep4')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            5. {t('notification.dingtalkStep5')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            6. {t('notification.dingtalkStep6')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.dingtalkNoticeTitle')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.dingtalkRateLimit')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.dingtalkKeywordNote')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            {t('notification.dingtalkPrivacyNote')}
                                                        </div>
                                                        <div>
                                                            📖 {t('notification.detailedDocumentation')}<a href="https://open.dingtalk.com/document/orgapp/assign-a-webhook-url-to-an-internal-chatbot" target="_blank" rel="noopener noreferrer">{t('notification.dingtalkDocumentationLink')}</a>
                                                        </div>
                                                    </div>
                                                }
                                                type="info"
                                                showIcon
                                            />
                                        </Card>
                                    ) : null
                                }
                            </Form.Item>

                            {/* 飞书机器人配置 */}
                            <Form.Item 
                                noStyle 
                                shouldUpdate={(prevValues, currentValues) => 
                                    prevValues.feishu_enabled !== currentValues.feishu_enabled
                                }
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('feishu_enabled') ? (
                                        <Card
                                            size="small"
                                            title={t('notification.feishuConfig')}
                                            style={{ marginTop: 16 }}
                                            type="inner"
                                        >
                                            <Row gutter={16}>
                                                <Col span={24}>
                                                    <Form.Item
                                                        name="feishu_webhook_url"
                                                        label="Webhook URL"
                                                        rules={[
                                                            { required: true, message: t('notification.validation.feishuWebhookRequired') },
                                                            { type: 'url', message: t('notification.validation.invalidUrl') }
                                                        ]}
                                                    >
                                                        <Input placeholder="https://open.feishu.cn/open-apis/bot/v2/hook/..." />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                            <Alert
                                                message={t('notification.feishuConfigurationGuide')}
                                                description={
                                                    <div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.configurationSteps')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            1. {t('notification.feishuStep1')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            2. {t('notification.feishuStep2')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            3. {t('notification.feishuStep3Detailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            4. {t('notification.feishuStep4Detailed')}
                                                        </div>
                                                        <div style={{ marginLeft: 16, marginBottom: 4 }}>
                                                            {t('notification.feishuSignatureVerification')}
                                                        </div>
                                                        <div style={{ marginLeft: 16, marginBottom: 4 }}>
                                                            {t('notification.feishuKeywordVerification')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            5. {t('notification.feishuStep5Detailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            6. {t('notification.feishuStep6Detailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            7. {t('notification.feishuStep7Detailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.feishuMessageFormatsTitle')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.feishuTextMessage')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.feishuRichTextMessage')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.feishuCardMessage')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            {t('notification.feishuImageMessage')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.feishuNoticeTitle')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.feishuRateLimit')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.feishuKeywordNote')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.feishuSecurityRecommendation')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            {t('notification.feishuPrivacyNote')}
                                                        </div>
                                                        <div>
                                                            📖 {t('notification.detailedDocumentation')}<a href="https://open.feishu.cn/document/client-docs/bot-v3/add-custom-bot" target="_blank" rel="noopener noreferrer">{t('notification.feishuDocumentationLink')}</a>
                                                        </div>
                                                    </div>
                                                }
                                                type="info"
                                                showIcon
                                            />
                                        </Card>
                                    ) : null
                                }
                            </Form.Item>

                            {/* Webhook配置 */}
                            <Form.Item 
                                noStyle 
                                shouldUpdate={(prevValues, currentValues) => 
                                    prevValues.webhook_enabled !== currentValues.webhook_enabled
                                }
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('webhook_enabled') ? (
                                        <Card
                                            size="small"
                                            title={t('notification.webhookConfig')}
                                            style={{ marginTop: 16 }}
                                            type="inner"
                                        >
                                            <Row gutter={16}>
                                                <Col span={12}>
                                                    <Form.Item
                                                        name="webhook_url"
                                                        label={t('notification.webhookCallUrl')}
                                                        rules={[
                                                            { required: true, message: t('notification.validation.webhookUrlRequired') },
                                                            { type: 'url', message: t('notification.validation.invalidUrl') }
                                                        ]}
                                                    >
                                                        <Input placeholder={t('notification.webhookUrlPlaceholder')} />
                                                    </Form.Item>
                                                </Col>
                                                <Col span={12}>
                                                    <Form.Item
                                                        name="webhook_token"
                                                        label={
                                                            <Space>
                                                                {t('notification.webhookToken')}
                                                                <Tooltip title={t('notification.webhookTokenTooltip')}>
                                                                    <InfoCircleOutlined />
                                                                </Tooltip>
                                                            </Space>
                                                        }
                                                    >
                                                        <Input.Password placeholder={t('notification.webhookTokenPlaceholder')} />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                            <div style={{ marginBottom: 16 }}>
                                                <Text type="secondary" style={{ fontSize: '14px' }}>
                                                    {t('notification.webhookRequestMethod')}
                                                </Text>
                                            </div>
                                            <div style={{ marginBottom: 16 }}>
                                                <Text type="secondary" style={{ fontSize: '14px' }}>
                                                    {t('notification.webhookContentType')}
                                                </Text>
                                            </div>

                                            <div style={{ marginBottom: 16 }}>
                                                <div style={{ marginBottom: 8 }}>
                                                    <Text strong>{t('notification.webhookDataFormatExample')}</Text>
                                                </div>
                                                <div style={{
                                                    background: '#f6f8fa',
                                                    padding: '12px',
                                                    borderRadius: '6px',
                                                    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
                                                    fontSize: '13px',
                                                    border: '1px solid #e1e4e8',
                                                    color: '#24292e'
                                                }}>
                                                    {`{
  "type": "quota_exceed",                    // 通知类型
  "title": "额度预警通知",                    // 通知标题
  "content": "通知内容，支持 {{value}} 变量占位符",  // 通知内容，支持变量替换content中的 {{value}} 占位符
  "values": ["$0.99"],                      // 按顺序替换content中的 {{value}} 占位符
  "timestamp": 1739950503                   // 时间戳
}`}
                                                </div>
                                            </div>

                                            <div style={{ marginBottom: 16 }}>
                                                <div style={{ marginBottom: 8 }}>
                                                    <Text strong>{t('notification.webhookExampleTitle')}</Text>
                                                </div>
                                                <div style={{
                                                    background: '#f6f8fa',
                                                    padding: '12px',
                                                    borderRadius: '6px',
                                                    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
                                                    fontSize: '13px',
                                                    border: '1px solid #e1e4e8',
                                                    color: '#24292e'
                                                }}>
                                                    {`{
  "type": "quota_exceed",
  "title": "额度预警通知",
  "content": "您的额度即将用尽，当前剩余额度 {{value}}",
  "values": ["$0.99"],
  "timestamp": 1739950503
}`}
                                                </div>
                                            </div>

                                            <div style={{
                                                background: '#fff7e6',
                                                border: '1px solid #ffd591',
                                                borderRadius: '6px',
                                                padding: '12px',
                                                marginBottom: 16
                                            }}>
                                                <Text style={{ fontSize: '14px', color: '#d46b08' }}>
                                                    <strong>额度预警阈值 等价金额：$0.20</strong>
                                                </Text>
                                                <div style={{ marginTop: 8, fontSize: '13px', color: '#8c8c8c' }}>
                                                    <div>100000</div>
                                                    <div style={{ marginTop: 4 }}>
                                                        {t('notification.webhookTip')}
                                                    </div>
                                                </div>
                                            </div>
                                        </Card>
                                    ) : null
                                }
                            </Form.Item>

                            {/* Telegram配置 */}
                            <Form.Item 
                                noStyle 
                                shouldUpdate={(prevValues, currentValues) => 
                                    prevValues.telegram_enabled !== currentValues.telegram_enabled
                                }
                            >
                                {({ getFieldValue }) =>
                                    getFieldValue('telegram_enabled') ? (
                                        <Card
                                            size="small"
                                            title={t('notification.telegramConfig')}
                                            style={{ marginTop: 16 }}
                                            type="inner"
                                        >
                                            <Row gutter={16}>
                                                <Col span={12}>
                                                    <Form.Item
                                                        name="telegram_bot_token"
                                                        label="Bot Token"
                                                        rules={[
                                                            { required: true, message: t('notification.validation.telegramBotTokenRequired') }
                                                        ]}
                                                    >
                                                        <Input.Password placeholder="1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZ" />
                                                    </Form.Item>
                                                </Col>
                                                <Col span={12}>
                                                    <Form.Item
                                                        name="telegram_chat_id"
                                                        label="Chat ID"
                                                        rules={[
                                                            { required: true, message: t('notification.validation.telegramChatIdRequired') }
                                                        ]}
                                                    >
                                                        <Input placeholder={t('notification.telegramChatIdPlaceholder')} />
                                                    </Form.Item>
                                                </Col>
                                            </Row>
                                            <Alert
                                                message={t('notification.telegramConfigurationGuide')}
                                                description={
                                                    <div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.configurationSteps')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            1. {t('notification.telegramStep1')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            2. {t('notification.telegramStep2')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            3. {t('notification.telegramStep3Detailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            4. {t('notification.telegramStep4Detailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            5. {t('notification.telegramStep5Detailed')}
                                                        </div>
                                                        <div style={{ marginLeft: 16, marginBottom: 4 }}>
                                                            {t('notification.telegramPersonalChatDetailed')} <code>https://api.telegram.org/bot{"{BOT_TOKEN}"}/getUpdates</code>
                                                        </div>
                                                        <div style={{ marginLeft: 16, marginBottom: 4 }}>
                                                            {t('notification.telegramGroupChatDetailed')}
                                                        </div>
                                                        <div style={{ marginLeft: 16, marginBottom: 4 }}>
                                                            {t('notification.telegramChannelDetailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            6. {t('notification.telegramStep6Detailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            7. {t('notification.telegramStep7Detailed')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.telegramChatIdFormatsTitle')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.telegramPersonalChatId')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.telegramGroupChatId')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.telegramSuperGroupChatId')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            {t('notification.telegramUsernameFormat')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            <strong>{t('notification.telegramNoticeTitle')}</strong>
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.telegramInteractionRequired')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.telegramGroupMembership')}
                                                        </div>
                                                        <div style={{ marginBottom: 4 }}>
                                                            {t('notification.telegramChannelPermission')}
                                                        </div>
                                                        <div style={{ marginBottom: 8 }}>
                                                            {t('notification.telegramPrivacyNote')}
                                                        </div>
                                                        <div>
                                                            📖 {t('notification.detailedDocumentation')}<a href="https://core.telegram.org/bots#6-botfather" target="_blank" rel="noopener noreferrer">{t('notification.telegramDocumentationLink')}</a>
                                                        </div>
                                                    </div>
                                                }
                                                type="info"
                                                showIcon
                                                style={{ marginBottom: 16 }}
                                            />
                                            <div style={{ 
                                                background: '#f6f8fa', 
                                                padding: '12px', 
                                                borderRadius: '4px', 
                                                fontFamily: 'monospace',
                                                fontSize: '12px',
                                                marginBottom: 16,
                                                border: '1px solid #e1e4e8'
                                            }}>
                                                <div style={{ marginBottom: 8 }}>
                                                    <strong>{t('notification.telegramQuickChatIdTitle')}</strong>
                                                </div>
                                                <div style={{ marginBottom: 4 }}>
                                                    1. {t('notification.telegramQuickStep1').replace('YOUR_BOT_TOKEN', `<span style={{ color: '#d73a49' }}>YOUR_BOT_TOKEN</span>`)}
                                                </div>
                                                <div style={{ marginBottom: 4 }}>
                                                    2. {t('notification.telegramQuickStep2')}
                                                </div>
                                                <div>
                                                    3. {t('notification.telegramQuickStep3').replace('"chat":{"id":123456789}', `<span style={{ color: '#005cc5' }}>"chat":{"{"}"id":123456789{"}"}</span>`)}
                                                </div>
                                            </div>
                                        </Card>
                                    ) : null
                                }
                            </Form.Item>
                        </Card>
                    </Col>

                    {/* 预警设置 */}
                    <Col span={24}>
                        <Card
                            title={
                                <Space>
                                    <DollarOutlined />
                                    <span>{t('notification.alertSettings')}</span>
                                </Space>
                            }
                        >
                            <Row gutter={16}>
                                <Col span={12}>
                                    <div style={{ 
                                        padding: '16px', 
                                        border: '1px solid #f0f0f0', 
                                        borderRadius: '8px' 
                                    }}>
                                        <Row justify="space-between" align="middle" style={{ marginBottom: 8 }}>
                                            <Col span={16}>
                                                <Text strong>{t('notification.balanceThreshold')}</Text>
                                            </Col>
                                            <Col span={8} style={{ textAlign: 'right' }}>
                                                <Form.Item name="balance_threshold" style={{ margin: 0 }}>
                                                    <InputNumber
                                                        style={{ width: '100%' }}
                                                        min={0}
                                                        precision={0}
                                                        addonBefore="$"
                                                        placeholder="30"
                                                    />
                                                </Form.Item>
                                            </Col>
                                        </Row>
                                        <Text type="secondary" style={{ fontSize: '12px' }}>
                                            {t('notification.balanceThresholdDescription', '当余额低于此数值时发送预警通知（实时检查，2小时内最多通知1次）')}
                                        </Text>
                                    </div>
                                </Col>
                                <Col span={12}>
                                    <div style={{ 
                                        padding: '16px', 
                                        border: '1px solid #f0f0f0', 
                                        borderRadius: '8px',
                                        backgroundColor: '#f6ffed'
                                    }}>
                                        <div style={{ marginBottom: 8 }}>
                                            <Space>
                                                <CheckCircleOutlined style={{ color: '#52c41a' }} />
                                                <Text strong>{t('notification.alertExplanationTitle', '预警说明')}</Text>
                                            </Space>
                                        </div>
                                        <Text type="secondary" style={{ fontSize: '12px' }}>
                                            {t('notification.alertExplanation',
                                                '• 余额预警：实时检查用户余额，低于阈值时立即通知\n• 营销通知：每日检查一次，避免过度打扰\n• 安全警报：发生时立即通知，确保账户安全\n• 系统公告：重要更新时一次性通知所有用户'
                                            ).split('\n').map((line, index) => (
                                                <span key={index}>
                                                    {line}
                                                    {index < 3 && <br/>}
                                                </span>
                                            ))}
                                        </Text>
                                    </div>
                                </Col>
                            </Row>
                        </Card>
                    </Col>

                    {/* 保存按钮 */}
                    <Col span={24}>
                        <Card>
                            <div style={{ textAlign: 'center' }}>
                                <Space size="large">
                                    <Button 
                                        type="primary" 
                                        size="large"
                                        icon={<SaveOutlined />}
                                        loading={saving}
                                        onClick={handleSave}
                                    >
                                        {t('common.save')}
                                    </Button>
                                    <Alert
                                        message={t('notification.testRecommendation', '保存设置后建议进行测试，确保通知功能正常工作')}
                                        type="info"
                                        showIcon
                                        style={{ textAlign: 'left' }}
                                    />
                                </Space>
                            </div>
                        </Card>
                    </Col>
                </Row>
            </Form>
        </div>
    );
};

export default NotificationSettingsPage; 