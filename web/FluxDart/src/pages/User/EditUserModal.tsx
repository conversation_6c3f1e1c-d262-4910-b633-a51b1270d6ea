import React, {useEffect, useState} from 'react'
import {API, dataToJsonString, jsonValidationRule, showError} from '../../helpers'
import {App, Button, Checkbox, Form, Input, InputNumber, Modal, Select, Space, Spin, Switch, Divider, Tooltip, Alert, Tag, DatePicker, Radio, Card} from 'antd'
import {
    CheckCircleTwoTone,
    CloseCircleTwoTone,
    CodeTwoTone,
    ContactsTwoTone,
    GithubFilled,
    InfoCircleTwoTone,
    MailFilled,
    PhoneFilled,
    SettingTwoTone,
    TagTwoTone,
    UnlockTwoTone,
    WechatFilled,
    CloudTwoTone,
    ApiOutlined,
    SyncOutlined,
    InfoCircleOutlined,
    ClockCircleTwoTone,
    RobotOutlined,
    EyeTwoTone,
    DollarTwoTone,
    CrownFilled
} from '@ant-design/icons'
import {modalProps, UserInfoById} from "../../constants";
import GroupDiscountEditor from "./GroupDiscountEditor";
import RouteDiscountEditor from "./RouteDiscountEditor";
import dayjs from 'dayjs';

const {Option} = Select;


const EditUser = ({
                      isUserEditModalVisible,
                      setIsUserEditModalVisible,
                      editingUserId,
                      setEditingUserId,
                      reload,
                      isRoot
                  }) => {

    const [form] = Form.useForm();
    const {message: AntdMessage, modal} = App.useApp();
    const [loading, setLoading] = useState(true);

    const [groupOptions, setGroupOptions] = useState([]);
    const [submitting, setSubmitting] = useState(false);
    const [quotaWarning, setQuotaWarning] = useState('');

    const [showAdvancedItems, setShowAdvancedItems] = useState<boolean>(false);
    const [isRateLimitEnabled, setIsRateLimitEnabled] = useState<boolean>(false);

    const [isThisUserRoot, setIsThisUserRoot] = useState<boolean>(false);

    const [mjSensitiveWordsRefund, setMjSensitiveWordsRefund] = useState<number | null>(null);

    const [groupDiscounts, setGroupDiscounts] = useState({});

    const [routeDiscounts, setRouteDiscounts] = useState({});

    const [quotaExpireMode, setQuotaExpireMode] = useState<'unlimited' | 'date' | 'days'>('date');
    const [quotaExpireDate, setQuotaExpireDate] = useState<dayjs.Dayjs | null>(null);
    const [quotaExpireDays, setQuotaExpireDays] = useState<number>(30);

    const [isSay1DirectSuccessEnabled, setIsSay1DirectSuccessEnabled] = useState<boolean>(false);

    const [adminPermissions, setAdminPermissions] = useState<number[]>([]);

    const fetchGroups = async () => {
        try {
            let res = await API.get(`/api/group/`);
            const groups = res.data.data;
            setGroupOptions(groups.map((group: string) => ({
                key: group, text: group, value: group
            })));

            // 只在第一次加载时初始化分组折扣
            setGroupDiscounts(prev => {
                if (Object.keys(prev).length === 0) {
                    const initialDiscounts = {};
                    groups.forEach(group => {
                        initialDiscounts[group] = 1;
                    });
                    return initialDiscounts;
                }
                return prev;
            });
        } catch (error) {
            showError(error);
        }
    };


    const loadUser = async () => {
        try {
            let res = await API.get(`/api/user/${editingUserId}`);
            const {success, message, data} = res.data;
            const UserInfoById = data as UserInfoById & {
                quota_warning?: string;
                redis_quota?: number;
                redis_quota_expire_time?: number;
                quota_diff?: number;
            };
            if (success) {
                try {
                    // 检查是否有余额不一致警告
                    if (UserInfoById.quota_warning) {
                        setQuotaWarning(UserInfoById.quota_warning);
                    } else {
                        setQuotaWarning('');
                    }
                    
                    // 更新分组折扣
                    if (UserInfoById.group_discounts) {
                        const parsedDiscounts = JSON.parse(UserInfoById.group_discounts);
                        setGroupDiscounts(prev => ({
                            ...prev,  // 保留默认值
                            ...parsedDiscounts  // 覆盖用户自定义的值
                        }));
                    }

                    // 更新动态路由折扣
                    if (UserInfoById.route_discounts) {
                        const parsedRouteDiscounts = JSON.parse(UserInfoById.route_discounts);
                        setRouteDiscounts(parsedRouteDiscounts);
                    } else {
                        setRouteDiscounts({});
                    }

                    // 先设置是否显示高级选项，确保权限勾选框能够渲染
                    setIsThisUserRoot(UserInfoById.role === 100);
                    const shouldShowAdvanced = UserInfoById.role >= 10 ||
                        UserInfoById.admin_access_flags > 0 ||
                        UserInfoById.model_ratio !== '' ||
                        UserInfoById.model_fixed_price !== '' ||
                        UserInfoById.completion_ratio !== '';
                    setShowAdvancedItems(shouldShowAdvanced);

                    // 处理权限标志位转换
                    const adminAccessFlags = UserInfoById.admin_access_flags || 0;
                    let permissionArray: number[] = [];

                    console.log('原始权限值:', adminAccessFlags);

                    // 如果 adminAccessFlags 为 -1 或 0，则表示没有权限
                    if (adminAccessFlags > 0) {
                        permissionArray = adminAccessFlags.toString(2)
                            .split('')
                            .reverse()
                            .map((item, index) => item === '1' ? Math.pow(2, index) : 0)
                            .filter(item => item !== 0);
                        console.log('解析后的权限数组:', permissionArray);
                    }

                    // 设置权限状态
                    setAdminPermissions(permissionArray);

                    // 使用 setTimeout 确保高级选项已经渲染完成后再设置表单值
                    setTimeout(() => {
                        // 先设置其他字段
                        form.setFieldsValue({
                            ...data,
                            model_ratio: dataToJsonString(UserInfoById.model_ratio),
                            model_fixed_price: dataToJsonString(UserInfoById.model_fixed_price),
                            completion_ratio: dataToJsonString(UserInfoById.completion_ratio),
                            mj_sensitive_words_refund: UserInfoById.mj_sensitive_words_refund,
                            trust_upstream_stream_usage: UserInfoById.trust_upstream_stream_usage || 0,
                            force_stream_option: UserInfoById.force_stream_option || 0,
                            force_downstream_stream_usage: UserInfoById.force_downstream_stream_usage || 0,
                            say1_direct_success_mode: UserInfoById.say1_direct_success_mode || 0,
                            log_upstream_response_enabled: UserInfoById.log_upstream_response_enabled || 0,
                            log_full_response_enabled: UserInfoById.log_full_response_enabled || 0,
                            mock_openai_complete_format: UserInfoById.mock_openai_complete_format || 0,
                            claude_message_normalization: (data as any).claude_message_normalization || 0,
                            extra_visible_groups: UserInfoById.extra_visible_groups || '',
                            log_downstream_error_enabled: (data as any).log_downstream_error_enabled || 0,
                            max_prompt_log_length: (data as any).max_prompt_log_length || 0,
                        });

                        // 单独设置权限字段，并强制触发更新
                        form.setFieldValue('admin_access_flags', permissionArray);
                        console.log('表单值设置完成，权限数组:', permissionArray);
                    }, 100);
                    setMjSensitiveWordsRefund(UserInfoById.mj_sensitive_words_refund);
                    setIsSay1DirectSuccessEnabled(UserInfoById.say1_direct_success_mode === 1);

                    // 处理余额有效期
                    if (UserInfoById.quota_expire_time) {
                        const expireDate = dayjs.unix(UserInfoById.quota_expire_time);
                        if (expireDate.isValid()) {
                            setQuotaExpireMode('date');
                            setQuotaExpireDate(expireDate);
                        } else {
                            setQuotaExpireMode('unlimited');
                            setQuotaExpireDate(null);
                        }
                    } else {
                        setQuotaExpireMode('unlimited');
                        setQuotaExpireDate(null);
                    }
                } catch (error) {
                    console.error('解析失败：', error);
                }
            } else {
                AntdMessage.error('用户信息加载失败：' + message);
            }
        } catch (error) {
            showError(error);
        }
    };

    useEffect(() => {
        let isSubscribed = true;  // 用于防止组件卸载后的状态更新

        const loadData = async () => {
            if (editingUserId > 0) {
                setLoading(true);
                try {
                    // 先加载分组
                    await fetchGroups();
                    console.log('分组加载完成');

                    // 再加载用户信息
                    if (isSubscribed) {
                        await loadUser();
                        console.log('用户信息加载完成');
                    }
                } catch (error) {
                    console.error('数据加载失败:', error);
                    showError(error);
                } finally {
                    if (isSubscribed) {
                        setLoading(false);
                    }
                }
            }
        };

        loadData();

        // 清理函数
        return () => {
            isSubscribed = false;
        };
    }, [editingUserId]); // 只依赖 editingUserId

    useEffect(() => {
        console.log('groupDiscounts changed:', groupDiscounts);
    }, [groupDiscounts]);

    const updatePasswordConfirm = () => {
        return new Promise((resolve) => {
            modal.confirm({
                title: '警告',
                content: '提交的操作包含对用户密码的修改，是否继续？',
                onOk: () => resolve(true),
                onCancel: () => resolve(false),
                getContainer: () => document.getElementById('globalModalContainer') as HTMLElement,
            });
        });
    };

    const onFinish = async (values: any) => {
        if (values.password && values.password.length > 0) {
            const confirmResult = await updatePasswordConfirm();
            if (!confirmResult) return;
        }

        if (showAdvancedItems) {
            // 使用权限状态而不是表单值
            const adminAccessFlagsSum = adminPermissions.reduce((sum, flag) => sum + flag, 0);
            values.admin_access_flags = adminAccessFlagsSum;
            if (adminAccessFlagsSum > 0) {
                values.role = 10;
            } else {
                values.role = 1;
                values.admin_access_flags = -1;
            }
        }

        if (!isRateLimitEnabled) {
            values.rate_limit = -1;
            values.rate_limit_exceeded_message = '';
        }

        values.mj_sensitive_words_refund = mjSensitiveWordsRefund;
        values.group_discounts = JSON.stringify(groupDiscounts);
        values.route_discounts = JSON.stringify(routeDiscounts);
        values.trust_upstream_stream_usage = values.trust_upstream_stream_usage || 0;
        values.force_stream_option = values.force_stream_option || 0;
        values.force_downstream_stream_usage = values.force_downstream_stream_usage || 0;
        values.say1_direct_success_mode = isSay1DirectSuccessEnabled ? 1 : (values.say1_direct_success_mode || 0);
        values.mock_openai_complete_format = values.mock_openai_complete_format || 0;
        values.claude_message_normalization = values.claude_message_normalization || 0;

        // 处理余额有效期
        if (quotaExpireMode === 'unlimited') {
            values.quota_expire_time = 0; // 0 表示无限期
        } else if (quotaExpireMode === 'date' && quotaExpireDate) {
            values.quota_expire_time = quotaExpireDate.unix();
        } else if (quotaExpireMode === 'days') {
            values.quota_expire_time = dayjs().add(quotaExpireDays, 'day').unix();
        }

        setSubmitting(true);
        try {
            values.id = editingUserId;
            const res = await API.put(`/api/user/`, values);
            const {success, message} = res.data;
            if (success) {
                reload();
                setIsUserEditModalVisible(false);
                AntdMessage.success('用户信息更新成功！');
            } else {
                AntdMessage.error('用户信息更新失败：' + message);
            }
        } catch (error) {
            showError(error);
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Modal
            {...modalProps}
            title="编辑用户信息"
            open={isUserEditModalVisible}
            centered
            onCancel={() => setIsUserEditModalVisible(false)}
            styles={{body: {maxHeight: '75vh', overflow: 'auto', overflowX: 'hidden', padding: 5,},}}
            afterClose={() => {
                form.resetFields();
                setEditingUserId(0);
                setShowAdvancedItems(false);
                setIsRateLimitEnabled(false);
                setMjSensitiveWordsRefund(null);
                setGroupDiscounts({}); // 重置分组折扣
                setRouteDiscounts({}); // 重置动态路由折扣
                setQuotaExpireMode('date');
                setQuotaExpireDate(null);
                setQuotaExpireDays(30);
                setIsSay1DirectSuccessEnabled(false);
                setQuotaWarning(''); // 清空余额警告
                setAdminPermissions([]); // 重置权限状态
            }}
        >
            <Spin spinning={loading}>
                {quotaWarning && (
                    <Alert
                        message="余额数据不一致警告"
                        description={
                            <div>
                                {quotaWarning}
                                <div style={{ marginTop: 10 }}>
                                    <Button 
                                        type="primary" 
                                        danger 
                                        size="small" 
                                        onClick={async () => {
                                            try {
                                                AntdMessage.loading({content: '正在重置Redis余额...', key: 'resetRedis'});
                                                const res = await API.post(`/api/user/reset_redis_quota/${editingUserId}`);
                                                const {success, message} = res.data;
                                                if (success) {
                                                    AntdMessage.success({content: '操作成功，已清除Redis缓存', key: 'resetRedis'});
                                                    setQuotaWarning('');
                                                    // 重新加载用户信息
                                                    await loadUser();
                                                } else {
                                                    AntdMessage.error({content: `操作失败: ${message}`, key: 'resetRedis'});
                                                }
                                            } catch (error) {
                                                AntdMessage.error({content: '操作失败，请稍后再试', key: 'resetRedis'});
                                                showError(error);
                                            }
                                        }}
                                    >
                                        清除Redis余额缓存
                                    </Button>
                                </div>
                            </div>
                        }
                        type="warning"
                        showIcon
                        style={{ marginBottom: 16 }}
                    />
                )}
                <Form
                    style={{marginTop: 15}}
                    form={form}
                    name="editUserForm"
                    onFinish={onFinish}
                    autoComplete='off'
                    onValuesChange={(changedValues) => {
                        if ('mj_sensitive_words_refund' in changedValues) {
                            setMjSensitiveWordsRefund(changedValues.mj_sensitive_words_refund);
                        }
                    }}
                >
                    <Form.Item label='用户名称' name={'username'}>
                        <Input prefix={<ContactsTwoTone/>} placeholder='设置新用户名'/>
                    </Form.Item>
                    <Form.Item label='用户备注' name={'remark'}>
                        <Input prefix={<TagTwoTone/>}/>
                    </Form.Item>
                    <Form.Item label='用户分组' name={'group'}>
                        <Select placeholder='请选择分组'>
                            {groupOptions.map((group: { key: string, text: string, value: string }) => (
                                <Option key={group.value} value={group.value} label={group.text}>
                                    {group.text}
                                </Option>
                            ))}
                        </Select>
                    </Form.Item>
                    
                    {isRoot && (
                        <Form.Item label='用户权限' name={'role'}>
                            <Select placeholder='请选择用户权限'>
                                <Option value={1}>
                                    <ContactsTwoTone twoToneColor="#1890ff" /> 普通用户
                                </Option>
                                <Option value={5}>
                                    <CrownFilled style={{ color: '#faad14' }} /> 代理商用户
                                </Option>
                                <Option value={10}>
                                    <SettingTwoTone twoToneColor="#52c41a" /> 管理员用户
                                </Option>
                                <Option value={100}>
                                    <CrownFilled style={{ color: '#f5222d' }} /> 超级管理员
                                </Option>
                            </Select>
                        </Form.Item>
                    )}
                    
                    <Form.Item label='用户状态' name={'status'}>
                        <Select placeholder='请选择用户状态'>
                            <Option value={1}>
                                <CheckCircleTwoTone twoToneColor="#52c41a" /> 正常
                            </Option>
                            <Option value={2}>
                                <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 禁用
                            </Option>
                        </Select>
                    </Form.Item>
                    
                    <Form.Item label='速率限制'>
                        <Switch checked={isRateLimitEnabled} onChange={() => setIsRateLimitEnabled(!isRateLimitEnabled)}
                                checkedChildren={'启用'}
                                unCheckedChildren={'禁用'}/>
                    </Form.Item>

                    {isRateLimitEnabled &&
                        <>
                            <Form.Item label='速率限制' name={'rate_limit'}>
                                <InputNumber prefix={<CodeTwoTone/>} name='rate_limit' suffix={'次/3分钟'}
                                             style={{width: '100%'}}/>
                            </Form.Item>

                            <Form.Item label='限制提示' name={'rate_limit_exceeded_message'}>
                                <Input prefix={<InfoCircleTwoTone/>} placeholder='速率超过限制时展示的信息'/>
                            </Form.Item>
                        </>
                    }

                    <Form.Item>
                        <Checkbox
                            checked={showAdvancedItems}
                            defaultChecked={false}
                            onClick={() => setShowAdvancedItems(!showAdvancedItems)}>更多信息</Checkbox>
                    </Form.Item>

                    {showAdvancedItems &&
                        <>
                            <Form.Item label='用户昵称' name={'display_name'}>
                                <Input prefix={<ContactsTwoTone/>}/>
                            </Form.Item>

                            <Form.Item label='修改密码' name={'password'}>
                                <Input
                                    variant='filled'
                                    prefix={<UnlockTwoTone/>}
                                />
                            </Form.Item>

                            {isRoot && !isThisUserRoot &&
                                <Form.Item label='授权接口' name={'admin_access_flags'}>
                                    <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f6f8fa', borderRadius: 6, border: '1px solid #d1d9e0' }}>
                                        <div style={{ marginBottom: 8, fontWeight: 'bold', color: '#0969da' }}>
                                            📋 权限配置说明
                                        </div>
                                        <div style={{ fontSize: '12px', lineHeight: '1.5', color: '#656d76' }}>
                                            <div><strong>传统权限（完整权限）：</strong>勾选后拥有对应模块的完整读写权限</div>
                                            <div><strong>读写分离权限：</strong>可以单独控制查看和编辑权限</div>
                                            <div style={{ color: '#d1242f', marginTop: 4 }}>
                                                <strong>⚠️ 重要：</strong>如果设置了读写分离权限，传统权限将被忽略。要实现只读权限，请取消传统权限，只勾选对应的"查看"权限。
                                            </div>
                                        </div>
                                    </div>

                                    <Checkbox.Group
                                        value={adminPermissions}
                                        onChange={(checkedValues) => {
                                            console.log('权限勾选变化:', checkedValues);
                                            setAdminPermissions(checkedValues as number[]);
                                            form.setFieldValue('admin_access_flags', checkedValues);
                                        }}
                                    >
                                        <div style={{ marginBottom: 12 }}>
                                            <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#24292f' }}>
                                                🔧 传统权限（完整读写权限）
                                            </div>
                                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px 16px' }}>
                                                <Checkbox value={1}>渠道&渠道组</Checkbox>
                                                <Checkbox value={2}>日志</Checkbox>
                                                <Checkbox value={4}>详细日志</Checkbox>
                                                <Checkbox value={8}>兑换码</Checkbox>
                                                <Checkbox value={16}>用户</Checkbox>
                                                <Checkbox value={32}>套餐</Checkbox>
                                                <Checkbox value={64}>系统配置</Checkbox>
                                                <Checkbox value={256}>渠道密钥查看</Checkbox>
                                            </div>
                                        </div>

                                        <div style={{ marginBottom: 12 }}>
                                            <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#24292f' }}>
                                                👁️ 查看权限（只读）
                                            </div>
                                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px 16px' }}>
                                                <Checkbox value={512}>渠道查看</Checkbox>
                                                <Checkbox value={2048}>兑换码查看</Checkbox>
                                                <Checkbox value={8192}>用户查看</Checkbox>
                                                <Checkbox value={32768}>套餐查看</Checkbox>
                                                <Checkbox value={131072}>系统配置查看</Checkbox>
                                                <Checkbox value={524288}>能力查看</Checkbox>
                                            </div>
                                        </div>

                                        <div>
                                            <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#24292f' }}>
                                                ✏️ 编辑权限（可修改）
                                            </div>
                                            <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px 16px' }}>
                                                <Checkbox value={1024}>渠道编辑</Checkbox>
                                                <Checkbox value={4096}>兑换码编辑</Checkbox>
                                                <Checkbox value={16384}>用户编辑</Checkbox>
                                                <Checkbox value={65536}>套餐编辑</Checkbox>
                                                <Checkbox value={262144}>系统配置编辑</Checkbox>
                                                <Checkbox value={1048576}>能力编辑</Checkbox>
                                            </div>
                                        </div>
                                    </Checkbox.Group>

                                    <div style={{ marginTop: 16, padding: 12, backgroundColor: '#fff8dc', borderRadius: 6, border: '1px solid #f4d03f' }}>
                                        <div style={{ fontSize: '12px', lineHeight: '1.5', color: '#856404' }}>
                                            <div><strong>💡 配置示例：</strong></div>
                                            <div>• <strong>只读用户：</strong>取消"渠道&渠道组"，勾选"渠道查看"</div>
                                            <div>• <strong>编辑用户：</strong>勾选"渠道查看"+"渠道编辑"</div>
                                            <div>• <strong>传统用户：</strong>直接勾选"渠道&渠道组"</div>
                                        </div>
                                    </div>
                                </Form.Item>
                            }

                            <Form.Item 
                                label={<Space><EyeTwoTone />额外可见分组</Space>}
                                name="extra_visible_groups"
                                tooltip={{
                                    title: '用户额外可见的分组，多个分组用英文逗号分隔。这些额外分组将在系统查询分组接口中一并返回。',
                                    icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                }}
                            >
                                <Input placeholder="多个分组请用英文逗号分隔，如：vip,premium,enterprise" />
                            </Form.Item>

                            <Form.Item name={"model_ratio"} label={"模型倍率"} rules={[jsonValidationRule]}>
                                <Input.TextArea autoSize={{minRows: 2, maxRows: 7}}/>
                            </Form.Item>

                            <Form.Item name={"model_fixed_price"} label={"模型按次价格"} rules={[jsonValidationRule]}>
                                <Input.TextArea autoSize={{minRows: 2, maxRows: 7}}/>
                            </Form.Item>

                            <Form.Item name={"completion_ratio"} label={"补全倍率"} rules={[jsonValidationRule]}>
                                <Input.TextArea autoSize={{minRows: 2, maxRows: 7}}/>
                            </Form.Item>

                            <Form.Item name={"topup_ratio"} label={"充值倍率"}>
                                <InputNumber prefix={<CodeTwoTone/>}
                                             step={0.01}
                                             max={1000}
                                             min={0}
                                             style={{width: '100%'}}/>
                            </Form.Item>

                            <Form.Item label="分组折扣" name="group_discounts">
                                <GroupDiscountEditor
                                    groupDiscounts={groupDiscounts}
                                    setGroupDiscounts={setGroupDiscounts}
                                />
                            </Form.Item>

                            <Form.Item label="动态路由折扣" name="route_discounts">
                                <RouteDiscountEditor
                                    routeDiscounts={routeDiscounts}
                                    setRouteDiscounts={setRouteDiscounts}
                                />
                            </Form.Item>

                            <Form.Item
                                name="new_tik_token_billing"
                                label="新版tiktoken计费"
                                tooltip="是否开启新版tiktoken计费，默认0表示使用系统默认配置，1为旧版，2为新版"
                            >
                                <Select
                                    style={{ width: '100%' }}
                                    placeholder="请选择tiktoken计费版本"
                                >
                                    <Option value={0}>
                                        <CodeTwoTone /> 使用系统默认配置
                                    </Option>
                                    <Option value={1}>
                                        <CodeTwoTone twoToneColor="#eb2f96" /> 旧版
                                    </Option>
                                    <Option value={2}>
                                        <CodeTwoTone twoToneColor="#52c41a" /> 新版
                                    </Option>
                                </Select>
                            </Form.Item>
                            <Form.Item
                                name="mj_sensitive_words_refund"
                                label="MJ敏感词退款"
                                tooltip="MJ敏感词退款设置：0使用系统默认配置，1为返还，2为不返还"
                            >
                                <Select
                                    style={{ width: '100%' }}
                                    placeholder="请选择MJ敏感词退款设置"
                                >
                                    <Option value={0}>
                                        <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                    </Option>
                                    <Option value={1}>
                                        <CheckCircleTwoTone twoToneColor="#52c41a" /> 返还
                                    </Option>
                                    <Option value={2}>
                                        <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 不返还
                                    </Option>
                                </Select>
                            </Form.Item>

                            <Form.Item label='GitHub' name={'github_id'}>
                                <Input prefix={<GithubFilled/>} disabled placeholder='此项只读，不可直接修改'/>
                            </Form.Item>

                            <Form.Item label='微信账户' name={'wechat_id'}>
                                <Input prefix={<WechatFilled/>} disabled placeholder='此项只读，不可直接修改'/>
                            </Form.Item>

                            <Form.Item label='邮箱地址' name='email'>
                                <Input prefix={<MailFilled/>} disabled placeholder='此项只读，不可直接修改'/>
                            </Form.Item>

                            <Form.Item label={'手机号码'} name={'phone_number'}>
                                <Input prefix={<PhoneFilled/>} disabled placeholder='此项只读，不可直接修改'/>
                            </Form.Item>

                            <Form.Item
                                name="log_detail_enabled"
                                label="记录详细日志"
                                tooltip="是否记录详细日志，默认0表示使用系统默认配置，1为记录，2为不记录"
                            >
                                <Select
                                    style={{ width: '100%' }}
                                    placeholder="请选择是否记录详细日志"
                                >
                                    <Option value={0}>
                                        <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                    </Option>
                                    <Option value={1}>
                                        <CheckCircleTwoTone twoToneColor="#52c41a" /> 记录
                                    </Option>
                                    <Option value={2}>
                                        <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 不记录
                                    </Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="log_downstream_error_enabled"
                                label="记录下游错误"
                                tooltip="是否记录下游错误日志，默认0表示使用系统默认配置，1为记录，2为不记录"
                            >
                                <Select
                                    style={{ width: '100%' }}
                                    placeholder="请选择是否记录下游错误"
                                >
                                    <Option value={0}>
                                        <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                    </Option>
                                    <Option value={1}>
                                        <CheckCircleTwoTone twoToneColor="#52c41a" /> 记录
                                    </Option>
                                    <Option value={2}>
                                        <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 不记录
                                    </Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="log_upstream_response_enabled"
                                label="记录上游响应"
                                tooltip="是否记录上游原始响应内容，默认0表示使用系统默认配置，1为记录，2为不记录"
                            >
                                <Select
                                    style={{ width: '100%' }}
                                    placeholder="请选择是否记录上游响应"
                                >
                                    <Option value={0}>
                                        <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                    </Option>
                                    <Option value={1}>
                                        <CheckCircleTwoTone twoToneColor="#52c41a" /> 记录
                                    </Option>
                                    <Option value={2}>
                                        <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 不记录
                                    </Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="log_full_response_enabled"
                                label="记录完整响应"
                                tooltip="是否记录处理后的完整响应内容，默认0表示使用系统默认配置，1为记录，2为不记录"
                            >
                                <Select
                                    style={{ width: '100%' }}
                                    placeholder="请选择是否记录完整响应"
                                >
                                    <Option value={0}>
                                        <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                    </Option>
                                    <Option value={1}>
                                        <CheckCircleTwoTone twoToneColor="#52c41a" /> 记录
                                    </Option>
                                    <Option value={2}>
                                        <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 不记录
                                    </Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                name="max_prompt_log_length"
                                label="最大Prompt日志长度"
                                tooltip="用户个性化的最大Prompt日志长度限制，0表示使用系统默认配置，大于0的值表示自定义长度限制"
                            >
                                <InputNumber
                                    min={0}
                                    max={100000}
                                    style={{ width: '100%' }}
                                    placeholder="0表示使用系统默认配置"
                                    addonAfter="字符"
                                />
                            </Form.Item>

                            <Divider orientation="left">
                                <Space>
                                    <CloudTwoTone />
                                    流式用量配置
                                    <Tooltip title="这些配置的优先级为：用户 &gt; 渠道 &gt; 系统全局设置">
                                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    </Tooltip>
                                </Space>
                            </Divider>
                            
                            <Form.Item 
                                label={<Space><CloudTwoTone />信任上游流式用量统计</Space>}
                                name='trust_upstream_stream_usage'
                                tooltip={{
                                    title: '是否信任上游API返回的用量统计数据。启用后将直接使用上游返回的用量数据，否则将自行计算。',
                                    icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                }}
                            >
                                <Select placeholder='请选择'>
                                    <Option value={0}>使用系统默认配置</Option>
                                    <Option value={1}>信任上游统计</Option>
                                    <Option value={2}>不信任上游统计</Option>
                                </Select>
                            </Form.Item>
                            
                            <Form.Item 
                                label={<Space><ApiOutlined />强制要求上游返回用量</Space>}
                                name='force_stream_option'
                                tooltip={{
                                    title: '是否在请求中添加参数强制要求上游API返回用量统计。某些API提供商需要特定参数才会在流式响应中返回用量。',
                                    icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                }}
                            >
                                <Select placeholder='请选择'>
                                    <Option value={0}>使用系统默认配置</Option>
                                    <Option value={1}>强制要求</Option>
                                    <Option value={2}>不强制要求</Option>
                                </Select>
                            </Form.Item>
                            
                            <Form.Item 
                                label={<Space><SyncOutlined />强制返回下游流式用量</Space>}
                                name='force_downstream_stream_usage'
                                tooltip={{
                                    title: '是否在流式响应结束时强制返回用量统计给客户端。启用后将在流式响应结束时附加用量信息。',
                                    icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                }}
                            >
                                <Select placeholder='请选择'>
                                    <Option value={0}>使用系统默认配置</Option>
                                    <Option value={1}>强制返回</Option>
                                    <Option value={2}>不强制返回</Option>
                                </Select>
                            </Form.Item>
                            
                            <Alert
                                message="流式用量配置说明"
                                description={
                                    <ul style={{ paddingLeft: '20px', margin: '5px 0' }}>
                                        <li>这些配置的优先级为：用户设置 {'>'} 渠道设置 {'>'} 系统全局设置</li>
                                        <li>选择"使用系统默认配置"时，将按照优先级链向上查找配置</li>
                                        <li>信任上游统计：直接使用上游API返回的用量数据，通常更准确</li>
                                        <li>强制要求上游返回：在请求中添加参数要求上游返回用量统计</li>
                                        <li>强制返回下游：在流式响应结束时附加用量信息给客户端</li>
                                    </ul>
                                }
                                type="info"
                                showIcon
                                style={{ marginBottom: '20px' }}
                            />

                            <Divider orientation="left">
                                <Space>
                                    <ApiOutlined />
                                    请求响应格式
                                    <Tooltip title="控制API响应的格式和行为">
                                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    </Tooltip>
                                </Space>
                            </Divider>
                            
                            <Form.Item 
                                label={<Space><ApiOutlined />模拟官方响应格式</Space>}
                                name="mock_openai_complete_format"
                                tooltip={{
                                    title: '是否严格按照OpenAI最新响应格式返回，包括system_fingerprint等字段。注意：启用后会覆盖掉所有伪造原始模型响应之类的设置！',
                                    icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                }}
                                extra={<div style={{ color: '#ff4d4f' }}>注意：启用此功能会覆盖掉所有伪造原始模型响应之类的设置！</div>}
                            >
                                <Select placeholder='请选择'>
                                    <Option value={0}>使用系统默认配置</Option>
                                    <Option value={1}>启用</Option>
                                    <Option value={2}>禁用</Option>
                                </Select>
                            </Form.Item>

                            <Form.Item
                                label={<Space><CloudTwoTone />Claude 消息整理</Space>}
                                name="claude_message_normalization"
                                tooltip={{
                                    title: (
                                        <div>
                                            <p><b>Claude 消息整理功能说明：</b></p>
                                            <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>
                                                <li>仅对 /v1/messages 端点的 Claude 请求生效</li>
                                                <li>自动修复 max_tokens、messages、model 等参数错误</li>
                                                <li>处理图片大小限制、温度参数范围等问题</li>
                                                <li>提供友好的中文错误提示信息</li>
                                            </ul>
                                            <p style={{ color: '#52c41a' }}>
                                                <b>适用场景：</b> 提高 Claude API 请求的成功率，减少格式错误
                                            </p>
                                        </div>
                                    ),
                                    icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                }}
                                extra={<div style={{ color: '#1890ff' }}>仅对 Claude /v1/messages 端点生效</div>}
                            >
                                <Select placeholder='请选择'>
                                    <Option value={0}>使用系统默认配置</Option>
                                    <Option value={1}>启用</Option>
                                    <Option value={2}>禁用</Option>
                                </Select>
                            </Form.Item>

                            <Divider orientation="left">
                                <Space>
                                    <ApiOutlined />
                                    渠道路由设置
                                    <Tooltip title="这些配置的优先级为：用户 > 系统全局设置">
                                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    </Tooltip>
                                </Space>
                            </Divider>
                            
                            <Form.Item 
                                label={
                                    <span style={{ display: 'flex', alignItems: 'center' }}>
                                        基于渠道得分的路由
                                        <Tag color="blue" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                            Beta
                                        </Tag>
                                    </span>
                                }
                                name='channel_score_routing'
                                tooltip={{
                                    title: '是否使用基于渠道历史表现的智能路由。启用后系统会根据渠道的历史表现自动调整请求分配优先级。',
                                    icon: <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                }}
                            >
                                <Select placeholder='请选择'>
                                    <Option value={0}>使用系统默认配置</Option>
                                    <Option value={1}>启用</Option>
                                    <Option value={2}>禁用</Option>
                                </Select>
                            </Form.Item>

                            <Divider orientation="left">
                                <Space>
                                    <RobotOutlined style={{ color: '#1890ff' }} />
                                    拨测配置
                                    <Tooltip title="这些配置的优先级为：用户 > 系统全局设置">
                                        <InfoCircleOutlined style={{ color: '#1890ff' }} />
                                    </Tooltip>
                                </Space>
                            </Divider>

                            <Card title="吃掉拨测" size="small">
                                <Form.Item label="吃掉拨测" name="say1_direct_success_mode">
                                    <Select
                                        style={{ width: '100%' }}
                                        placeholder="请选择拨测处理方式"
                                        onChange={(value) => setIsSay1DirectSuccessEnabled(value === 1)}
                                    >
                                        <Option value={0}>
                                            <SettingTwoTone twoToneColor="#1890ff" /> 使用系统默认配置
                                        </Option>
                                        <Option value={1}>
                                            <CheckCircleTwoTone twoToneColor="#52c41a" /> 开启（拨测直接返回指定内容）
                                        </Option>
                                        <Option value={2}>
                                            <CloseCircleTwoTone twoToneColor="#ff4d4f" /> 关闭
                                        </Option>
                                    </Select>
                                </Form.Item>
                            </Card>

                            <Divider orientation="left">
                                <Space>
                                    <ClockCircleTwoTone />
                                    余额有效期设置
                                </Space>
                            </Divider>
                            
                            <Form.Item label="有效期模式">
                                <Radio.Group 
                                    value={quotaExpireMode} 
                                    onChange={(e) => setQuotaExpireMode(e.target.value)}
                                >
                                    <Radio value="unlimited">无限期</Radio>
                                    <Radio value="date">指定日期</Radio>
                                    <Radio value="days">指定天数</Radio>
                                </Radio.Group>
                            </Form.Item>
                            
                            {quotaExpireMode === 'date' && (
                                <Form.Item label="到期日期">
                                    <DatePicker 
                                        value={quotaExpireDate}
                                        onChange={(date) => setQuotaExpireDate(date)}
                                        style={{ width: '100%' }}
                                        showTime={{ format: 'HH:mm' }}
                                        format="YYYY-MM-DD HH:mm"
                                    />
                                </Form.Item>
                            )}
                            
                            {quotaExpireMode === 'days' && (
                                <Form.Item label="有效天数">
                                    <InputNumber 
                                        min={1}
                                        max={3650}
                                        value={quotaExpireDays}
                                        onChange={(value) => setQuotaExpireDays(value as number)}
                                        style={{ width: '100%' }}
                                        addonAfter="天"
                                    />
                                    <div style={{ marginTop: 8, color: '#666' }}>
                                        到期时间：{dayjs().add(quotaExpireDays, 'day').format('YYYY-MM-DD HH:mm')}
                                    </div>
                                </Form.Item>
                            )}
                        </>
                    }
                    <Form.Item>
                        <Space size='middle' style={{float: 'right'}}>
                            <Button onClick={() => setIsUserEditModalVisible(false)}>取消</Button>
                            <Button type='primary' htmlType='submit' loading={submitting}>提交</Button>
                        </Space>
                    </Form.Item>
                </Form>
            </Spin>
        </Modal>
    );
};
export default EditUser;
