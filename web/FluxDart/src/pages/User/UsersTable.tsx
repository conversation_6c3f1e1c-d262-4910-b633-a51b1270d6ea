import React, { useContext, useState, useRef } from 'react';
import { API, isMobile, safeParseJSON, showError } from '../../helpers';
import {
    createRenderFunctions,
    renderNumber,
    renderQuotaWithUsage,
    renderUserGroupTag,
    renderUserRole,
    renderUserStatus
} from '../../helpers/render';
import {App, Button, Input, Popconfirm, Popover, Select, Space, Menu, Dropdown, Tag} from 'antd';
import {
    CrownFilled,
    DeleteTwoTone,
    DollarTwoTone,
    EditTwoTone,
    FileTextTwoTone,
    PlayCircleTwoTone,
    StopTwoTone,
    DownOutlined,
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { ParamsType, ProTable } from '@ant-design/pro-components';
import type { ActionType, ProColumns } from '@ant-design/pro-table';
import moment from 'moment';
import { actionButtonProps, DefaultResponse, paginationProps } from '../../constants';
import { getCountByPathAndParam, getDataByPathAndParam } from "../../helpers/api-request-module";
import { StatusContext } from "../../context/Status";
import { UserContext } from "../../context/User";
import {useTranslation} from "react-i18next";

const EditUserModal = React.lazy(() => import('./EditUserModal'));
const AddUserModal = React.lazy(() => import('./AddUserModal'));
const AddQuotaModal = React.lazy(() => import('./AddQuotaModal'));

interface UserType {
    id: number;
    username: string;
    status: number;
    role: number;
    group: string;
    quota: number;
    used_quota: number;
    quota_expire_time: number;
    request_count: number;
    last_login_time: number;
    last_login_ip: string;
    remark: string;
    email: string;
    phone_number: string;
    inviter_id: number;
    inviteUserNumber: number;
    has_special_settings: boolean;
    quota_warning?: string;
    redis_quota?: number;
    redis_quota_expire_time?: number;
    quota_diff?: number;
    user_type: number; // 新增用户类型字段
}

const UsersTable: React.FC = () => {
    const {t} = useTranslation();
    const { renderModels, renderQuota,renderRoleTag,renderQuotaExpireTime, renderChannelStatusTag } = createRenderFunctions(t);

    const [statusState] = useContext(StatusContext);
    const [userState] = useContext(UserContext);
    const navigate = useNavigate();
    const { message: AntdMessage, modal } = App.useApp();
    const tableRef = useRef<ActionType>();
    const { Option } = Select;

    const [userCount, setUserCount] = useState(0);
    const [order, setOrder] = useState('');
    const [sortOrder, setSortOrder] = useState('');
    const userGroups = Object.keys(safeParseJSON(localStorage.getItem('GroupRatio') || '{}', {}));

    const [editingId, setEditingId] = useState<number | null>(null);
    const [editingRemark, setEditingRemark] = useState('');
    const [editingOriginalRemark, setEditingOriginalRemark] = useState('');

    const [editingUserIdForBalance, setEditingUserIdForBalance] = useState(0);
    const [editingUserIdForInfo, setEditingUserIdForInfo] = useState(0);
    const [isAddUserModalVisible, setIsAddUserModalVisible] = useState(false);
    const [isModalAddQuotaVisible, setIsModalAddQuotaVisible] = useState(false);
    const [isUserEditModalVisible, setIsUserEditModalVisible] = useState(false);

    const handleSort = ({ key }: { key: string }) => {
        const [newSortField, newSortOrder] = key.split('-');
        setOrder(newSortField);
        setSortOrder(newSortOrder);
        tableRef.current?.reload();
    };

    const sortMenu = (
        <Menu onClick={handleSort}>
            <Menu.Item key="quota-descend">可用余额降序</Menu.Item>
            <Menu.Item key="used_quota-descend">已用额度降序</Menu.Item>
            <Menu.Item key="request_count-descend">请求次数降序</Menu.Item>
        </Menu>
    );

    const request = async (params: ParamsType & {
        sorter?: Record<string, any>;
        filter?: Record<string, any>;
    }) => {
        try {
            const requestParams = {
                ...params,
                order,
                // sortOrder,
            };
            const [res, count] = await Promise.all([
                getDataByPathAndParam('user', requestParams),
                getCountByPathAndParam('user', requestParams)
            ]);
            const { success, data, message } = res as DefaultResponse;
            if (!success) AntdMessage.error('获取用户列表失败：' + message);
            setUserCount(count);
            return { success: success && count !== -1, data, total: count };
        } catch (error) {
            showError(error);
            return { success: false, data: [], total: 0 };
        }
    };

    const handleEditClick = (userId: number) => {
        setEditingUserIdForInfo(userId);
        setIsUserEditModalVisible(true);
    };

    const showAddQuotaModal = (userId: number) => {
        setEditingUserIdForBalance(userId);
        setIsModalAddQuotaVisible(true);
    };

    const manageUser = async (username: string, action: "delete" | "disable" | "enable") => {
        try {
            const res = await API.post('/api/user/manage', {username, action});
            const {success, message} = res.data;
            if (success) {
                AntdMessage.success('操作成功！');
                tableRef.current?.reload();
            } else {
                AntdMessage.error(message);
            }
        } catch (error) {
            showError(error);
        }
    };

    const renderUserType = (userType: number) => {
        switch (userType) {
            case 1:
                return <Tag color="blue">用户名</Tag>;
            case 2:
                return <Tag color="cyan">邮箱</Tag>;
            case 3:
                return <Tag color="orange">手机</Tag>;
            case 4:
                return <Tag color="red">Google</Tag>;
            case 5:
                return <Tag color="purple">GitHub</Tag>;
            case 6:
                return <Tag color="green">微信</Tag>;
            case 7:
                return <Tag color="geekblue">飞书</Tag>;
            case 8:
                return <Tag color="magenta">OIDC</Tag>;
            case 9:
                return <Tag color="volcano">Telegram</Tag>;
            default:
                return <Tag color="gray">未知</Tag>;
        }
    };

    const columns = [
        {
            title: '快速搜索',
            dataIndex: 'keyword',
            hideInTable: true,
            search: {
                order: 999,
            },
            tooltip: '支持搜索：用户名、备注、邮箱、手机号、IP地址',
        },
        {
            title: '特殊设置',
            dataIndex: 'has_special_settings',
            hideInTable: true,
            search: {
                order: 1,
            },
            renderFormItem: () => (
                <Select placeholder="特殊设置过滤" allowClear>
                    <Option value="true">有特殊设置</Option>
                    <Option value="false">无特殊设置</Option>
                </Select>
            ),
        },
        {
            title: 'ID',
            dataIndex: 'id',
            width: isMobile() ? '15%' : '4%',
            fixed: !isMobile(),
            disable: true,
            search: {
                order: 4,
            },
        },
        {
            title: '用户名',
            dataIndex: 'username',
            width: isMobile() ? '30%' : '10.5%',
            copyable: true,
            ellipsis: true,
            fixed: !isMobile(),
            disable: true,
            search: {
                order: 5,
            },
        },
        {
            title: '状态',
            dataIndex: 'status',
            width: isMobile() ? '15%' : '5.5%',
            align: 'center',
            render: (status: number) => renderUserStatus(status),
            renderFormItem: () => (
                <Select placeholder={'选择用户状态'} allowClear>
                    <Option value="1">正常</Option>
                    <Option value="2">禁用</Option>
                </Select>
            ),
        },
        {
            title: '用户类型',
            dataIndex: 'user_type',
            width: isMobile() ? '15%' : '6%',
            align: 'center',
            render: (userType: number) => renderUserType(userType),
            renderFormItem: () => (
                <Select placeholder="选择用户类型" allowClear>
                    <Option value="1">用户名登录</Option>
                    <Option value="2">邮箱登录</Option>
                    <Option value="3">手机登录</Option>
                    <Option value="4">Google登录</Option>
                    <Option value="5">GitHub登录</Option>
                    <Option value="6">微信登录</Option>
                    <Option value="7">飞书登录</Option>
                    <Option value="8">OIDC登录</Option>
                    <Option value="9">Telegram登录</Option>
                </Select>
            ),
            hideInTable: isMobile(),
        },
        {
            title: '权限',
            dataIndex: 'role',
            width: '5.5%',
            align: 'center',
            render: (role: number) => renderUserRole(role),
            renderFormItem: () => (
                <Select placeholder="选择用户权限" allowClear>
                    <Option value="1">用户</Option>
                    <Option value="5">代理商</Option>
                    <Option value="10">管理</Option>
                    <Option value="100">超管</Option>
                </Select>
            ),
            hideInTable: isMobile(),
        },
        {
            title: '分组',
            dataIndex: 'group',
            width: isMobile() ? '20%' : '7%',
            align: 'center',
            render: (group: string) => renderUserGroupTag(group),
            renderFormItem: () => (
                <Select placeholder="选择用户分组" allowClear>
                    {userGroups.map((group: string) => (
                        <Option value={group} key={group}>{group}</Option>
                    ))}
                </Select>
            ),
        },
        {
            title: '余额',
            dataIndex: 'quota',
            width: isMobile() ? '15%' : '7%',
            search: false,
            render: (quota: number, record: UserType) => {
                if (record.quota_warning) {
                    return (
                        <Popover
                            title="余额数据不一致警告"
                            content={
                                <div style={{ maxWidth: '300px' }}>
                                    {record.quota_warning}
                                    <div style={{ marginTop: 8 }}>
                                        <Button
                                            type="primary"
                                            danger
                                            size="small"
                                            onClick={async (e) => {
                                                e.stopPropagation(); // 阻止事件冒泡
                                                try {
                                                    AntdMessage.loading({content: '正在清除Redis缓存...', key: 'resetRedis'});
                                                    const res = await API.post(`/api/user/reset_redis_quota/${record.id}`);
                                                    const {success, message} = res.data;
                                                    if (success) {
                                                        AntdMessage.success({content: '操作成功，已清除Redis缓存', key: 'resetRedis'});
                                                        tableRef.current?.reload();
                                                    } else {
                                                        AntdMessage.error({content: `操作失败: ${message}`, key: 'resetRedis'});
                                                    }
                                                } catch (error) {
                                                    AntdMessage.error({content: '操作失败，请稍后再试', key: 'resetRedis'});
                                                    showError(error);
                                                }
                                            }}
                                        >
                                            清除Redis余额缓存
                                        </Button>
                                    </div>
                                </div>
                            }
                            trigger="hover"
                        >
                            <div>
                                {renderQuotaWithUsage(quota, record.used_quota)}
                                <span style={{ marginLeft: 5, color: '#ff4d4f' }}>⚠️</span>
                            </div>
                        </Popover>
                    );
                }
                return renderQuotaWithUsage(quota, record.used_quota);
            },
        },
        {
            title: '消耗',
            dataIndex: 'used_quota',
            width: '7%',
            search: false,
            render: (used_quota: number) => renderQuota(used_quota),
            hideInTable: isMobile(),
        },
        {
            title: '过期',
            dataIndex: 'quota_expire_time',
            width: '6.5%',
            search: false,
            render: (text: number) => renderQuotaExpireTime(statusState.status.QuotaExpireEnabled, text),
            hideInTable: isMobile(),
        },
        {
            title: '次数',
            dataIndex: 'request_count',
            width: '6%',
            search: false,
            render: (request_count: number) => renderNumber(request_count),
            hideInTable: isMobile(),
        },
        {
            title: '最近登录',
            dataIndex: 'last_login_time',
            width: '8%',
            search: false,
            render: (_text: any, record: UserType) => {
                let date = moment(record.last_login_time * 1000);
                if (date.year() === 1970) {
                    return '';
                } else {
                    return (
                        <Popover
                            title="更多信息"
                            content={
                                <>
                                    <p>{`时间：${date.format('YYYY/MM/DD HH:mm:ss')}`}</p>
                                    <p>{`IP地址：${record.last_login_ip}`}</p>
                                    <p>{`邮箱地址：${record.email ? record.email : '无记录'}`}</p>
                                    <p>{`手机号码：${record.phone_number ? record.phone_number : '无记录'}`}</p>
                                    <p>{`邀请者ID：${record.inviter_id !== 0 ? record.inviter_id : '无'}`}</p>
                                    <p>{`邀请人数：${record.inviteUserNumber}`}</p>
                                </>
                            }
                        >
                            {date.format('YYYY-MM-DD')}
                        </Popover>
                    );
                }
            },
            hideInTable: isMobile(),
        },
        {
            title: '最近IP',
            dataIndex: 'last_login_ip',
            width: '8%',
            search: false,
            hideInTable: true,
        },
        {
            title: '备注',
            dataIndex: 'remark',
            width: '11.5%',
            render: (text, record) => {
                const handleEdit = async () => {
                    if (editingRemark === editingOriginalRemark) {
                        setEditingId(null);
                        setEditingRemark('');
                        return;
                    }
                    try {
                        AntdMessage.loading({content: '正在更新用户备注...', key: 'updateRemark'});
                        const res = await API.put(`/api/user/`, {id: editingId, remark: editingRemark});
                        if (res && res.data && res.data.success) {
                            AntdMessage.success({content: '更新成功', key: 'updateRemark'});
                            tableRef.current?.reload();
                        } else {
                            AntdMessage.error({
                                content: res.data && res.data.message ? res.data.message : '更新失败',
                                key: 'updateRemark'
                            });
                        }
                    } catch (error) {
                        AntdMessage.error({content: '更新失败', key: 'updateRemark'});
                    }
                    setEditingId(null);
                    setEditingRemark('');
                };

                if (editingId === record.id) {
                    return (
                        <Input
                            size="small"
                            value={editingRemark}
                            onChange={(e) => setEditingRemark(e.target.value)}
                            onBlur={handleEdit}
                            onPressEnter={handleEdit}
                        />
                    );
                } else {
                    return (
                        <div
                            onClick={() => {
                                setEditingId(record.id);
                                setEditingRemark(record.remark || '');
                                setEditingOriginalRemark(record.remark || '');
                            }}
                            style={{
                                minHeight: '20px',
                                minWidth: '20px',
                                backgroundColor: 'transparent',
                            }}
                        >
                            {text}
                        </div>
                    );
                }
            },
            search: {
                order: 2,
            },
        },
        {
            title: '邮箱',
            dataIndex: 'email',
            hideInTable: true,
        },
        {
            title: '操作',
            key: 'action',
            fixed: isMobile() ? false : 'right',
            width: isMobile() ? '20%' : undefined,
            search: false,
            render: (_text: any, record: UserType) => (
                <Space 
                    size={isMobile() ? [2, 2] : 'middle'}
                    style={{ 
                        display: 'flex', 
                        flexWrap: 'wrap',
                        justifyContent: isMobile() ? 'center' : 'flex-start'
                    }}
                >
                    <Button
                        {...actionButtonProps}
                        size={isMobile() ? 'small' : 'middle'}
                        icon={<DollarTwoTone/>}
                        onClick={() => showAddQuotaModal(record.id)}
                    />
                    <Button
                        {...actionButtonProps}
                        size={isMobile() ? 'small' : 'middle'}
                        icon={<EditTwoTone/>}
                        onClick={() => handleEditClick(record.id)}
                    />
                    <Popconfirm
                        title={record.status === 1 ? `封禁账户 ${record.username}？` : `解封账户 ${record.username}？`}
                        onConfirm={() => manageUser(record.username, record.status === 1 ? 'disable' : 'enable')}
                        placement="topRight"
                        mouseLeaveDelay={0}
                        mouseEnterDelay={0.1}
                        overlayStyle={{ maxWidth: '300px', position: 'absolute' }}
                        destroyTooltipOnHide={true}
                        autoAdjustOverflow={false}
                        trigger="click"
                    >
                        <Button
                            {...actionButtonProps}
                            size={isMobile() ? 'small' : 'middle'}
                            icon={record.status === 1 ? <StopTwoTone/> : <PlayCircleTwoTone/>}
                            disabled={record.role === 100}
                        />
                    </Popconfirm>
                    <Button
                        {...actionButtonProps}
                        size={isMobile() ? 'small' : 'middle'}
                        icon={<FileTextTwoTone/>}
                        onClick={() => navigate(
                            `/log?current=1&pageSize=10&username=${record.username}`)}
                    />
                    <Button
                        {...actionButtonProps}
                        size={isMobile() ? 'small' : 'middle'}
                        icon={<DeleteTwoTone twoToneColor={'#f5222d'}/>}
                        disabled={record.role === 100}
                        onClick={() => {
                            modal.confirm({
                                title: (<span>删除用户<b>{record.username}</b>？</span>),
                                content: '删除后无法恢复，请谨慎操作！',
                                onOk: () => manageUser(record.username, 'delete'),
                                maskClosable: true,
                                okButtonProps: { danger: true }
                            });
                        }}
                    />
                </Space>
            ),
        },
    ];

    return (
        <>
            <ProTable<UserType, ParamsType & { sorter?: Record<string, any>; filter?: Record<string, any> }>
                actionRef={tableRef}
                columns={columns as any}
                request={request}
                rowKey="id"
                pagination={{
                    ...paginationProps,
                    total: userCount,
                }}
                search={{
                    labelWidth: 'auto',
                    defaultCollapsed: true,
                    span: isMobile() ? 24 : 4,
                    defaultColsNumber: 6,
                }}
                dateFormatter="string"
                headerTitle="用户管理"
                scroll={isMobile() ? { x: 700 } : { x: 1300 }}
                toolBarRender={() => [
                    <Button
                        key="add"
                        type="primary"
                        icon={<CrownFilled />}
                        onClick={() => setIsAddUserModalVisible(true)}
                    >
                        添加
                    </Button>,
                    <Dropdown key="sort" overlay={sortMenu}>
                        <Button>
                            排序 <DownOutlined />
                        </Button>
                    </Dropdown>
                ]}
            />

            <AddUserModal
                isAddUserModalVisible={isAddUserModalVisible}
                setIsAddUserModalVisible={setIsAddUserModalVisible}
                reload={() => tableRef.current?.reload()}
            />

            <AddQuotaModal
                isModalAddQuotaVisible={isModalAddQuotaVisible}
                setIsModalAddQuotaVisible={setIsModalAddQuotaVisible}
                editingUserId={editingUserIdForBalance}
                setEditingUserId={setEditingUserIdForBalance}
                isQuotaExpireEnabled={statusState.status.QuotaExpireEnabled}
                reload={() => tableRef.current?.reload()}
            />

            <EditUserModal
                isUserEditModalVisible={isUserEditModalVisible}
                setIsUserEditModalVisible={setIsUserEditModalVisible}
                editingUserId={editingUserIdForInfo}
                setEditingUserId={setEditingUserIdForInfo}
                reload={() => tableRef.current?.reload()}
                isRoot={userState.user.role === 100}
            />
        </>
    );
};

export default UsersTable;
