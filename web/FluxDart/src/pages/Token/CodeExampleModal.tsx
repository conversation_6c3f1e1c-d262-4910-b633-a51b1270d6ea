import React, { useState, useMemo } from "react";
import { App, AutoComplete, Button, Modal, Radio, Space, Switch } from "antd";
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { xonokai } from "react-syntax-highlighter/dist/esm/styles/prism";
import { HappyProvider } from "@ant-design/happy-work-theme";
import { modalProps } from "../../constants";
import { copy } from "../../helpers";

interface CodeExampleModalProps {
    isCodeExampleModalVisible: boolean;
    setIsCodeExampleModalVisible: (visible: boolean) => void;
    serverAddress: string;
    apiToken: string;
    availableModels?: string[]; // 新增：可用模型列表
}

type LanguageOption = 'Curl' | 'Python (SDK)' | 'Python (requests)' | 'Node.js' | 'Java' | 'C#' | 'Ruby' | 'Go' | 'PHP' | 'Rust' | 'C' | 'C++' | 'Dart' | 'AutoJS';

type ApiFormat = 'chat/completions' | 'responses' | 'messages' | 'images/generations' | 'images/edits';

const CodeExampleModal: React.FC<CodeExampleModalProps> = ({
                                                               isCodeExampleModalVisible,
                                                               setIsCodeExampleModalVisible,
                                                               serverAddress,
                                                               apiToken,
                                                               availableModels = []
                                                           }) => {
    const [selectModel, setSelectModel] = useState<string>('gpt-4o-mini');
    const [isTestMode, setIsTestMode] = useState<boolean>(false);
    const [isStreamMode, setIsStreamMode] = useState<boolean>(false);
    const [codeExampleLanguage, setCodeExampleLanguage] = useState<LanguageOption>('Curl');
    const [apiFormat, setApiFormat] = useState<ApiFormat>('chat/completions');
    const [hasImageInput, setHasImageInput] = useState<boolean>(false);
    const { message: AntdMessage } = App.useApp();

    // 静态模型列表作为备选
    const staticModels = ['gpt-4o-mini', 'gpt-3.5-turbo', 'gpt-4-turbo', 'claude-3-opus-20240229'];

    // 合并动态模型和静态模型，去重并排序
    const modelOptions = useMemo(() => {
        // 如果没有可用模型列表，使用静态列表
        if (!availableModels || availableModels.length === 0) {
            return staticModels.map(model => ({ value: model }));
        }

        // 创建一个Set来快速去重
        const modelSet = new Set<string>();
        const result: string[] = [];

        // 先添加动态模型（按令牌可用性排序）
        availableModels.forEach(model => {
            if (model && !modelSet.has(model)) {
                modelSet.add(model);
                result.push(model);
            }
        });

        // 再添加静态模型作为补充
        staticModels.forEach(model => {
            if (model && !modelSet.has(model)) {
                modelSet.add(model);
                result.push(model);
            }
        });

        return result.map(model => ({ value: model }));
    }, [availableModels]);
    const getLanguageForHighlighter = (language: LanguageOption): string => {
        const languageMap: { [key in LanguageOption]: string } = {
            'Curl': 'bash',
            'Python (SDK)': 'python',
            'Python (requests)': 'python',
            'Node.js': 'javascript',
            'Java': 'java',
            'C#': 'csharp',
            'Ruby': 'ruby',
            'Go': 'go',
            'PHP': 'php',
            'Rust': 'rust',
            'C': 'c',
            'C++': 'cpp',
            'Dart': 'dart',
            'AutoJS': 'javascript'
        };
        return languageMap[language] || language.toLowerCase();
    };

    const getApiEndpoint = () => {
        switch (apiFormat) {
            case 'responses':
                return '/v1/responses';
            case 'messages':
                return '/v1/messages';
            case 'images/generations':
                return '/v1/images/generations';
            case 'images/edits':
                return '/v1/images/edits';
            default:
                return '/v1/chat/completions';
        }
    };

    const getRequestBody = () => {
        const testContent = isTestMode ? "say 1" : "Hello!";

        switch (apiFormat) {
            case 'responses':
                return {
                    model: selectModel,
                    input: testContent,
                    instructions: "You are a helpful assistant.",
                    ...(isStreamMode && { stream: true })
                };
            case 'messages':
                const messagesBody: any = {
                    model: selectModel,
                    max_tokens: 1024,
                    messages: [
                        { role: "user", content: testContent }
                    ]
                };

                // 添加系统提示
                if (!isTestMode) {
                    messagesBody.system = "You are a helpful assistant.";
                }

                // 添加流式支持
                if (isStreamMode) {
                    messagesBody.stream = true;
                }

                // 添加thinking支持（仅在非测试模式下，需要Claude模型）
                // thinking功能让Claude展示其推理过程，需要至少1024个tokens预算
                if (!isTestMode && selectModel.includes('claude')) {
                    messagesBody.thinking = {
                        type: "enabled",
                        budget_tokens: 10000  // 推理过程的token预算，最少1024
                    };
                }

                // 添加温度参数
                messagesBody.temperature = 0.7;

                // 添加工具示例（仅在非测试模式下）
                // 工具调用允许Claude调用外部函数和API
                if (!isTestMode) {
                    messagesBody.tools = [
                        {
                            name: "get_weather",
                            description: "Get the current weather in a given location",
                            input_schema: {
                                type: "object",
                                properties: {
                                    location: {
                                        type: "string",
                                        description: "The city and state, e.g. San Francisco, CA"
                                    },
                                    unit: {
                                        type: "string",
                                        description: "Unit for the output - one of (celsius, fahrenheit)",
                                        enum: ["celsius", "fahrenheit"]
                                    }
                                },
                                required: ["location"]
                            }
                        }
                    ];
                    // tool_choice控制工具使用策略: auto(自动), any(必须使用), tool(指定工具), none(不使用)
                    messagesBody.tool_choice = { type: "auto" };
                }

                return messagesBody;
            case 'images/generations':
                return {
                    model: selectModel || "dall-e-3",
                    prompt: isTestMode ? "A simple red circle" : "A cute baby sea otter",
                    n: 1,
                    size: "1024x1024"
                };
            case 'images/edits':
                return {
                    model: selectModel || "dall-e-2",
                    prompt: isTestMode ? "Add a red hat" : "Create a lovely gift basket with these items"
                };
            default:
                // OpenAI Chat Completions format
                const userMessage: any = { role: "user" };

                if (hasImageInput && !isTestMode) {
                    // 支持图片输入的消息格式
                    userMessage.content = [
                        {
                            type: "text",
                            text: "What's in this image?"
                        },
                        {
                            type: "image_url",
                            image_url: {
                                url: "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
                                detail: "high"
                            }
                        }
                    ];
                } else {
                    // 普通文本消息格式
                    userMessage.content = testContent;
                }

                return {
                    model: selectModel,
                    stream: isStreamMode,
                    messages: [
                        { role: "system", content: "You are a helpful assistant." },
                        userMessage
                    ]
                };
        }
    };

    const codeExamples = useMemo(() => {
        const endpoint = getApiEndpoint();
        const requestBody = getRequestBody();

        return {
        'Curl': apiFormat === 'images/edits' ?
            `curl ${serverAddress}${endpoint} \\
  -H "Authorization: Bearer sk-${apiToken}" \\
  -F "model=${requestBody.model}" \\
  -F "image=@image.png" \\
  -F "prompt=${requestBody.prompt}"` :
            apiFormat === 'messages' ?
            `curl ${serverAddress}${endpoint} \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer sk-${apiToken}" \\
  -H "anthropic-version: 2023-06-01" \\
  -d '${JSON.stringify(requestBody, null, 2).replace(/\n/g, '\n  ')}'

# 带thinking功能的示例（仅支持Claude模型）
curl ${serverAddress}${endpoint} \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer sk-${apiToken}" \\
  -H "anthropic-version: 2023-06-01" \\
  -d '{
    "model": "${selectModel}",
    "max_tokens": 16000,
    "stream": ${isStreamMode},
    "thinking": {
      "type": "enabled",
      "budget_tokens": 10000
    },
    "messages": [
      {
        "role": "user",
        "content": "What is 27 * 453?"
      }
    ]
  }'

# 带工具调用的示例
curl ${serverAddress}${endpoint} \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer sk-${apiToken}" \\
  -H "anthropic-version: 2023-06-01" \\
  -d '{
    "model": "${selectModel}",
    "max_tokens": 1024,
    "system": "You are a helpful assistant with access to weather information.",
    "messages": [
      {
        "role": "user",
        "content": "What'\''s the weather like in San Francisco?"
      }
    ],
    "tools": [
      {
        "name": "get_weather",
        "description": "Get the current weather in a given location",
        "input_schema": {
          "type": "object",
          "properties": {
            "location": {
              "type": "string",
              "description": "The city and state, e.g. San Francisco, CA"
            },
            "unit": {
              "type": "string",
              "description": "Unit for the output - one of (celsius, fahrenheit)",
              "enum": ["celsius", "fahrenheit"]
            }
          },
          "required": ["location"]
        }
      }
    ],
    "tool_choice": {"type": "auto"}
  }'` :
            `curl ${serverAddress}${endpoint} \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer sk-${apiToken}" \\
  -d '${JSON.stringify(requestBody, null, 2).replace(/\n/g, '\n  ')}'${hasImageInput && !isTestMode ? `

# 图片输入示例 - 使用base64编码的本地图片
curl ${serverAddress}${endpoint} \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer sk-${apiToken}" \\
  -d '{
    "model": "${selectModel}",
    "stream": ${isStreamMode},
    "messages": [
      {"role": "system", "content": "You are a helpful assistant."},
      {
        "role": "user",
        "content": [
          {
            "type": "text",
            "text": "What'\''s in this image?"
          },
          {
            "type": "image_url",
            "image_url": {
              "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Wq1BjjjMjRiGVZEO4eQehHcH2lAi+0F3AAAAABJRU5ErkJggg==",
              "detail": "high"
            }
          }
        ]
      }
    ]
  }'` : ''}`,
        'Python (SDK)': apiFormat === 'messages' ?
            `import anthropic

client = anthropic.Anthropic(
    api_key="sk-${apiToken}",
    base_url="${serverAddress}"
)

${isStreamMode ?
`# 流式请求
with client.messages.stream(
    model="${selectModel}",
    max_tokens=1024,
    system="You are a helpful assistant.",
    messages=[
        {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
    ]
) as stream:
    for text in stream.text_stream:
        print(text, end="", flush=True)` :
`# 非流式请求
message = client.messages.create(
    model="${selectModel}",
    max_tokens=1024,
    system="You are a helpful assistant.",
    messages=[
        {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
    ]
)
print(message.content[0].text)`}

# 带thinking功能的示例（仅支持Claude模型）
message = client.messages.create(
    model="${selectModel}",
    max_tokens=16000,
    thinking={
        "type": "enabled",
        "budget_tokens": 10000
    },
    messages=[
        {"role": "user", "content": "What is 27 * 453?"}
    ]
)

# 打印thinking过程
if hasattr(message.content[0], 'thinking'):
    print("Thinking process:")
    print(message.content[0].thinking)
    print("\\nFinal answer:")

print(message.content[0].text)

# 带工具调用的示例
tools = [
    {
        "name": "get_weather",
        "description": "Get the current weather in a given location",
        "input_schema": {
            "type": "object",
            "properties": {
                "location": {
                    "type": "string",
                    "description": "The city and state, e.g. San Francisco, CA"
                },
                "unit": {
                    "type": "string",
                    "description": "Unit for the output - one of (celsius, fahrenheit)",
                    "enum": ["celsius", "fahrenheit"]
                }
            },
            "required": ["location"]
        }
    }
]

message = client.messages.create(
    model="${selectModel}",
    max_tokens=1024,
    system="You are a helpful assistant with access to weather information.",
    messages=[
        {"role": "user", "content": "What's the weather like in San Francisco?"}
    ],
    tools=tools,
    tool_choice={"type": "auto"}
)

# 处理工具调用
for content_block in message.content:
    if content_block.type == "tool_use":
        print(f"Tool used: {content_block.name}")
        print(f"Tool input: {content_block.input}")
        # 这里你需要实际调用工具并返回结果
    elif content_block.type == "text":
        print(f"Response: {content_block.text}")` :
            apiFormat === 'responses' ?
            `from openai import OpenAI

client = OpenAI(api_key="sk-${apiToken}", base_url="${serverAddress}/v1")

${isStreamMode ?
`# 流式请求
response = client.responses.create(
    model="${selectModel}",
    input="${isTestMode ? "say 1" : "Hello!"}",
    instructions="You are a helpful assistant.",
    stream=True
)

for chunk in response:
    if hasattr(chunk, 'content') and chunk.content:
        print(chunk.content, end="", flush=True)` :
`# 非流式请求
response = client.responses.create(
    model="${selectModel}",
    input="${isTestMode ? "say 1" : "Hello!"}",
    instructions="You are a helpful assistant."
)

print(response.content)`}` :
            apiFormat === 'images/generations' ?
            `from openai import OpenAI

client = OpenAI(api_key="sk-${apiToken}", base_url="${serverAddress}/v1")

# 图片生成
response = client.images.generate(
    model="${selectModel || "dall-e-3"}",
    prompt="${isTestMode ? "A simple red circle" : "A cute baby sea otter"}",
    n=1,
    size="1024x1024"
)

# 获取生成的图片
image_url = response.data[0].url
print(f"Generated image URL: {image_url}")

# 如果返回base64格式
if hasattr(response.data[0], 'b64_json'):
    import base64
    image_data = base64.b64decode(response.data[0].b64_json)
    with open("generated_image.png", "wb") as f:
        f.write(image_data)
    print("Image saved as generated_image.png")` :
            apiFormat === 'images/edits' ?
            `from openai import OpenAI

client = OpenAI(api_key="sk-${apiToken}", base_url="${serverAddress}/v1")

# 图片编辑
with open("image.png", "rb") as image_file:
    response = client.images.edit(
        model="${selectModel || "dall-e-2"}",
        image=image_file,
        prompt="${isTestMode ? "Add a red hat" : "Create a lovely gift basket with these items"}",
        n=1,
        size="1024x1024"
    )

# 获取编辑后的图片
image_url = response.data[0].url
print(f"Edited image URL: {image_url}")

# 如果返回base64格式
if hasattr(response.data[0], 'b64_json'):
    import base64
    image_data = base64.b64decode(response.data[0].b64_json)
    with open("edited_image.png", "wb") as f:
        f.write(image_data)
    print("Edited image saved as edited_image.png")` :
            `from openai import OpenAI

client = OpenAI(api_key="sk-${apiToken}", base_url="${serverAddress}/v1")

completion = client.chat.completions.create(
    model="${selectModel}",
    stream=${isStreamMode ? 'True' : 'False'},
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        ${hasImageInput && !isTestMode ?
        `{
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "What's in this image?"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
                        "detail": "high"
                    }
                }
            ]
        }` :
        `{"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}`}
    ]
)

${isStreamMode ?
            `for chunk in completion:
    print(chunk.choices[0].delta)` :
            `print(completion.choices[0].message)`}

${hasImageInput && !isTestMode ? `
# 图片输入示例 - 使用base64编码
import base64

def encode_image(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode('utf-8')

# 使用本地图片
base64_image = encode_image("path/to/your/image.jpg")

completion_with_base64 = client.chat.completions.create(
    model="${selectModel}",
    messages=[
        {"role": "system", "content": "You are a helpful assistant."},
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": "What's in this image?"
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}",
                        "detail": "high"
                    }
                }
            ]
        }
    ]
)

print(completion_with_base64.choices[0].message.content)` : ''}`,
        'Python (requests)': apiFormat === 'images/edits' ?
            `import requests

url = "${serverAddress}${getApiEndpoint()}"
headers = {
    "Authorization": f"Bearer sk-${apiToken}"
}

# 图片编辑需要使用multipart/form-data
files = {
    'model': (None, '${getRequestBody().model}'),
    'prompt': (None, '${getRequestBody().prompt}'),
    'image': ('image.png', open('image.png', 'rb'), 'image/png')
}

response = requests.post(url, headers=headers, files=files)

if response.status_code == 200:
    # 处理图片编辑响应
    result = response.json()
    if result.get('data') and len(result['data']) > 0:
        image_data = result['data'][0]
        if 'url' in image_data:
            print(f"Edited image URL: {image_data['url']}")
        elif 'b64_json' in image_data:
            import base64
            image_bytes = base64.b64decode(image_data['b64_json'])
            with open('edited_image.png', 'wb') as f:
                f.write(image_bytes)
            print("Edited image saved as edited_image.png")

        # 显示使用情况
        if 'usage' in result:
            usage = result['usage']
            print(f"Tokens used: {usage.get('total_tokens', 'N/A')}")
    else:
        print("No image data received")
else:
    print(f"Error: {response.status_code}, {response.text}")` :
            `import requests
import json

url = "${serverAddress}${getApiEndpoint()}"
headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer sk-${apiToken}"${apiFormat === 'messages' ? ',\n    "anthropic-version": "2023-06-01"' : ''}
}
data = ${JSON.stringify(getRequestBody(), null, 4).replace(/\n/g, '\n')}

response = requests.post(url, headers=headers, json=data)

if response.status_code == 200:
    ${((apiFormat as ApiFormat) === 'images/generations' || (apiFormat as ApiFormat) === 'images/edits') ?
    `# 处理图片API响应
    result = response.json()
    if result.get('data') and len(result['data']) > 0:
        image_data = result['data'][0]
        if 'url' in image_data:
            print(f"Image URL: {image_data['url']}")
        elif 'b64_json' in image_data:
            import base64
            image_bytes = base64.b64decode(image_data['b64_json'])
            with open('generated_image.png', 'wb') as f:
                f.write(image_bytes)
            print("Image saved as generated_image.png")

        # 显示使用情况
        if 'usage' in result:
            usage = result['usage']
            print(f"Tokens used: {usage.get('total_tokens', 'N/A')}")
    else:
        print("No image data received")` :
    isStreamMode ?
    `# 处理流式响应
    for line in response.iter_lines():
        if line:
            line_text = line.decode('utf-8')
            if line_text.startswith('data: '):
                data_str = line_text[6:]  # 去掉'data: '前缀
                if data_str.strip() == '[DONE]':
                    break
                try:
                    chunk = json.loads(data_str)
                    ${apiFormat === 'messages' ?
                        `# Claude Messages API 流式响应处理
                    if chunk.get('type') == 'content_block_delta':
                        if chunk.get('delta', {}).get('text'):
                            print(chunk['delta']['text'], end='')
                    elif chunk.get('type') == 'content_block_start':
                        if chunk.get('content_block', {}).get('type') == 'thinking':
                            print("\\n[Thinking process started]\\n")
                    elif chunk.get('type') == 'content_block_stop':
                        if chunk.get('content_block', {}).get('type') == 'thinking':
                            print("\\n[Thinking process ended]\\n")` :
                        apiFormat === 'responses' ?
                        `if chunk.get('content'):
                        print(chunk['content'], end='')` :
                        `if chunk['choices'][0].get('delta', {}).get('content'):
                        print(chunk['choices'][0]['delta']['content'], end='')`}
                except json.JSONDecodeError:
                    pass`
    :
    `# 处理非流式响应
    ${apiFormat === 'messages' ?
        `result = response.json()

    # 处理thinking内容
    for content_block in result.get('content', []):
        if content_block.get('type') == 'thinking':
            print("Thinking process:")
            print(content_block.get('thinking', ''))
            print("\\nFinal answer:")
        elif content_block.get('type') == 'text':
            print(content_block.get('text', ''))
        elif content_block.get('type') == 'tool_use':
            print(f"Tool used: {content_block.get('name')}")
            print(f"Tool input: {content_block.get('input')}")

    # 显示使用情况
    if result.get('usage'):
        usage = result['usage']
        print(f"\\nTokens used - Input: {usage.get('input_tokens')}, Output: {usage.get('output_tokens')}")
        if usage.get('cache_read_input_tokens'):
            print(f"Cache read tokens: {usage.get('cache_read_input_tokens')}")
        if usage.get('cache_creation_input_tokens'):
            print(f"Cache creation tokens: {usage.get('cache_creation_input_tokens')}")` :
        apiFormat === 'responses' ?
        `print(response.json()['content'])` :
        `print(response.json()['choices'][0]['message']['content'])`}`}
else:
    print(f"Error: {response.status_code}, {response.text}")`,
        'Node.js': apiFormat === 'messages' ?
            `import Anthropic from '@anthropic-ai/sdk';

const anthropic = new Anthropic({
    apiKey: 'sk-${apiToken}',
    baseURL: '${serverAddress}'
});

async function main() {
    ${isStreamMode ?
    `// 流式请求
    const stream = await anthropic.messages.stream({
        model: '${selectModel}',
        max_tokens: 1024,
        system: 'You are a helpful assistant.',
        messages: [
            { role: 'user', content: '${isTestMode ? "say 1" : "Hello!"}' }
        ]
    });

    for await (const chunk of stream) {
        if (chunk.type === 'content_block_delta') {
            process.stdout.write(chunk.delta.text);
        }
    }` :
    `// 非流式请求
    const message = await anthropic.messages.create({
        model: '${selectModel}',
        max_tokens: 1024,
        system: 'You are a helpful assistant.',
        messages: [
            { role: 'user', content: '${isTestMode ? "say 1" : "Hello!"}' }
        ]
    });

    console.log(message.content[0].text);`}
}

// 带thinking功能的示例（仅支持Claude模型）
async function thinkingExample() {
    const message = await anthropic.messages.create({
        model: '${selectModel}',
        max_tokens: 16000,
        thinking: {
            type: 'enabled',
            budget_tokens: 10000
        },
        messages: [
            { role: 'user', content: 'What is 27 * 453?' }
        ]
    });

    // 打印thinking过程
    if (message.content[0].thinking) {
        console.log('Thinking process:');
        console.log(message.content[0].thinking);
        console.log('\\nFinal answer:');
    }

    console.log(message.content[0].text);
}

// 带工具调用的示例
async function toolExample() {
    const tools = [
        {
            name: 'get_weather',
            description: 'Get the current weather in a given location',
            input_schema: {
                type: 'object',
                properties: {
                    location: {
                        type: 'string',
                        description: 'The city and state, e.g. San Francisco, CA'
                    },
                    unit: {
                        type: 'string',
                        description: 'Unit for the output - one of (celsius, fahrenheit)',
                        enum: ['celsius', 'fahrenheit']
                    }
                },
                required: ['location']
            }
        }
    ];

    const message = await anthropic.messages.create({
        model: '${selectModel}',
        max_tokens: 1024,
        system: 'You are a helpful assistant with access to weather information.',
        messages: [
            { role: 'user', content: "What's the weather like in San Francisco?" }
        ],
        tools: tools,
        tool_choice: { type: 'auto' }
    });

    // 处理工具调用
    for (const contentBlock of message.content) {
        if (contentBlock.type === 'tool_use') {
            console.log(\`Tool used: \${contentBlock.name}\`);
            console.log(\`Tool input: \${JSON.stringify(contentBlock.input)}\`);
            // 这里你需要实际调用工具并返回结果
        } else if (contentBlock.type === 'text') {
            console.log(\`Response: \${contentBlock.text}\`);
        }
    }
}

main();
// thinkingExample();
// toolExample();` :
            apiFormat === 'responses' ?
            `import OpenAI from "openai";

const openai = new OpenAI({
    apiKey: "sk-${apiToken}",
    baseURL: "${serverAddress}/v1"
});

async function main() {
    ${isStreamMode ?
    `// 流式请求
    const response = await openai.responses.create({
        model: "${selectModel}",
        input: "${isTestMode ? "say 1" : "Hello!"}",
        instructions: "You are a helpful assistant.",
        stream: true
    });

    for await (const chunk of response) {
        if (chunk.content) {
            process.stdout.write(chunk.content);
        }
    }` :
    `// 非流式请求
    const response = await openai.responses.create({
        model: "${selectModel}",
        input: "${isTestMode ? "say 1" : "Hello!"}",
        instructions: "You are a helpful assistant."
    });

    console.log(response.content);`}
}

main();` :
            `import OpenAI from "openai";
${hasImageInput && !isTestMode ? 'import fs from "fs";' : ''}

const openai = new OpenAI({
    apiKey: "sk-${apiToken}",
    baseURL: "${serverAddress}/v1"
});

async function main() {
    const completion = await openai.chat.completions.create({
        model: "${selectModel}",
        stream: ${isStreamMode},
        messages: [
            { role: "system", content: "You are a helpful assistant." },
            ${hasImageInput && !isTestMode ?
            `{
                role: "user",
                content: [
                    {
                        type: "text",
                        text: "What's in this image?"
                    },
                    {
                        type: "image_url",
                        image_url: {
                            url: "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
                            detail: "high"
                        }
                    }
                ]
            }` :
            `{ role: "user", content: "${isTestMode ? "say 1" : "Hello!"}" }`}
        ]
    });

    ${isStreamMode ?
            `for await (const chunk of completion) {
        console.log(chunk.choices[0].delta);
    }` :
            `console.log(completion.choices[0].message);`}
}

${hasImageInput && !isTestMode ? `
// 图片输入示例 - 使用base64编码的本地图片
async function imageExample() {
    // 读取本地图片并转换为base64
    const imageBuffer = fs.readFileSync("path/to/your/image.jpg");
    const base64Image = imageBuffer.toString('base64');

    const completion = await openai.chat.completions.create({
        model: "${selectModel}",
        messages: [
            { role: "system", content: "You are a helpful assistant." },
            {
                role: "user",
                content: [
                    {
                        type: "text",
                        text: "What's in this image?"
                    },
                    {
                        type: "image_url",
                        image_url: {
                            url: \`data:image/jpeg;base64,\${base64Image}\`,
                            detail: "high"
                        }
                    }
                ]
            }
        ]
    });

    console.log(completion.choices[0].message.content);
}

// imageExample();` : ''}
main();`,
        'Java': `import com.theokanning.openai.completion.chat.ChatCompletionRequest;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.service.OpenAiService;
import com.theokanning.openai.client.OpenAiApi;

import retrofit2.Retrofit;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.time.Duration;
import java.util.Arrays;${hasImageInput && !isTestMode ? '\nimport java.util.List;\nimport java.util.Map;\nimport java.util.HashMap;\nimport java.io.IOException;\nimport java.nio.file.Files;\nimport java.nio.file.Paths;\nimport java.util.Base64;\nimport okhttp3.*;\nimport com.fasterxml.jackson.databind.ObjectMapper;' : ''}

public class OpenAIExample {
    public static void main(String[] args) {
        String token = "sk-${apiToken}";
        String baseUrl = "${serverAddress}/v1/";

        ${hasImageInput && !isTestMode ? `// 图片输入需要使用原始HTTP请求，因为当前的OpenAI Java库可能不支持多模态
        try {
            // 使用OkHttp直接发送请求
            OkHttpClient client = new OkHttpClient();
            ObjectMapper mapper = new ObjectMapper();

            // 构建包含图片的消息
            Map<String, Object> imageMessage = new HashMap<>();
            imageMessage.put("role", "user");

            Map<String, Object> textContent = new HashMap<>();
            textContent.put("type", "text");
            textContent.put("text", "What's in this image?");

            Map<String, Object> imageContent = new HashMap<>();
            imageContent.put("type", "image_url");
            Map<String, Object> imageUrl = new HashMap<>();
            imageUrl.put("url", "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg");
            imageUrl.put("detail", "high");
            imageContent.put("image_url", imageUrl);

            imageMessage.put("content", Arrays.asList(textContent, imageContent));

            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", "${selectModel}");
            requestBody.put("messages", Arrays.asList(
                Map.of("role", "system", "content", "You are a helpful assistant."),
                imageMessage
            ));

            String json = mapper.writeValueAsString(requestBody);

            RequestBody body = RequestBody.create(json, MediaType.get("application/json; charset=utf-8"));
            Request request = new Request.Builder()
                .url(baseUrl + "chat/completions")
                .header("Authorization", "Bearer " + token)
                .header("Content-Type", "application/json")
                .post(body)
                .build();

            Response response = client.newCall(request).execute();
            System.out.println("Image response: " + response.body().string());

        } catch (Exception e) {
            e.printStackTrace();
        }

        // 使用base64编码的本地图片示例
        try {
            byte[] imageBytes = Files.readAllBytes(Paths.get("path/to/your/image.jpg"));
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            // 构建请求... (类似上面的代码，但使用 "data:image/jpeg;base64," + base64Image)
            System.out.println("Base64 image can be used with: data:image/jpeg;base64," + base64Image.substring(0, 50) + "...");
        } catch (IOException e) {
            System.out.println("Error reading image file: " + e.getMessage());
        }` : `ObjectMapper mapper = OpenAiService.defaultObjectMapper();
        Retrofit retrofit = new Retrofit.Builder()
            .baseUrl(baseUrl)
            .client(OpenAiService.defaultClient(token, Duration.ofSeconds(30)))
            .addConverterFactory(JacksonConverterFactory.create(mapper))
            .build();
        OpenAiApi api = retrofit.create(OpenAiApi.class);
        OpenAiService service = new OpenAiService(api);

        ChatCompletionRequest completionRequest = ChatCompletionRequest.builder()
            .model("${selectModel}")
            .messages(Arrays.asList(
                new ChatMessage("system", "You are a helpful assistant."),
                new ChatMessage("user", "${isTestMode ? "say 1" : "Hello!"}")
            ))
            .build();

        service.createChatCompletion(completionRequest).getChoices().forEach(choice -> {
            System.out.println(choice.getMessage().getContent());
        });`}
    }
}`,
        'C#': `using OpenAI_API;
using OpenAI_API.Chat;${hasImageInput && !isTestMode ? '\nusing System.Net.Http;\nusing System.Text;\nusing Newtonsoft.Json;' : ''}

class Program
{
    static async Task Main(string[] args)
    {
        var api = new OpenAIAPI("sk-${apiToken}");
        api.ApiBase = "${serverAddress}/v1";

        ${hasImageInput && !isTestMode ? `// 图片输入需要使用HttpClient直接发送请求
        // 因为当前的OpenAI_API库可能不支持多模态输入
        using (var httpClient = new HttpClient())
        {
            httpClient.DefaultRequestHeaders.Add("Authorization", "Bearer sk-${apiToken}");

            var requestBody = new
            {
                model = "${selectModel}",
                messages = new[]
                {
                    new { role = "system", content = "You are a helpful assistant." },
                    new
                    {
                        role = "user",
                        content = new object[]
                        {
                            new { type = "text", text = "What's in this image?" },
                            new
                            {
                                type = "image_url",
                                image_url = new
                                {
                                    url = "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
                                    detail = "high"
                                }
                            }
                        }
                    }
                }
            };

            var json = JsonConvert.SerializeObject(requestBody);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await httpClient.PostAsync("${serverAddress}/v1/chat/completions", content);
            var responseString = await response.Content.ReadAsStringAsync();
            Console.WriteLine("Image response: " + responseString);
        }

        // 使用base64编码的本地图片示例
        // string base64Image = Convert.ToBase64String(File.ReadAllBytes("path/to/your/image.jpg"));
        // 然后在image_url中使用: "data:image/jpeg;base64," + base64Image` : `var chat = api.Chat.CreateConversation();
        chat.AppendSystemMessage("You are a helpful assistant.");
        chat.AppendUserInput("${isTestMode ? "say 1" : "Hello!"}");

        string response = await chat.GetResponseFromChatbotAsync();
        Console.WriteLine(response);`}
    }
}`,
        'Ruby': `require 'openai'

client = OpenAI::Client.new(access_token: 'sk-${apiToken}', uri_base: '${serverAddress}/v1')

response = client.chat(
    parameters: {
        model: "${selectModel}",
        messages: [
            { role: "system", content: "You are a helpful assistant." },
            { role: "user", content: "${isTestMode ? "say 1" : "Hello!"}" }
        ],
        stream: ${isStreamMode}
    }
)

${isStreamMode ?
            `response.each do |chunk|
  print chunk.dig("choices", 0, "delta", "content")
end` :
            `puts response.dig("choices", 0, "message", "content")`}`,
        'Go': `package main

import (
    "context"
    "fmt"
    "github.com/sashabaranov/go-openai"${hasImageInput && !isTestMode ? '\n    "encoding/base64"\n    "io/ioutil"' : ''}
)

func main() {
    client := openai.NewClient("sk-${apiToken}")
    client.BaseURL = "${serverAddress}/v1"

    ${hasImageInput && !isTestMode ? `// 图片输入示例
    messages := []openai.ChatCompletionMessage{
        {
            Role:    openai.ChatMessageRoleSystem,
            Content: "You are a helpful assistant.",
        },
        {
            Role: openai.ChatMessageRoleUser,
            MultiContent: []openai.ChatMessagePart{
                {
                    Type: openai.ChatMessagePartTypeText,
                    Text: "What's in this image?",
                },
                {
                    Type: openai.ChatMessagePartTypeImageURL,
                    ImageURL: &openai.ChatMessageImageURL{
                        URL:    "https://upload.wikimedia.org/wikipedia/commons/thumb/d/dd/Gfp-wisconsin-madison-the-nature-boardwalk.jpg/2560px-Gfp-wisconsin-madison-the-nature-boardwalk.jpg",
                        Detail: openai.ImageURLDetailHigh,
                    },
                },
            },
        },
    }` : `messages := []openai.ChatCompletionMessage{
        {
            Role:    openai.ChatMessageRoleSystem,
            Content: "You are a helpful assistant.",
        },
        {
            Role:    openai.ChatMessageRoleUser,
            Content: "${isTestMode ? "say 1" : "Hello!"}",
        },
    }`}

    resp, err := client.CreateChatCompletion(
        context.Background(),
        openai.ChatCompletionRequest{
            Model:    "${selectModel}",
            Messages: messages,
        },
    )

    if err != nil {
        fmt.Printf("ChatCompletion error: %v\\n", err)
        return
    }

    fmt.Println(resp.Choices[0].Message.Content)${hasImageInput && !isTestMode ? `

    // 使用base64编码的本地图片示例
    imageData, err := ioutil.ReadFile("path/to/your/image.jpg")
    if err != nil {
        fmt.Printf("Error reading image: %v\\n", err)
        return
    }

    base64Image := base64.StdEncoding.EncodeToString(imageData)

    messagesWithBase64 := []openai.ChatCompletionMessage{
        {
            Role:    openai.ChatMessageRoleSystem,
            Content: "You are a helpful assistant.",
        },
        {
            Role: openai.ChatMessageRoleUser,
            MultiContent: []openai.ChatMessagePart{
                {
                    Type: openai.ChatMessagePartTypeText,
                    Text: "What's in this image?",
                },
                {
                    Type: openai.ChatMessagePartTypeImageURL,
                    ImageURL: &openai.ChatMessageImageURL{
                        URL:    "data:image/jpeg;base64," + base64Image,
                        Detail: openai.ImageURLDetailHigh,
                    },
                },
            },
        },
    }

    respBase64, err := client.CreateChatCompletion(
        context.Background(),
        openai.ChatCompletionRequest{
            Model:    "${selectModel}",
            Messages: messagesWithBase64,
        },
    )

    if err != nil {
        fmt.Printf("ChatCompletion error: %v\\n", err)
        return
    }

    fmt.Println("Base64 image response:", respBase64.Choices[0].Message.Content)` : ''}
}`,
        'PHP': `<?php

require_once 'vendor/autoload.php';

$client = OpenAI::client('sk-${apiToken}');

$result = $client->chat()->create([
    'model' => '${selectModel}',
    'messages' => [
        ['role' => 'system', 'content' => 'You are a helpful assistant.'],
        ['role' => 'user', 'content' => '${isTestMode ? "say 1" : "Hello!"}'],
    ],
]);

echo $result->choices[0]->message->content;`,
        'Rust': `use reqwest::Client;
use serde_json::{json, Value};

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    let client = Client::new();
    let response = client.post("${serverAddress}/v1/chat/completions")
        .header("Authorization", "Bearer sk-${apiToken}")
        .json(&json!({
            "model": "${selectModel}",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
            ]
        }))
        .send()
        .await?;

    let body: Value = response.json().await?;
    println!("{}", body["choices"][0]["message"]["content"]);

    Ok(())
}`,
        'C': `#include <stdio.h>
#include <curl/curl.h>
#include <string.h>

size_t write_callback(char *ptr, size_t size, size_t nmemb, void *userdata) {
    printf("%.*s", (int)(size * nmemb), ptr);
    return size * nmemb;
}

int main(void) {
    CURL *curl;
    CURLcode res;
    struct curl_slist *headers = NULL;
    char *data = "{"
        "\"model\": \"${selectModel}\","
        "\"messages\": ["
            "{\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},"
            "{\"role\": \"user\", \"content\": \"${isTestMode ? "say 1" : "Hello!"}\"}"
        "]"
    "}";

    curl = curl_easy_init();
    if(curl) {
        headers = curl_slist_append(headers, "Content-Type: application/json");
        headers = curl_slist_append(headers, "Authorization: Bearer sk-${apiToken}");

        curl_easy_setopt(curl, CURLOPT_URL, "${serverAddress}/v1/chat/completions");
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data);
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, write_callback);

        res = curl_easy_perform(curl);
        if(res != CURLE_OK)
            fprintf(stderr, "curl_easy_perform() failed: %s\\n", curl_easy_strerror(res));

        curl_easy_cleanup(curl);
        curl_slist_free_all(headers);
    }
    return 0;
}`,
        'C++': `#include <iostream>
#include <curl/curl.h>
#include <string>

size_t WriteCallback(void *contents, size_t size, size_t nmemb, std::string *s) {
    size_t newLength = size * nmemb;
    s->append((char*)contents, newLength);
    return newLength;
}

int main() {
    CURL *curl;
    CURLcode res;
    std::string readBuffer;

    curl = curl_easy_init();
    if(curl) {
        std::string url = "${serverAddress}/v1/chat/completions";
        std::string data = R"({
            "model": "${selectModel}",
            "messages": [
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "${isTestMode ? "say 1" : "Hello!"}"}
            ]
        })";

        struct curl_slist *headers = NULL;
        headers = curl_slist_append(headers, "Content-Type: application/json");
        headers = curl_slist_append(headers, "Authorization: Bearer sk-${apiToken}");

        curl_easy_setopt(curl, CURLOPT_URL, url.c_str());
        curl_easy_setopt(curl, CURLOPT_POSTFIELDS, data.c_str());
        curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
        curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
        curl_easy_setopt(curl, CURLOPT_WRITEDATA, &readBuffer);

        res = curl_easy_perform(curl);
        curl_easy_cleanup(curl);

        if(res != CURLE_OK)
            std::cerr << "curl_easy_perform() failed: " << curl_easy_strerror(res) << std::endl;
        else
            std::cout << readBuffer << std::endl;
    }

    return 0;
}`,
        'Dart': `import 'package:http/http.dart' as http;
import 'dart:convert';

void main() async {
  final url = Uri.parse('${serverAddress}/v1/chat/completions');
  final response = await http.post(
    url,
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer sk-${apiToken}',
    },
    body: jsonEncode({
      'model': '${selectModel}',
      'messages': [
        {'role': 'system', 'content': 'You are a helpful assistant.'},
        {'role': 'user', 'content': '${isTestMode ? "say 1" : "Hello!"}'},
      ],
    }),
  );

  if (response.statusCode == 200) {
    final jsonResponse = jsonDecode(response.body);
    print(jsonResponse['choices'][0]['message']['content']);
  } else {
    print('Request failed with status: \${response.statusCode}.');
  }
}`,
        'AutoJS': `// AutoJS 示例代码 - ${apiFormat === 'messages' ? 'Anthropic Messages' : apiFormat === 'responses' ? 'OpenAI Responses' : apiFormat === 'images/generations' ? 'OpenAI Images' : apiFormat === 'images/edits' ? 'OpenAI Image Edit' : 'OpenAI Chat'}
// 需要先安装 http 模块
let http = require('http');

// 构建请求数据
let requestData = ${JSON.stringify(getRequestBody(), null, 4).replace(/\n/g, '\n')};

// 设置请求头
let headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer sk-${apiToken}"${apiFormat === 'messages' ? ',\n    "anthropic-version": "2023-06-01"' : ''}
};

${apiFormat === 'images/edits' ?
`// 注意：图片编辑需要上传文件，AutoJS可能不支持multipart/form-data
// 建议使用其他编程语言进行图片编辑操作
console.log("图片编辑API需要上传文件，AutoJS可能不支持");
console.log("请参考Python或Node.js示例");

// 如果要尝试，需要实现文件上传功能
let response = null; // 占位符` :
`// 发送 POST 请求
let response = http.postJson("${serverAddress}${getApiEndpoint()}", requestData, {
    headers: headers
});`}

// 处理响应
if (response.statusCode == 200) {
    let result = response.body.json();
    ${isStreamMode ?
    `// 流式响应处理
    console.log("流式响应暂不支持，请使用非流式模式");` :
    `// 非流式响应处理
    ${apiFormat === 'messages' ?
        `if (result.content && result.content.length > 0) {
        console.log("AI回复:", result.content[0].text);
        toast("AI回复: " + result.content[0].text);
    } else {
        console.log("未获取到有效回复");
    }` :
        apiFormat === 'responses' ?
        `if (result.content) {
        console.log("AI回复:", result.content);
        toast("AI回复: " + result.content);
    } else {
        console.log("未获取到有效回复");
    }` :
        (apiFormat === 'images/generations' || apiFormat === 'images/edits') ?
        `if (result.data && result.data.length > 0) {
        let imageData = result.data[0];
        if (imageData.url) {
            console.log("图片URL:", imageData.url);
            toast("图片生成成功，URL: " + imageData.url);
        } else if (imageData.b64_json) {
            console.log("图片base64数据已生成");
            toast("图片生成成功，已获取base64数据");
        }

        // 显示使用情况
        if (result.usage) {
            console.log("Token使用:", result.usage.total_tokens);
        }
    } else {
        console.log("未获取到图片数据");
        toast("图片生成失败");
    }` :
        `if (result.choices && result.choices.length > 0) {
        console.log("AI回复:", result.choices[0].message.content);
        toast("AI回复: " + result.choices[0].message.content);
    } else {
        console.log("未获取到有效回复");
    }`}`}
} else {
    console.log("请求失败，状态码:", response.statusCode);
    console.log("错误信息:", response.body.string());
    toast("请求失败: " + response.statusCode);
}`
        };
    }, [serverAddress, apiToken, selectModel, isStreamMode, isTestMode, apiFormat, hasImageInput]);

    return (
        <Modal
            {...modalProps}
            title="代码示例"
            open={isCodeExampleModalVisible}
            onOk={() => setIsCodeExampleModalVisible(false)}
            onCancel={() => setIsCodeExampleModalVisible(false)}
            width={840}
            centered={true}
            afterClose={() => {
                setCodeExampleLanguage('Curl');
                setIsTestMode(false);
                setIsStreamMode(false);
                setSelectModel('gpt-4o-mini');
                setApiFormat('chat/completions');
                setHasImageInput(false);
            }}
        >
            <Space direction="vertical" size="middle" style={{ display: 'flex' }}>
                <Space wrap>
                    <span style={{ marginRight: 8, fontWeight: 'bold' }}>API格式:</span>
                    <Radio.Group
                        value={apiFormat}
                        onChange={(e) => setApiFormat(e.target.value)}
                        style={{ marginRight: 16 }}
                    >
                        <Radio.Button value="chat/completions">OpenAI Chat</Radio.Button>
                        <Radio.Button value="responses">OpenAI Responses</Radio.Button>
                        <Radio.Button value="messages">Anthropic Messages</Radio.Button>
                        <Radio.Button value="images/generations">OpenAI Images</Radio.Button>
                        <Radio.Button value="images/edits">OpenAI Image Edit</Radio.Button>
                    </Radio.Group>
                </Space>
                <Space wrap>
                    <span style={{ marginRight: 8, fontWeight: 'bold' }}>编程语言:</span>
                    <Radio.Group
                        value={codeExampleLanguage}
                        onChange={(e) => setCodeExampleLanguage(e.target.value)}
                    >
                        <Radio.Button value="Curl">Curl</Radio.Button>
                        <Radio.Button value="Python (SDK)">Python (SDK)</Radio.Button>
                        <Radio.Button value="Python (requests)">Python (requests)</Radio.Button>
                        <Radio.Button value="Node.js">Node.js</Radio.Button>
                        <Radio.Button value="Java">Java</Radio.Button>
                        <Radio.Button value="C#">C#</Radio.Button>
                        <Radio.Button value="Ruby">Ruby</Radio.Button>
                        <Radio.Button value="Go">Go</Radio.Button>
                        <Radio.Button value="PHP">PHP</Radio.Button>
                        <Radio.Button value="Rust">Rust</Radio.Button>
                        <Radio.Button value="C">C</Radio.Button>
                        <Radio.Button value="C++">C++</Radio.Button>
                        <Radio.Button value="Dart">Dart</Radio.Button>
                        <Radio.Button value="AutoJS">AutoJS</Radio.Button>
                    </Radio.Group>
                    <Radio.Group
                        value={isTestMode}
                        onChange={(e) => setIsTestMode(e.target.value)}
                    >
                        <Radio.Button value={false}>正常</Radio.Button>
                        <Radio.Button value={true}>拨测</Radio.Button>
                    </Radio.Group>
                    <Radio.Group
                        value={isStreamMode}
                        onChange={(e) => setIsStreamMode(e.target.value)}
                    >
                        <Radio.Button value={false}>非流</Radio.Button>
                        <Radio.Button value={true}>流式</Radio.Button>
                    </Radio.Group>
                    {apiFormat === 'chat/completions' && (
                        <Space>
                            <span>图片输入:</span>
                            <Switch
                                checked={hasImageInput}
                                onChange={setHasImageInput}
                                disabled={isTestMode}
                                size="small"
                            />
                        </Space>
                    )}
                    <AutoComplete
                        value={selectModel}
                        options={modelOptions}
                        style={{width: 200}}
                        onSelect={(value: string) => setSelectModel(value)}
                        onChange={(value: string) => setSelectModel(value)}
                        placeholder="选择或输入模型名称"
                        filterOption={(inputValue, option) => {
                            if (!option?.value) return false;
                            const optionValue = String(option.value).toLowerCase();
                            const input = inputValue.toLowerCase();
                            return optionValue.includes(input);
                        }}
                    />
                </Space>
                <SyntaxHighlighter
                    language={getLanguageForHighlighter(codeExampleLanguage)}
                    style={xonokai}
                    wrapLines={true}
                    wrapLongLines={true}
                >
                    {codeExamples[codeExampleLanguage]}
                </SyntaxHighlighter>
                <HappyProvider>
                    <Button
                        block={true}
                        type="primary"
                        onClick={async () => {
                            try {
                                await copy(codeExamples[codeExampleLanguage]);
                                AntdMessage.success({content: '复制成功', duration: 1, key: 'copy'});
                            } catch {
                                AntdMessage.warning({content: '复制失败', duration: 1, key: 'copy'});
                            }
                        }}
                    >
                        复制到剪贴板
                    </Button>
                </HappyProvider>
            </Space>
        </Modal>
    );
};

export default CodeExampleModal;
