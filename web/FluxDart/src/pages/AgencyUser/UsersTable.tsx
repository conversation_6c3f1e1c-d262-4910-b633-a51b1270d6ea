import React, {useContext, useState} from 'react'
import {API, isMobile, safeParseJSON, showError} from '../../helpers'
import {
    createRenderFunctions,
    renderNumber,
    renderQuotaWithUsage,
    renderUserGroupTag,
    renderUserRole,
    renderUserStatus
} from '../../helpers/render'
import {App, Button, Input, Popconfirm, Popover, Select, Space, Tag} from 'antd'
import {
    CrownFilled,
    DeleteTwoTone,
    EditTwoTone,
    FileTextTwoTone,
    PlayCircleTwoTone,
    StopTwoTone,
} from '@ant-design/icons'
import {useNavigate} from 'react-router-dom'
import {ParamsType, ProTable} from '@ant-design/pro-components'
import moment from 'moment/moment'
import {actionButtonProps, DefaultResponse, paginationProps} from '../../constants'
import {getCountByPathAndParam, getDataByPathAndParam} from "../../helpers/api-request-module";
import {StatusContext} from "../../context/Status";
import {UserContext} from "../../context/User";
import {useTranslation} from "react-i18next";

const EditUserModal = React.lazy(() => import('./EditUserModal'));
const AddUserModal = React.lazy(() => import('./AddUserModal'));

const UsersTable = () => {
    const {t} = useTranslation();
    const { renderModels, renderQuota,renderRoleTag,renderQuotaExpireTime, renderChannelStatusTag } = createRenderFunctions(t);

    const [statusState] = useContext(StatusContext)
    const [userState] = useContext(UserContext)
    const navigate = useNavigate();
    const {message: AntdMessage, modal} = App.useApp();
    const ref = React.useRef({
        reload: function () {
            console.log("reload table");
        },
    });
    const {Option} = Select;

    // 表格
    const [userCount, setUserCount] = useState(0);
    const userGroups = Object.keys(safeParseJSON(localStorage.getItem('GroupRatio') || '{}', {}));//用户分组

    // 表格操作
    const [editingId, setEditingId] = useState(null);//当前编辑的备注的用户 ID
    const [editingRemark, setEditingRemark] = useState('');//当前编辑的备注
    const [editingOriginalRemark, setEditingOriginalRemark] = useState('');//当前编辑的备注的用户原始备注

    // 模态框
    const [editingUserIdForInfo, setEditingUserIdForInfo] = useState(0);//当前编辑的用户 ID，用于模态框（编辑用户信息）
    const [isAddUserModalVisible, setIsAddUserModalVisible] = useState(false);
    const [isUserEditModalVisible, setIsUserEditModalVisible] = useState(false);

    const request = async (params: ParamsType) => {
        try {
            const [res, count] = await Promise.all([
                getDataByPathAndParam('user/agency', params),
                getCountByPathAndParam('user/agency', params)
            ]);
            const {success, data, message} = res as DefaultResponse;
            if (!success) AntdMessage.error('获取用户列表失败：' + message);
            setUserCount(count);
            return {success: success && count !== -1, data, total: count};
        } catch (error) {
            showError(error);
        }
    }

    const handleEditClick = (userId: number) => {
        setEditingUserIdForInfo(userId);
        setIsUserEditModalVisible(true);
    };

    const manageUser = async (username: string, action: "delete" | "disable" | "enable") => {
        try {
            const res = await API.post('/api/user/agency/manage', {username, action});
            const {success, message} = res.data;
            if (success) {
                AntdMessage.success('操作成功！');
                ref.current.reload();//刷新
            } else {
                AntdMessage.error(message);
            }
        } catch (error) {
            showError(error);
        }
    };

    const renderUserType = (userType: number) => {
        switch (userType) {
            case 1:
                return <Tag color="blue">用户名</Tag>;
            case 2:
                return <Tag color="cyan">邮箱</Tag>;
            case 3:
                return <Tag color="orange">手机</Tag>;
            case 4:
                return <Tag color="red">Google</Tag>;
            case 5:
                return <Tag color="purple">GitHub</Tag>;
            case 6:
                return <Tag color="green">微信</Tag>;
            case 7:
                return <Tag color="geekblue">飞书</Tag>;
            case 8:
                return <Tag color="magenta">OIDC</Tag>;
            case 9:
                return <Tag color="volcano">Telegram</Tag>;
            default:
                return <Tag color="gray">未知</Tag>;
        }
    };

    const columns = [
        {
            title: 'ID',
            dataIndex: 'id',
            width: '4%',
            fixed: !isMobile(),//默认固定在左边
            disable: true,
        },
        {
            title: '用户名',
            copyable: true,
            ellipsis: true,
            fixed: !isMobile(),//默认固定在左边
            dataIndex: 'username',
            width: '10.5%',
            disable: true,
        },
        {
            title: '状态',
            dataIndex: 'status',
            align: 'center',
            width: '5.5%',
            render: (status: any) => renderUserStatus(status),
            renderFormItem: () => {
                return <Select placeholder={'选择用户状态'} allowClear>
                    <Option value="1">正常</Option>
                    <Option value="2">禁用</Option>
                </Select>;
            },
        },
        {
            title: '用户类型',
            dataIndex: 'user_type',
            align: 'center',
            width: '6%',
            render: (userType: number) => renderUserType(userType),
            renderFormItem: () => (
                <Select placeholder="选择用户类型" allowClear>
                    <Option value="1">用户名登录</Option>
                    <Option value="2">邮箱登录</Option>
                    <Option value="3">手机登录</Option>
                    <Option value="4">Google登录</Option>
                    <Option value="5">GitHub登录</Option>
                    <Option value="6">微信登录</Option>
                    <Option value="7">飞书登录</Option>
                    <Option value="8">OIDC登录</Option>
                    <Option value="9">Telegram登录</Option>
                </Select>
            ),
            hideInTable: isMobile(),
        },
        {
            title: '权限',
            dataIndex: 'role',
            align: 'center',
            width: '5.5%',
            renderFormItem: () => {
                return <Select placeholder="选择用户权限" allowClear>
                    <Option value="1">用户</Option>
                    <Option value="5">代理商</Option>
                    <Option value="10">管理</Option>
                    <Option value="100">超管</Option>
                </Select>;
            },
            render: (role: any) => renderUserRole(role),
            hideInTable: isMobile(),
        },
        {
            title: '分组',
            dataIndex: 'group',
            align: 'center',
            width: isMobile() ? '20%' : '7%',
            renderFormItem: () => {
                return (
                    <Select placeholder="选择用户分组" allowClear>
                        {userGroups.map((group: any) => (
                            <Option value={group} key={group}>{group}</Option>
                        ))}
                    </Select>
                );
            },
            render: (group: any) => renderUserGroupTag(group),
        },
        {
            title: '余额',
            dataIndex: 'quota',
            width: isMobile() ? '15%' : '7%',
            search: false,
            render: (quota: any, record: { used_quota: any }) => renderQuotaWithUsage(quota, record.used_quota),
            disable: true,
        },
        {
            title: '消耗',
            dataIndex: 'used_quota',
            width: '7%',
            search: false,
            render: (used_quota: number) => renderQuota(used_quota),
            hideInTable: isMobile(),
        },
        {
            title: '过期',
            hideInSearch: true,
            dataIndex: 'quota_expire_time',
            width: '6.5%',
            render: (text: number) => renderQuotaExpireTime(statusState.status.QuotaExpireEnabled, text),
            hideInTable: isMobile(),
        },
        {
            title: '次数',
            dataIndex: 'request_count',
            width: '6%',
            search: false,
            render: (request_count: number) => renderNumber(request_count),
            hideInTable: isMobile(),
        },
        {
            title: '最近登录',
            dataIndex: 'last_login_time',
            ellipsis: true,
            width: '8%',
            search: false,
            render: (_text: any, record: {
                last_login_time: number;
                last_login_ip: any;
                email: any;
                phone_number: any;
                inviter_id: number;
                inviteUserNumber: any
            }) => {
                let date = moment(record.last_login_time * 1000);
                if (date.year() === 1970) {
                    return '';
                } else {
                    return (
                        <Popover
                            title="更多信息"
                            content={[
                                <>
                                    <p>{`时间：${date.format('YYYY/MM/DD HH:mm:ss')}`}</p>
                                    <p>{`IP地址：${record.last_login_ip}`}</p>
                                    <p>{`邮箱地址：${record.email ? record.email : '无记录'}`}</p>
                                    <p>{`手机号码：${record.phone_number ? record.phone_number : '无记录'}`}</p>
                                    <p>{`邀请者ID：${record.inviter_id !== 0 ? record.inviter_id : '无'}`}</p>
                                    <p>{`邀请人数：${record.inviteUserNumber}`}</p>
                                </>,
                            ]}>
                            {date.format('YYYY-MM-DD')}
                        </Popover>
                    );
                }
            },
            hideInTable: isMobile(),
        },
        {
            title: '最近IP',
            dataIndex: 'last_login_ip',
            ellipsis: true,
            hideInTable: true,
            width: '8%',
            search: false,
        },
        {
            title: '备注',
            dataIndex: 'remark',
            width: '11.5%',
            render: (text, record) => {
                const handleEdit = async () => {
                    if (editingRemark === editingOriginalRemark) {
                        setEditingId(null);
                        setEditingRemark('');
                        return;
                    }
                    try {
                        AntdMessage.loading({content: '正在更新用户备注...', key: 'updateRemark'});
                        const res = await API.put(`/api/user/`, {id: editingId, remark: editingRemark});
                        if (res && res.data && res.data.success) {
                            AntdMessage.success({content: '更新成功', key: 'updateRemark'});
                            ref.current.reload();
                        } else {
                            AntdMessage.error({
                                content: res.data && res.data.message ? res.data.message : '更新失败',
                                key: 'updateRemark'
                            });
                        }
                    } catch (error) {
                        AntdMessage.error({content: '更新失败', key: 'updateRemark'});
                    }
                    setEditingId(null);
                    setEditingRemark('');
                };

                if (editingId === record.id) {
                    return (
                        <Input
                            size="small"
                            value={editingRemark}
                            onChange={(e) => setEditingRemark(e.target.value)}
                            onBlur={handleEdit}
                            onPressEnter={handleEdit}
                        />
                    );
                } else {
                    return (
                        <div
                            onClick={() => {
                                setEditingId(record.id);
                                setEditingRemark(record.remark || '');
                                setEditingOriginalRemark(record.remark || '');
                            }}
                            style={{
                                minHeight: '20px',
                                minWidth: '20px',
                                backgroundColor: 'transparent',
                            }}
                        >
                            {text}
                        </div>
                    );
                }
            },
        },
        {
            title: '邮箱',
            dataIndex: 'email',
            hideInTable: true,
        },
        {
            title: '操作',
            key: 'action',
            fixed: 'right',
            align: 'center',
            width: isMobile() ? '20%' : undefined,
            search: false,
            render: (_: any, record: any) => (
                <Space 
                    size={isMobile() ? [2, 2] : 'middle'}
                    style={{ 
                        display: 'flex', 
                        flexWrap: 'wrap',
                        justifyContent: isMobile() ? 'center' : 'flex-start'
                    }}
                >
                    <Button {...actionButtonProps} size={isMobile() ? 'small' : 'middle'} icon={<EditTwoTone />}
                        onClick={() => handleEditClick(record.id)} />
                    <Button
                        {...actionButtonProps} size={isMobile() ? 'small' : 'middle'}
                        icon={<FileTextTwoTone />}
                        onClick={() => navigate(
                            `/log?current=1&pageSize=10&username=${record.username}`)}
                    />
                    <Popconfirm
                        title={record.status === 1 ? '确定要禁用此用户吗？' : '确定要启用此用户吗？'}
                        onConfirm={() => manageUser(record.username, record.status === 1 ? 'disable' : 'enable')}
                        placement="topRight"
                        mouseLeaveDelay={0}
                        mouseEnterDelay={0.1}
                        overlayStyle={{ maxWidth: '300px', position: 'absolute' }}
                        destroyTooltipOnHide={true}
                        autoAdjustOverflow={false}
                        trigger="click"
                    >
                        <Button {...actionButtonProps} size={isMobile() ? 'small' : 'middle'}
                            icon={record.status === 1 ? <StopTwoTone /> : <PlayCircleTwoTone />} />
                    </Popconfirm>
                    <Button {...actionButtonProps} size={isMobile() ? 'small' : 'middle'} icon={<DeleteTwoTone twoToneColor={'#f5222d'} />}
                        onClick={() => {
                            modal.confirm({
                                title: (<span>删除用户<b>{record.username}</b>？</span>),
                                content: '删除后无法恢复，请谨慎操作！',
                                onOk: () => manageUser(record.username, 'delete'),
                                maskClosable: true,
                                okButtonProps: { danger: true }
                            });
                        }}
                    />
                </Space>
            ),
        },
    ];

    return (
        <>
            <ProTable
                cardBordered
                actionRef={ref as any}
                defaultSize={"small"}
                rowKey="id"
                scroll={isMobile() ? {x: 700} : {x: 1300}}
                request={request as any}
                columnEmptyText={false}
                search={{labelWidth: 'auto', span: isMobile() ? undefined : 6}}
                columns={columns as any}
                pagination={{
                    ...paginationProps,
                    total: userCount,
                }}
                headerTitle="用户管理"
                toolBarRender={() => [<Button type="primary" icon={<CrownFilled/>}
                                              onClick={() => setIsAddUserModalVisible(true)}>添加</Button>]}
            />

            <AddUserModal
                isAddUserModalVisible={isAddUserModalVisible}
                setIsAddUserModalVisible={setIsAddUserModalVisible}
                reload={() => ref.current.reload()}
            />

            <EditUserModal
                isUserEditModalVisible={isUserEditModalVisible}
                setIsUserEditModalVisible={setIsUserEditModalVisible}
                editingUserId={editingUserIdForInfo}
                setEditingUserId={setEditingUserIdForInfo}
                reload={() => ref.current.reload()}
                isRoot={userState.user.role === 100}
            />
        </>
    );
};

export default UsersTable;
