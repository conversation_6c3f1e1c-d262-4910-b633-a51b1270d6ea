// @ts-nocheck

import React, {useState, Suspense} from 'react'
import {HappyProvider} from '@ant-design/happy-work-theme'
import {Button, Checkbox, Dropdown, message as AntdMessage, Menu, Modal, Popconfirm, Popover, Select, Space, Tag, Tooltip, Spin, Switch} from 'antd'
import {ProTable} from '@ant-design/pro-components'
import {
  ApiFilled,
  BulbFilled,
  BulbTwoTone,
  ClockCircleFilled,
  ClockCircleOutlined,
  CopyTwoTone,
  DeleteTwoTone,
  DiffFilled,
  DollarCircleOutlined,
  DollarOutlined,
  EditTwoTone,
  FileTextTwoTone,
  OpenAIOutlined,
  PayCircleFilled,
  PlayCircleTwoTone,
  QuestionCircleFilled,
  QuestionCircleOutlined,
  StopTwoTone,
  ThunderboltOutlined,
  ToolTwoTone,
} from '@ant-design/icons'
import {API, copy, isMobile, showError} from '../../helpers'
import {createRenderFunctions, renderChannelBillingTypesTag, renderModels, renderQuotaNumber, renderResponseTime, renderUserGroupTag,} from '../../helpers/render'
import {CHANNEL_TYPE_OPTIONS} from '../../constants'
import EditChannelGroupModal from './EditChannelGroupModal'
import {useTranslation} from 'react-i18next'

// 懒加载编辑渠道的模态框组件
const EditChannelFormModal = React.lazy(() => import('../Channel/EditChannelFormModal'));

const ChannelGroupTable = () => {
    const { t } = useTranslation();
    const [channelGroups, setChannelGroups] = useState([]);
    const [loading, setLoading] = useState(true);
    // const customItemPerPage = useState(localStorage.getItem('customItemPerPage'));
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [addChannelsModalVisible, setAddChannelsModalVisible] = useState(false);
    const [selectedInnerChannelRowKeys, setSelectedInnerChannelRowKeys] = useState(
        []);
    // 以下用于显示【未绑定的渠道组】模态框中的表格
    let groupRatio = localStorage.getItem('GroupRatio');
    let parsedGroupRatio = groupRatio ? JSON.parse(groupRatio) : null;
    const [userGroups] = useState(
        parsedGroupRatio ? Object.keys(parsedGroupRatio) : []);
    const [unboundChannels, setUnboundChannels] = useState([]);
    const [unboundChannelsModalVisible, setUnboundChannelsModalVisible] = useState(
        false);
    const [unboundChannelsPage, setUnboundChannelsPage] = useState(1);
    const [unboundChannelsItemsPerPage, setUnboundChannelsItemsPerPage] = useState(
        10);
    const [unboundChannelsTotal, setUnboundChannelsTotal] = useState(0);
    const [selectedUnboundChannelRowKeys, setSelectedUnboundChannelRowKeys] = useState(
        []);
    const [searchParams, setSearchParams] = useState({});
    const [channelGroupsCount, setChannelGroupsCount] = useState(0);
    const [isEditChannelGroupModalVisible, setIsEditChannelGroupModalVisible] = useState(
        false);
    const [editingChannelGroup, setEditingChannelGroup] = useState(null);
    const [loadingChannels, setLoadingChannels] = useState(true);
    const [testingChannelIds, setTestingChannelIds] = useState([]);
    // 编辑渠道相关状态
    const [isEditChannelModalVisible, setIsEditChannelModalVisible] = useState(false);
    const [editingChannelId, setEditingChannelId] = useState(0);
    const [isChannelCopyMode, setIsChannelCopyMode] = useState(false);
    const Option = Select.Option;
    
    // 添加双数据库同步状态
    const [syncBothDB, setSyncBothDB] = useState(false);
    
    const ref = React.useRef({
        reload: function (resetPageIndex) {
        },
        reset: function () {
        },
    });
    
    // 获取渲染函数，传入t参数
    const { renderChannelStatusTag } = createRenderFunctions(t);
    
    // 处理编辑渠道 - 直接设置状态，让EditChannelFormModal组件自己显示
    const handleEditChannel = (channelId, isCopy = false) => {
        setEditingChannelId(channelId);
        setIsChannelCopyMode(isCopy);
        setIsEditChannelModalVisible(true);
    };

    //编辑渠道组（传入 id-编辑，不传入-新建）
    const handleEditClick = (channelGroupId) => {
        setEditingChannelGroup(channelGroupId); // 设置当前编辑的渠道 ID
        setIsEditChannelGroupModalVisible(true); // 显示模态框
    };

    //获取渠道组数量
    const getChannelGroupsCount = async (params) => {
        delete params.current;
        delete params.pageSize;
        let query = '';
        Object.keys(params).forEach((key) => {
            if (params[key]) {
                query += `&${key}=${params[key]}`;
            }
        });
        const res = await API.get(`/api/channelGroup/count?${query}`);
        const {success, message, data} = res.data;
        if (success) {
            setChannelGroupsCount(data.count);//注意：这里返回的 data 直接就是总数了，还不太一样....
        } else {
            showError(message || '获取渠道组数量失败');
        }
    };

    //刷新单个渠道组的信息（只更新指定渠道组的信息，不影响已展开的渠道数据）
    const refreshChannelGroupStats = async (channelGroupId) => {
        try {
            // 获取指定渠道组的最新信息
            const res = await API.get(`/api/channelGroup/${channelGroupId}`);
            const {success, message, data} = res.data;
            if (success) {
                // 更新指定渠道组的信息，保留其他数据（包括已展开的渠道列表）
                setChannelGroups(prevGroups =>
                    prevGroups.map(group =>
                        group.id === channelGroupId
                            ? {
                                ...group,
                                // 更新基本信息
                                name: data.name,
                                status: data.status,
                                group: data.group,
                                models: data.models,
                                sort: data.sort,
                                weight: data.weight,
                                retryInterval: data.retryInterval,
                                overFrequencyAutoDisable: data.overFrequencyAutoDisable,
                                // 更新统计信息
                                channelStats: data.channelStats,
                                // 保留展开状态相关的数据
                                channels: group.channels,
                                activePage: group.activePage,
                                itemsPerPage: group.itemsPerPage,
                                channelCount: group.channelCount
                            }
                            : group
                    )
                );
            } else {
                console.error('获取渠道组信息失败:', message);
            }
        } catch (error) {
            console.error('刷新渠道组信息时出错:', error);
        }
    };

    //获取渠道组
    const fetchChannelGroups = async (params = {}) => {
        setLoading(true);
        let query = '';
        Object.keys(params).forEach((key) => {
            if (params[key]) {
                query += `&${key}=${params[key]}`;
            }
        });
        let url = `/api/channelGroup/?p=${params.current -
        1}&pageSize=${params.pageSize}${query}`;
        const res = await API.get(url);
        const {success, message, data} = res.data;
        if (success) {
            // 后端已经返回了包含统计信息的数据，直接使用即可
            setChannelGroups(data);
            await getChannelGroupsCount(params);
            setLoading(false);
            return {
                data: data,
                success: true,
                total: channelGroupsCount,
            };
        } else {
            showError(message);
            setLoading(false);
            return {data: [], success: false};
        }
    };

    //获取渠道组 ID 为 0 的渠道（未绑定的渠道）
    const fetchUnboundChannels = async (
        page = unboundChannelsPage, pageSize = itemsPerPage, search = {}) => {
        // 合并分页和搜索参数
        const params = {p: page - 1, pageSize, ...searchParams, ...search};

        // 构建查询字符串
        const query = Object.keys(params).map(
            key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join('&');

        // 请求未绑定渠道的数量
        const countResponse = await API.get(
            `/api/channel/count?channelGroupId=-1&${query}`);
        if (countResponse.data.success) {
            setUnboundChannelsTotal(countResponse.data.count);
        } else {
            AntdMessage.error('获取未绑定的渠道数量失败');
        }

        // 请求未绑定的渠道
        const response = await API.get(`/api/channel/?channelGroupId=-1&${query}`);
        if (response.data.success) {
            setUnboundChannels(response.data.data);
        } else {
            AntdMessage.error('获取未绑定的渠道失败');
        }
    };

    //渠道测试
    const testChannel = async (id, name, model) => {
        if (testingChannelIds.includes(id)) {
            console.log(`渠道 ${name}（ID：${id}）测试正在进行中`);
            return;
        }
        setTestingChannelIds((prevIds) => [...prevIds, id]);
        try {
            const url = model ? `/api/channel/test/${id}?model=${model}` : `/api/channel/test/${id}/`;
            const res = await API.get(url);
            const {success, message, time, statusCode} = res.data;
            if (success && statusCode === 200) {
                AntdMessage.success(`渠道 ${name}（ID：${id}）${model ? `模型 ${model} ` : ''}测试成功，耗时 ${time.toFixed(2)}s`);
                
                // 找到当前渠道所属的渠道组
                const channelGroup = channelGroups.find(group => 
                    group.channels && group.channels.some(channel => channel.id === id)
                );
                
                if (channelGroup) {
                    // 刷新该渠道组的渠道列表和统计信息
                    await loadChannels(channelGroup.id, channelGroup.activePage || 1, channelGroup.itemsPerPage || 10);
                    await refreshChannelGroupStats(channelGroup.id);
                }
            } else {
                AntdMessage.error(`渠道 ${name}（ID：${id}）${model ? `模型 ${model} ` : ''}测试失败，状态码: ${statusCode}，原因: ${message || '未知'}`);
                
                // 即使测试失败也刷新列表，因为状态可能已变更
                const channelGroup = channelGroups.find(group =>
                    group.channels && group.channels.some(channel => channel.id === id)
                );

                if (channelGroup) {
                    await loadChannels(channelGroup.id, channelGroup.activePage || 1, channelGroup.itemsPerPage || 10);
                    await refreshChannelGroupStats(channelGroup.id);
                }
            }
        } catch (error) {
            showError(error);
        } finally {
            setTestingChannelIds((prevIds) => prevIds.filter((i) => i !== id));
        }
    };

    //操作渠道
    const manageChannel = async (id, action, value) => {
        let data = {id};
        let res;
        const syncParam = syncBothDB ? '?syncBothDB=true' : '';
        
        switch (action) {
            case 'delete':
                res = await API.delete(`/api/channel/${id}/${syncParam}`);
                break;
            case 'enable':
                data.status = 1;
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            case 'disable':
                data.status = 2;
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            case 'cleanUsage':
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}cleanUsage=true`, data);
                break;
            case 'priority':
                if (value === '') return;
                data.priority = parseInt(value);
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            case 'weight':
                if (value === '') return;
                data.weight = parseInt(value);
                if (data.weight < 0) data.weight = 0;
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            default:
                return;
        }
        const {success, message} = res.data;
        if (success) {
            // 找到当前渠道所属的渠道组
            const channelGroup = channelGroups.find(group => 
                group.channels && group.channels.some(channel => channel.id === id)
            );
            
            if (channelGroup) {
                // 刷新该渠道组的渠道列表和统计信息
                await loadChannels(channelGroup.id, channelGroup.activePage || 1, channelGroup.itemsPerPage || 10);
                await refreshChannelGroupStats(channelGroup.id);
            }
            
            AntdMessage.success('操作成功');
        } else {
            AntdMessage.error(`操作失败: ${message}`);
        }
    };

    //更新指定渠道余额
    const updateChannelBalance = async (id, name) => {
        const res = await API.get(`/api/channel/update_balance/${id}/`);
        const {success, message} = res.data;
        if (success) {
            AntdMessage.success(`渠道 ${name} 余额更新成功`);
            await loadChannels(id);
        } else {
            AntdMessage.error(`渠道 ${name} 余额更新失败: ${message}`);
        }
    };

    //加载全部渠道，按渠道绑定的渠道组ID内嵌显示到对应的渠道组下
    const loadChannels = async (
        groupId, page = 1, pageSize = itemsPerPage, search = {}) => {
        console.log('加载渠道 - 组ID:', groupId, '页码:', page, '每页条数:', pageSize);
        
        // 合并分页和搜索参数
        const params = {p: page - 1, pageSize, ...searchParams, ...search};

        // 构建查询字符串
        const query = Object.keys(params).map(
            key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`).join('&');

        let newChannelGroups = [...channelGroups];
        // 请求特定渠道组的渠道数量
        const countResponse = await API.get(
            `/api/channel/count?channelGroupId=${groupId}&${query}`);
        if (countResponse.data.success) {
            console.log('渠道数量响应:', countResponse.data);
            const count = countResponse.data.data?.count || countResponse.data.count;
            // 更新渠道组的渠道数量
            for (let group of newChannelGroups) {
                if (group.id === groupId) {
                    group.channelCount = count;
                    console.log('更新渠道组数量:', group.id, '=', count);
                    break;
                }
            }
        } else {
            AntdMessage.error("获取渠道数量失败");
        }

        // 请求特定渠道组的渠道
        const res = await API.get(
            `/api/channel/?channelGroupId=${groupId}&${query}`);
        const {success, message, data} = res.data;
        if (success) {
            console.log('渠道数据响应:', res.data);
            for (let group of newChannelGroups) {
                if (group.id === groupId) {
                    group.channels = data;
                    group.activePage = page;
                    group.itemsPerPage = pageSize;
                    break;
                }
            }
            setChannelGroups(newChannelGroups);
            return newChannelGroups;
        } else {
            AntdMessage.error('获取渠道组内渠道失败');
        }
    };

    //批量添加指定渠道到某个渠道组
    const handleAddChannelsToGroup = async (channelGroupId, isUnbound) => {
        // 根据 isUnbound 使用正确的状态
        const selectedChannelIds = isUnbound
            ? selectedUnboundChannelRowKeys
            : selectedInnerChannelRowKeys;

        const response = await API.post('/api/channelGroup/addChannels', {
            channelGroupId,
            channelIdList: selectedChannelIds,
        });
        const {success, message, data} = response.data;
        if (success) {
            AntdMessage.success('成功将选定的渠道添加到渠道组');
            setAddChannelsModalVisible(false);
            if (isUnbound) {
                setSelectedUnboundChannelRowKeys([]);
            } else {
                setSelectedInnerChannelRowKeys([]);
            }

            const updateData = async () => {
                await ref.current.reload();
                await fetchUnboundChannels(); // 重新获取未绑定的渠道数据
                await loadChannels(channelGroupId); // 重新加载新增渠道的渠道组的渠道
                await refreshChannelGroupStats(channelGroupId); // 刷新目标渠道组的统计信息
                if (isUnbound) {
                    const oldChannelGroup = channelGroups.find(group => group.channels &&
                        group.channels.some(
                            channel => selectedUnboundChannelRowKeys.includes(channel.id))); // 重新加载减少渠道的渠道组的渠道
                    if (oldChannelGroup) {
                        await loadChannels(oldChannelGroup.id);
                        await refreshChannelGroupStats(oldChannelGroup.id); // 刷新原渠道组的统计信息
                    }
                } else {
                    const oldChannelGroup = channelGroups.find(group => group.channels &&
                        group.channels.some(
                            channel => selectedInnerChannelRowKeys.includes(channel.id))); // 重新加载减少渠道的渠道组的渠道
                    if (oldChannelGroup) {
                        await loadChannels(oldChannelGroup.id);
                        await refreshChannelGroupStats(oldChannelGroup.id); // 刷新原渠道组的统计信息
                    }
                }
            };
            await updateData();
        } else {
            AntdMessage.error('无法将选定的渠道添加到渠道组');
        }
    };

    //内嵌渠道组的表格
    const expandedRowRender = (record) => {
        let channels = record.channels || [];
        const currentChannelGroupId = record.id;
        const itemsPerPage = record.itemsPerPage || 10;
        const activePage = record.activePage || 1;
        const channelCount = record.channelCount || 0;
        return (
            <ProTable
                rowKey="id"
                columns={[
                    {title: '渠道ID', dataIndex: 'id'},
                    {
                        title: '渠道名称', 
                        dataIndex: 'name',
                        render: (text, record) => {
                            const displayName = record.remark ? record.remark : text;
                            const createdTime = record.created_time ? new Date(record.created_time * 1000).toLocaleString() : '-';
                            return (
                                <Tooltip
                                    title={
                                        <>
                                            <div>渠道名称: {text}</div>
                                            {record.remark && <div>备注: {record.remark}</div>}
                                            <div>创建时间: {createdTime}</div>
                                        </>
                                    }
                                >
                                    <span>{displayName}</span>
                                </Tooltip>
                            );
                        },
                    },
                    {
                        title: '模式', 
                        dataIndex: 'billing_type',
                        render: (billing_type) => {
                            switch (billing_type) {
                                case 1:
                                    return <Tag color="processing" icon={<PayCircleFilled/>}>按量计费</Tag>;
                                case 2:
                                    return <Tag color="volcano" icon={<ClockCircleFilled/>}>按次计费</Tag>;
                                default:
                                    return <Tag color="default" icon={<QuestionCircleFilled/>}>未知方式</Tag>;
                            }
                        },
                    },
                    {
                        title: '平台',
                        dataIndex: 'type',
                        render: (type, record) => {
                            const channel = CHANNEL_TYPE_OPTIONS.find(option => option.key === type);
                            // Shell API渠道特殊处理
                            if (record.type === 7007 && record.base_url !== '') {
                                return (
                                    <Popover
                                        title="渠道信息"
                                        content={
                                            <>
                                                <a href={`${record.base_url}`} target="_blank" rel="noopener noreferrer">
                                                    {`${record.base_url}`}
                                                </a>
                                                <Button type="link" onClick={async () => {
                                                    const res = await API.get(`/api/channel/${record.id}`);
                                                    const {success, message, data} = res.data;
                                                    if (success && data.key) {
                                                        await copy(data.key);
                                                        AntdMessage.success('密钥复制成功');
                                                    } else if (data.key.length === 0) {
                                                        AntdMessage.error('密钥为空');
                                                    } else {
                                                        AntdMessage.error(`复制失败: ${message}`);
                                                    }
                                                }}>
                                                    复制密钥
                                                </Button>
                                            </>
                                        }
                                    >
                                        <Tag color={channel?.color || 'default'}>
                                            {channel?.text || '未知平台'}
                                        </Tag>
                                    </Popover>
                                );
                            }
                            return channel ? <Tag color={channel.color}>{channel.text}</Tag> : <Tag>未知平台</Tag>;
                        },
                    },
                    {
                        title: '响应时间',
                        dataIndex: 'response_time',
                        render: (text, record) => renderResponseTime(record.response_time),
                    },
                    {
                        title: '状态', 
                        dataIndex: 'status',
                        render: (status, record) => {
                            // 直接使用统一的渠道状态渲染函数
                            return renderChannelStatusTag(record);
                        },
                    },
                    {
                        title: '使用额度', 
                        dataIndex: 'used_quota',
                        render: (text, record) => {
                            return (
                                <Popover
                                    title="使用详情"
                                    content={
                                        <>
                                            <p>已使用: ${(record.used_quota / 500000).toFixed(2)}</p>
                                            <p>剩余: ${(record.balance).toFixed(2)}</p>
                                            {record.custom_balance_limit && record.custom_balance_limit > 0 && 
                                                <p>自定义额度: ${record.custom_balance_limit.toFixed(2)}</p>}
                                        </>
                                    }
                                >
                                    <Tag
                                        icon={<DollarOutlined/>}
                                        bordered={false}
                                        color="blue"
                                        style={{cursor: 'pointer'}}
                                        onClick={() => updateChannelBalance(record.id, record.name)}
                                    >
                                        ${(record.used_quota / 500000).toFixed(2)}
                                    </Tag>
                                </Popover>
                            );
                        },
                    },
                    {
                        title: '剩余额度', 
                        dataIndex: 'balance',
                        render: (text, record) => {
                            const isShellApiUnlimitedQuotaToken = record.type === 7007 && record.balance > 9999990;
                            const isCustomBalance = record.custom_balance_limit && record.custom_balance_limit > 0;
                            
                            return (
                                <Popover
                                    title="额度详情"
                                    content={<>
                                        <p>已使用额度: ${(record.used_quota / 500000).toFixed(2)}</p>
                                        <p>剩余额度: ${(record.balance).toFixed(2)}</p>
                                        {isCustomBalance ? <p>自定义总额度: ${record.custom_balance_limit.toFixed(2)}</p> : <></>}
                                    </>}
                                >
                                    <Tag
                                        icon={<DollarCircleOutlined/>}
                                        bordered={false}
                                        color={isCustomBalance ? 'volcano' : isShellApiUnlimitedQuotaToken ? 'green' : 'cyan'}
                                        style={{cursor: 'pointer'}}
                                        onClick={() => updateChannelBalance(record.id, record.name)}
                                    >
                                        {isShellApiUnlimitedQuotaToken ? '充足' : (record.balance).toFixed(2)}
                                    </Tag>
                                </Popover>
                            );
                        },
                    },
                    {
                        title: '可用模型',
                        width: '18%',
                        dataIndex: 'models',
                        render: (text, record) => {
                            return renderModels(record.models, 4, 2);
                        },
                    },
                    {
                        title: '操作',
                        key: 'action',
                        width: '8%',
                        render: (_, record) => (
                            <Space>
                                <Button 
                                    type="text"
                                    size="small" 
                                    icon={testingChannelIds.includes(record.id) ? 
                                        <BulbFilled spin style={{ color: '#faad14' }} /> : 
                                        <BulbTwoTone />} 
                                    onClick={() => testChannel(record.id, record.name)}
                                />
                                <Button 
                                    type="text"
                                    size="small" 
                                    icon={record.status === 1 ? <StopTwoTone /> : <PlayCircleTwoTone />} 
                                    onClick={() => manageChannel(record.id, record.status === 1 ? 'disable' : 'enable')}
                                />
                                <Dropdown overlay={
                                    <Menu>
                                        <Menu.Item key="1" onClick={() => handleEditChannel(record.id, false)}>
                                            <EditTwoTone /> 编辑
                                        </Menu.Item>
                                        <Menu.Item key="copy" onClick={() => handleEditChannel(record.id, true)}>
                                            <CopyTwoTone /> 复制
                                        </Menu.Item>
                                        <Menu.Item key="2" onClick={() => window.location.href = `/log?channel=${record.id}`}>
                                            <FileTextTwoTone /> 查看日志
                                        </Menu.Item>
                                        <Menu.Item key="3" onClick={() => manageChannel(record.id, 'cleanUsage')}>
                                            <DeleteTwoTone /> 清除用量
                                        </Menu.Item>
                                        <Menu.Item key="4" onClick={() => window.location.href = `/ability?channel_id=${record.id}`}>
                                            <ThunderboltOutlined /> 查看能力
                                        </Menu.Item>
                                        <Menu.Item key="delete" onClick={() => deleteChannel(record.id, record.name)} danger>
                                            <DeleteTwoTone /> 删除
                                        </Menu.Item>
                                        {record.models && record.models.split(',').length > 0 && (
                                            <Menu.SubMenu key="test-models" title="测试模型">
                                                {record.models.split(',').map(model => (
                                                    <Menu.Item key={`test-${model}`} onClick={() => testChannel(record.id, record.name, model.trim())}>
                                                        {model.trim()}
                                                    </Menu.Item>
                                                ))}
                                            </Menu.SubMenu>
                                        )}
                                    </Menu>
                                }>
                                    <Button type="text" size="small" icon={<ToolTwoTone />} />
                                </Dropdown>
                            </Space>
                        ),
                    },
                ]}
                dataSource={channels}
                rowSelection={{
                    selectedRowKeys: selectedInnerChannelRowKeys,
                    onChange: setSelectedInnerChannelRowKeys,
                }}
                loading={loadingChannels}
                request={async (params, sorter, filter, a) => {
                    // 将 ProTable 的分页参数转换为 API 需要的参数
                    const {current, pageSize} = params;
                    params.channelGroupId = currentChannelGroupId;
                    setLoadingChannels(true);
                    await loadChannels(currentChannelGroupId, current, pageSize);
                    setLoadingChannels(false);
                }}
                search={false}//需要可以开启
                tableAlertOptionRender={() => {
                    return (
                        <Space size={16}>
                            <a onClick={() => setAddChannelsModalVisible(true)}>批量移动</a>
                        </Space>
                    );
                }}
                pagination={{
                    current: activePage,
                    pageSize: itemsPerPage,
                    total: channelCount,
                    onChange: (page, pageSize) => {
                        // 添加日志来调试
                        console.log('分页变化 - 页码:', page, '每页条数:', pageSize, '总数:', channelCount);
                        loadChannels(record.id, page, pageSize).then(r => {
                            console.log('加载渠道完成，新数据:', r);
                        });
                    },
                    showSizeChanger: true,
                    pageSizeOptions: [10, 50, 100, 200, 500, 1000],
                    showTotal: (total) => `第 ${(activePage-1)*itemsPerPage+1}-${Math.min(activePage*itemsPerPage, total)} 条/总共 ${total} 条`,
                }}
                options={false}
            />
        );
    };

    //删除指定 id 的渠道
    const deleteChannel = async (id, name) => {
        let localSyncBothDB = false;
        
        Modal.confirm({
            title: '确认删除',
            content: (
                <div>
                    <p>确定要删除渠道 <strong>{name}</strong> 吗？删除后无法恢复。</p>
                    <div style={{ marginTop: 16 }}>
                        <Switch 
                            checked={localSyncBothDB}
                            onChange={(checked) => { localSyncBothDB = checked; }}
                            checkedChildren="双写" 
                            unCheckedChildren="单写"
                        />
                        <span style={{ marginLeft: 8 }}>是否同时删除SQL和NoSQL数据库中的数据</span>
                    </div>
                </div>
            ),
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async () => {
                try {
                    const syncParam = localSyncBothDB ? '?syncBothDB=true' : '';
                    const res = await API.delete(`/api/channel/${id}/${syncParam}`);
                    const {success, message} = res.data;
                    if (success) {
                        AntdMessage.success('删除成功');
                        // 找到当前渠道所属的渠道组并刷新
                        const channelGroup = channelGroups.find(group => 
                            group.channels && group.channels.some(channel => channel.id === id)
                        );
                        
                        if (channelGroup) {
                            await loadChannels(channelGroup.id, channelGroup.activePage || 1, channelGroup.itemsPerPage || 10);
                            await refreshChannelGroupStats(channelGroup.id);
                        }
                    } else {
                        AntdMessage.error('删除失败：' + message);
                    }
                } catch (error) {
                    showError(error);
                }
            }
        });
    };

    //删除指定 id 的渠道组
    const handleRemove = async (id) => {
        let localSyncBothDB = false;
        
        Modal.confirm({
            title: '确认删除',
            content: (
                <div>
                    <p>确定要删除这个渠道组吗？删除后无法恢复。</p>
                    <div style={{ marginTop: 16 }}>
                        <Switch 
                            checked={localSyncBothDB}
                            onChange={(checked) => { localSyncBothDB = checked; }}
                            checkedChildren="双写" 
                            unCheckedChildren="单写"
                        />
                        <span style={{ marginLeft: 8 }}>是否同时删除SQL和NoSQL数据库中的数据</span>
                    </div>
                </div>
            ),
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async () => {
                try {
                    const syncParam = localSyncBothDB ? '?syncBothDB=true' : '';
                    const response = await API.delete(`/api/channelGroup/${id}${syncParam}`);
                    if (response.data.success) {
                        AntdMessage.success('删除成功');
                        ref.current.reload();
                    } else {
                        AntdMessage.error('删除失败: ' + response.data.message);
                    }
                } catch (error) {
                    AntdMessage.error('删除失败');
                }
            },
        });
    };

    //启停指定 id 的渠道组
    const handleEnableOrDisable = async (id, status) => {
        try {
            const syncParam = syncBothDB ? '?syncBothDB=true' : '';
            const response = await API.put(`/api/channelGroup/${syncParam}${syncParam ? '&' : '?'}status_only=true`, {
                id: id,
                status: status
            });
            if (response.data.success) {
                AntdMessage.success(status === 1 ? '启用成功' : '禁用成功');
                ref.current.reload();
            } else {
                AntdMessage.error((status === 1 ? '启用' : '禁用') + '失败: ' + response.data.message);
            }
        } catch (error) {
            AntdMessage.error((status === 1 ? '启用' : '禁用') + '失败');
        }
    };

    //主表格（渠道组）的列定义
    const channelGroupColumns = [
        {title: 'ID', dataIndex: 'id', width: '5%'},
        {title: '名称', dataIndex: 'name', width: '10%'},
        {
            title: '状态',
            dataIndex: 'status',
            width: '5%',
            render: (status) => {
                switch (status) {
                    case 1:
                        return <Tag color="success">启用</Tag>;
                    case 2:
                        return <Tag color="error">禁用</Tag>;
                    case 3:
                        return <Tag color="processing">其他</Tag>;
                    default:
                        return <Tag color="default">未知</Tag>;
                }
            },
            renderFormItem: (_, {defaultRender, ...rest}, form) => {
                return <Select {...rest} placeholder={'选择渠道组状态'} allowClear>
                    <Option value="1">正常</Option>
                    <Option value="2">禁用</Option>
                </Select>;
            },
        },
        {
            title: '渠道统计',
            dataIndex: 'channelStats',
            width: '15%',
            render: (stats, record) => {
                // 使用后端返回的统计信息
                if (!stats) {
                    return <span style={{ color: '#999' }}>暂无数据</span>;
                }
                
                return (
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                        <Tag color="blue">总计: {stats.total}</Tag>
                        {stats.enabled > 0 && <Tag color="success">正常: {stats.enabled}</Tag>}
                        {stats.disabled > 0 && <Tag color="error">禁用: {stats.disabled}</Tag>}
                        {stats.autoDisabled > 0 && <Tag color="warning">重试中: {stats.autoDisabled}</Tag>}
                        {stats.maxRetriesExceeded > 0 && <Tag color="volcano">停用: {stats.maxRetriesExceeded}</Tag>}
                        {stats.partiallyDisabled > 0 && <Tag color="orange">部分禁用: {stats.partiallyDisabled}</Tag>}
                        {stats.other > 0 && <Tag color="default">其他: {stats.other}</Tag>}
                    </div>
                );
            },
        },
        {
            title: '可用分组',
            dataIndex: 'group',
            width: '10%',
            render: (text) => renderUserGroupTag(text),
            renderFormItem: (_, {defaultRender, ...rest}, form) => {
                const defaultValue = 'default';
                const hasDefault = userGroups.includes(defaultValue);
                return <Select placeholder="选择用户分组" allowClear>
                    {userGroups.map(group => (
                        <Option value={group} key={group}>{group}</Option>
                    ))}
                    {!hasDefault && <Option value={defaultValue}
                                            key={defaultValue}>{defaultValue}</Option>}
                </Select>;
            },
        },
        {
            title: '可用模型',
            width: '18%',
            dataIndex: 'models',
            render: (text, record) => renderModels(record.models, 4, 2),
            hideInSearch: true,
        },
        {
            title: '熔断',
            dataIndex: 'overFrequencyAutoDisable',
            width: '5%',
            align: 'center',
            hideInSearch: true,
            render: (text, record) => <Checkbox
                checked={record.overFrequencyAutoDisable}
                onClick={() => AntdMessage.info('为防止误操作，请编辑渠道组', 0.8)}/>,
        },
        {
            title: '优先级',
            dataIndex: 'sort',
            width: '5%',
            align: 'center',
            hideInSearch: true,
        },
        {
            title: '权重',
            dataIndex: 'weight',
            width: '5%',
            align: 'center',
            hideInSearch: true,
        },
        {
            title: '重启间隔',
            dataIndex: 'retryInterval',
            width: '5%',
            hideInSearch: true,
            render: (text) => {
                switch (true) {
                    case (text < 0):
                        return <Tag color="green">不死模式</Tag>;
                    case (text < 60):
                        return <Tag color="cyan">{text} 秒</Tag>;
                    case (text < 600):
                        return <Tag color="geekblue">{text} 秒</Tag>;
                    default:
                        return <Tag color="gold">{text} 秒</Tag>;
                }
            },
        },
        {
            title: '操作',
            valueType: 'option',
            width: '8%',
            hideInSearch: true,
            render: (text, record) => [
                <>
                    <Button type="text" icon={<EditTwoTone/>} shape="circle" size="large"
                            onClick={() => handleEditClick(record.id)}/>
                    <Popconfirm title={`确认${record.status === 1
                        ? '禁用'
                        : '启用'}渠道组 ${record.name} 吗？`}
                                onConfirm={() => handleEnableOrDisable(record.id,
                                    record.status)}>
                        <Button shape="circle" type="text" size="large"
                                icon={record.status === 1 ? <StopTwoTone/> :
                                    <PlayCircleTwoTone/>}/>
                    </Popconfirm>
                    <Popconfirm title={`确认删除渠道组${record.name}（ID：${record.id}）吗`}
                                onConfirm={() => handleRemove(record.id)}>
                        <Button size="large" type="text" shape="circle"
                                icon={<DeleteTwoTone/>}/>
                    </Popconfirm>
                </>,
            ],
        },
    ];

    return (
        <>
            <ProTable
                cardBordered
                scroll={{x: 1500}}
                expandable={{
                    expandedRowRender,
                }}
                dataSource={channelGroups}
                rowKey="id"
                actionRef={ref}
                search={{labelWidth: 'auto', span: isMobile() ? undefined : 4}}
                toolBarRender={() => []}
                toolbar={{
                    title: `渠道组管理`,
                    tooltip: '操作会对渠道组下所有渠道生效',
                    actions: [
                        <>
                            <HappyProvider>
                                <Space>
                                    <Switch 
                                        checked={syncBothDB} 
                                        checkedChildren="双写" 
                                        unCheckedChildren="单写"
                                        onChange={setSyncBothDB}
                                        title="开启后操作将同时更新SQL和NoSQL数据库"
                                    />
                                    <Button
                                        icon={<ApiFilled/>}
                                        onClick={async () => {
                                            setSelectedInnerChannelRowKeys([]);
                                            await fetchUnboundChannels();
                                            setUnboundChannelsModalVisible(true);
                                        }}
                                    >绑定
                                    </Button>
                                    <Button icon={<DiffFilled/>} onClick={() => handleEditClick()}
                                            type="primary">创建</Button>
                                </Space>
                            </HappyProvider>
                        </>,
                    ],
                }}
                request={fetchChannelGroups}
                columns={channelGroupColumns}
                loading={loading}
                pagination={{
                    // current: activePage,
                    pageSize: itemsPerPage,
                    total: channelGroupsCount,
                    // onChange
                    showSizeChanger: true,
                    showQuickJumper: true,
                    pageSizeOptions: [10, 50, 200, 500, 1000],
                    onShowSizeChange: (current, size) => setItemsPerPage(size),
                }}
            />
            <Modal
                getContainer={() => document.getElementById('globalModalContainer')}
                afterOpenChange={(open) => {
                    if (open) {
                        console.log('Modal opened');
                        document.body.style.overflow = 'hidden';
                    } else {
                        console.log('Modal closed');
                        document.body.style.overflow = '';
                    }
                }}
                title="选择目标渠道组"
                open={addChannelsModalVisible}
                width={600}
                centered
                onCancel={() => setAddChannelsModalVisible(false)}
                footer={null}
            >
                {channelGroups.map((group) => (
                    <Button
                        key={group.id}
                        onClick={() => handleAddChannelsToGroup(group.id,
                            unboundChannelsModalVisible)} // 添加新的参数
                        style={{marginRight: 8, marginBottom: 8, marginTop: 8}}
                    >
                        {group.name}（渠道组ID：{group.id}）
                    </Button>
                ))}
            </Modal>
            <Modal
                afterOpenChange={(open) => {
                    if (open) {
                        console.log('Modal opened');
                        document.body.style.overflow = 'hidden';
                    } else {
                        console.log('Modal closed');
                        document.body.style.overflow = '';
                    }
                }}
                getContainer={() => document.getElementById('globalModalContainer')}
                title="未绑定的渠道"
                centered
                open={unboundChannelsModalVisible}
                styles={{
                    body: {
                        maxHeight: '760px',
                        overflow: 'auto',
                        overflowX: 'hidden',
                    },
                }}
                width={1200}
                onCancel={() => {
                    setUnboundChannelsModalVisible(false);
                    setSelectedUnboundChannelRowKeys([]);
                }}
                footer={null}
            >
                <ProTable
                    style={{marginTop: 30}}
                    cardBordered
                    options={false}//放不下
                    rowKey="id"
                    request={(params) => {
                        // 将 ProTable 的分页参数转换为 API 需要的参数
                        const {current, pageSize} = params;
                        return fetchUnboundChannels(current, pageSize, params);
                    }}
                    search={{labelWidth: 'auto'}}
                    columns={[
                        {title: 'ID', dataIndex: 'id'},
                        {title: '名称', dataIndex: 'name'},
                        {
                            title: '模式',
                            dataIndex: 'billing_type',
                            sortDirections: ['descend', 'ascend'],
                            render: (billing_type) => {
                                switch (billing_type) {
                                    case 1:
                                        return <Tag color="processing"
                                                    icon={<DollarOutlined/>}>按量计费</Tag>;
                                    case 2:
                                        return <Tag color="magenta"
                                                    icon={<ClockCircleOutlined/>}>按次计费</Tag>;
                                    default:
                                        return <Tag color="default"
                                                    icon={<QuestionCircleOutlined/>}>未知方式</Tag>;
                                }
                            },
                        },
                        {
                            title: '平台',
                            search: false,
                            dataIndex: 'type',
                            sortDirections: ['descend', 'ascend'],
                            render: (type, record) => {
                                const channel = CHANNEL_TYPE_OPTIONS.find(
                                    option => option.key === type);
                                return channel
                                    ? <Tag color={channel.color}>{channel.text}</Tag>
                                    : '';
                            },
                        },
                        {
                            title: '状态',
                            dataIndex: 'status',
                            sortDirections: ['descend', 'ascend'],
                            render: (status, record) => {
                                switch (status) {
                                    case 1:
                                        return <Tag color="success">正常</Tag>;
                                    case 2:
                                        return <Tag color="error">禁用</Tag>;
                                    case 3:
                                        return <Tag color="warning">禁用</Tag>;
                                    default:
                                        return <Tag color="default">未知</Tag>;
                                }
                            },
                            renderFormItem: (_, {defaultRender, ...rest}, form) => {
                                return <Select {...rest} placeholder={'选择渠道状态'}
                                               allowClear>
                                    <Option value="1">正常</Option>
                                    <Option value="2">手动禁用</Option>
                                    <Option value="3">自动禁用</Option>
                                </Select>;
                            },
                        },
                        {
                            title: '模型',
                            // search: false,
                            dataIndex: 'models',
                            render: (text, record) => renderModels(record.models, 0, 0),
                        },
                        {title: 'base_url', dataIndex: 'base_url'},
                    ]}
                    tableAlertOptionRender={() => {
                        return <a
                            onClick={() => setAddChannelsModalVisible(true)}>批量添加</a>;
                    }}
                    pagination={{
                        current: unboundChannelsPage,
                        pageSize: unboundChannelsItemsPerPage,
                        total: unboundChannelsTotal,
                        showQuickJumper: true,
                        next:
                            unboundChannelsTotal > unboundChannelsPage * itemsPerPage, // 如果已知的总数大于当前页面的最后一个项目的索引，那么就显示下一页的按钮
                        onChange: (page, pageSize) => {
                            setUnboundChannelsPage(page);
                            setUnboundChannelsItemsPerPage(pageSize);
                            fetchUnboundChannels(page, pageSize);
                        },
                        showSizeChanger: true,
                        pageSizeOptions: [10, 50, 200, 500, 1000],//ok
                    }}
                    dataSource={unboundChannels}
                    rowSelection={{
                        selectedRowKeys: selectedUnboundChannelRowKeys,
                        onChange: setSelectedUnboundChannelRowKeys,
                    }}
                />
            </Modal>

            <Modal
                getContainer={() => document.getElementById('globalModalContainer')}
                afterOpenChange={(open) => {
                    if (open) {
                        console.log('Modal opened');
                        document.body.style.overflow = 'hidden';
                    } else {
                        console.log('Modal closed');
                        document.body.style.overflow = '';
                    }
                }}
                title={editingChannelGroup ? '更新渠道组信息' : '创建新渠道组'}
                open={isEditChannelGroupModalVisible}
                width={650}
                centered
                onCancel={() => setIsEditChannelGroupModalVisible(false)}
                footer={null} // 不显示默认的模态框底部按钮
                destroyOnClose={true} // 关闭时销毁 Modal 里的子元素
                maskClosable={false}
                styles={{
                    body: {
                        maxHeight: '75vh',
                        overflow: 'auto',
                        overflowX: 'hidden',
                        padding: 5,
                    },
                }}
            >
                {/* 这里使用 EditChannelModal 组件，并传递 channelId 和关闭模态框的函数 */}
                <EditChannelGroupModal channelGroupId={editingChannelGroup}
                                       closeModal={async (isSuccess, editedChannelGroupId) => {
                                           setIsEditChannelGroupModalVisible(false);

                                           if (isSuccess) {
                                               if (editedChannelGroupId) {
                                                   // 编辑成功：只刷新被编辑的渠道组信息，保留已展开的渠道数据
                                                   await refreshChannelGroupStats(editedChannelGroupId);

                                                   // 如果被编辑的渠道组已展开，需要重新加载其渠道列表
                                                   const editedGroup = channelGroups.find(group => group.id === editedChannelGroupId);
                                                   if (editedGroup && editedGroup.channels) {
                                                       await loadChannels(editedChannelGroupId, editedGroup.activePage || 1, editedGroup.itemsPerPage || 10);
                                                   }
                                               } else {
                                                   // 新建成功：重新加载整个表格
                                                   ref.current.reload();
                                               }
                                           }
                                           // 取消或失败时不需要刷新
                                       }}/>
            </Modal>

            {/* 编辑渠道组件（不需要额外的Modal包装，因为组件内部已有Modal） */}
            <Suspense fallback={<div style={{ display: 'none' }}></div>}>
                <EditChannelFormModal
                    editingChannelId={editingChannelId}
                    setEditingChannelId={setEditingChannelId}
                    isEditChannelModalVisible={isEditChannelModalVisible}
                    setIsEditChannelModalVisible={setIsEditChannelModalVisible}
                    isCloneMode={isChannelCopyMode}
                    setIsCloneMode={setIsChannelCopyMode}
                    reload={async () => {
                        // 如果是编辑现有渠道，找到并刷新包含该渠道的渠道组
                        if (editingChannelId > 0 && !isChannelCopyMode) {
                            const channelGroup = channelGroups.find(group => 
                                group.channels && group.channels.some(channel => channel.id === editingChannelId)
                            );
                            
                            if (channelGroup) {
                                await loadChannels(channelGroup.id, channelGroup.activePage || 1, channelGroup.itemsPerPage || 10);
                                await refreshChannelGroupStats(channelGroup.id);
                            }
                        } else {
                            // 如果是新建或复制渠道，可能需要刷新未绑定的渠道列表
                            await fetchUnboundChannels();
                        }
                    }}
                />
            </Suspense>
        </>
    );
};

export default ChannelGroupTable;
