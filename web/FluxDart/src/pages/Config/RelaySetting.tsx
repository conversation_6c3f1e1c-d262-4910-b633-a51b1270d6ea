//RelaySetting.tsx，转发设置，如重试模式

import React, {CSSProperties, useContext, useEffect, useState} from 'react';
import {App, Button, Card, Form, Input, message, Modal, Select, Space, Spin, Switch, Tag, Tooltip, Row, Col} from 'antd';
import {useTranslation} from "react-i18next";
import {getAllSettings, updateSettings} from "../../helpers/api-request-module";
import {configFormProps} from "../../constants";
import {jsonValidationRule} from '../../helpers';
import {InfoCircleOutlined} from "@ant-design/icons";
import {StatusContext} from '../../context/Status';
import type {ConfigComponentProps} from './types';

const {Option} = Select;

const RelaySetting: React.FC<ConfigComponentProps> = ({highlightConfig}) => {
    const [data, setData] = useState([]);
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [form] = Form.useForm();
    const {t} = useTranslation();
    const [messageApi, contextHolder] = message.useMessage();
    const {message: AntdMessage} = App.useApp()
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isRetryBillingModalOpen, setIsRetryBillingModalOpen] = useState(false);

    const isCustomCircuitBreakerEnabled = Form.useWatch('CustomCircuitBreakerEnabled', form);
    const isCustomDisableChannelEnabled = Form.useWatch('CustomDisableChannelEnabled', form);
    const isRelayErrForceRetryKeywordEnabled = Form.useWatch('RelayErrForceRetryKeywordEnabled', form);
    const isRelayErrForceThrowErrorEnabled = Form.useWatch('RelayErrForceThrowErrorEnabled', form);
    const isEmptyPromptReplaceEnabled = Form.useWatch('EmptyPromptReplaceEnabled', form);
    const isHideRelayErrorEnabled = Form.useWatch('HideRelayErrorEnabled', form);
    const isDisableEntireChannelKeywordsEnabled = Form.useWatch('DisableEntireChannelKeywordsEnabled', form);
    const [statusState, statusDispatch] = useContext(StatusContext);

    // 添加高亮和滚动效果
    useEffect(() => {
        console.log('RelaySetting received highlightConfig:', highlightConfig);
        if (highlightConfig) {
            requestAnimationFrame(() => {
                const element = document.querySelector(`[data-field="${highlightConfig}"]`);
                if (element) {
                    element.scrollIntoView({behavior: 'smooth', block: 'center'});
                    element.classList.add('flash-highlight');
                    setTimeout(() => {
                        element.classList.remove('flash-highlight');
                    }, 2000);
                }
            });
        }
    }, [highlightConfig]);

    const getHighlightStyle = (fieldName: string): CSSProperties => {
        const isHighlighted = highlightConfig === fieldName;
        console.log(`Style check for ${fieldName}:`, {isHighlighted, highlightConfig});

        if (isHighlighted) {
            return {
                position: 'relative',
                animation: 'highlight-pulse 2s infinite',
                backgroundColor: '#ffd70033',
                padding: '16px',
                margin: '8px 0',
                borderRadius: '8px',
                border: '2px solid #ffd700',
                boxShadow: '0 0 10px rgba(255, 215, 0, 0.3)',
                transform: 'scale(1.02)',
                zIndex: 1
            };
        }
        return {};
    };

    useEffect(() => {
        getAllSettings().then((res) => {
            setData(res);
            form.setFieldsValue({...res, DisableChannelHttpStatusCodeList: []});
            form.setFieldsValue({...res, CircuitBreakerHttpStatusCodeList: []});
            form.setFieldsValue({...res, RelayErrForceRetryKeywordList: []});
            form.setFieldsValue({...res, RelayErrForceRetryModelList: []});
            form.setFieldsValue({...res, RelayErrForceThrowErrorKeywordList: []});
            form.setFieldsValue({...res, RelayErrForceThrowErrorModelList: []});
            form.setFieldsValue({...res, HideRelayErrorExceptList: []});
            if (res.DisableChannelHttpStatusCodeList !== '' && res.DisableChannelHttpStatusCodeList !== null && typeof res.DisableChannelHttpStatusCodeList !== 'undefined') {
                try {
                    form.setFieldsValue({DisableChannelHttpStatusCodeList: res.DisableChannelHttpStatusCodeList.split(',')});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '个性化禁用http状态码数据解析异常！'}).then();
                }
            }
            if (res.CircuitBreakerHttpStatusCodeList !== '' && res.CircuitBreakerHttpStatusCodeList !== null && typeof res.CircuitBreakerHttpStatusCodeList !== 'undefined') {
                try {
                    form.setFieldsValue({CircuitBreakerHttpStatusCodeList: res.CircuitBreakerHttpStatusCodeList.split(',')});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '个性化熔断http状态码数据解析异常！'}).then();
                }
            }
            if (res.RelayErrForceRetryKeywordList !== '' && res.RelayErrForceRetryKeywordList !== null && typeof res.RelayErrForceRetryKeywordList !== 'undefined') {
                try {
                    form.setFieldsValue({RelayErrForceRetryKeywordList: res.RelayErrForceRetryKeywordList.split(',')});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '强制重试关键词数据解析异常！'}).then();
                }
            }
            if (res.RelayErrForceRetryModelList !== '' && res.RelayErrForceRetryModelList !== null && typeof res.RelayErrForceRetryModelList !== 'undefined') {
                try {
                    form.setFieldsValue({RelayErrForceRetryModelList: res.RelayErrForceRetryModelList.split(',')});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '强制重试模型数据解析异常！'}).then();
                }
            }
            if (res.RelayErrForceThrowErrorKeywordList !== '' && res.RelayErrForceThrowErrorKeywordList !== null && typeof res.RelayErrForceThrowErrorKeywordList !== 'undefined') {
                try {
                    form.setFieldsValue({RelayErrForceThrowErrorKeywordList: res.RelayErrForceThrowErrorKeywordList.split(',')});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '强制抛出错误关键词数据解析异常！'}).then();
                }
            }
            if (res.RelayErrForceThrowErrorModelList !== '' && res.RelayErrForceThrowErrorModelList !== null && typeof res.RelayErrForceThrowErrorModelList !== 'undefined') {
                try {
                    form.setFieldsValue({RelayErrForceThrowErrorModelList: res.RelayErrForceThrowErrorModelList.split(',')});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '强制抛出错误模型数据解析异常！'}).then();
                }
            }
            if (res.HideRelayErrorExceptList !== '' && res.HideRelayErrorExceptList !== null && typeof res.HideRelayErrorExceptList !== 'undefined') {
                try {
                    form.setFieldsValue({HideRelayErrorExceptList: res.HideRelayErrorExceptList.split(',')});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '隐藏错误例外数据解析异常！'}).then();
                }
            }
            if (res.DisableEntireChannelKeywords !== '' && res.DisableEntireChannelKeywords !== null && typeof res.DisableEntireChannelKeywords !== 'undefined') {
                try {
                    form.setFieldsValue({DisableEntireChannelKeywords: res.DisableEntireChannelKeywords.split(',')});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '禁用整个渠道关键词数据解析异常！'}).then();
                }
            }
            
            // 处理CustomHideApiErrorTypes，它应该是JSON数组格式
            if (res.CustomHideApiErrorTypes !== '' && res.CustomHideApiErrorTypes !== null && typeof res.CustomHideApiErrorTypes !== 'undefined') {
                try {
                    // 如果是逗号分隔的字符串，转为JSON数组
                    const apiErrorTypes = res.CustomHideApiErrorTypes.includes('[') 
                        ? JSON.parse(res.CustomHideApiErrorTypes) 
                        : res.CustomHideApiErrorTypes.split(',');
                    form.setFieldsValue({CustomHideApiErrorTypes: JSON.stringify(apiErrorTypes)});
                } catch (e) {
                    AntdMessage.error({key: 'error', content: '自定义API错误类型数据解析异常！'}).then();
                }
            }
            
            setLoading(false);
        });
    }, []);

    const onFinish = async (values: any) => {
        if (submitting) {
            return;
        }
        setSubmitting(true);
        if (values.DisableChannelHttpStatusCodeList && values.DisableChannelHttpStatusCodeList.length > 0) {
            values.DisableChannelHttpStatusCodeList = values.DisableChannelHttpStatusCodeList.join(',');
        } else {
            values.DisableChannelHttpStatusCodeList = '';
        }
        if (values.CircuitBreakerHttpStatusCodeList && values.CircuitBreakerHttpStatusCodeList.length > 0) {
            values.CircuitBreakerHttpStatusCodeList = values.CircuitBreakerHttpStatusCodeList.join(',');
        } else {
            values.CircuitBreakerHttpStatusCodeList = '';
        }
        if (values.RelayErrForceRetryKeywordList && values.RelayErrForceRetryKeywordList.length > 0) {
            values.RelayErrForceRetryKeywordList = values.RelayErrForceRetryKeywordList.join(',');
        } else {
            values.RelayErrForceRetryKeywordList = '';
        }
        if (values.RelayErrForceRetryModelList && values.RelayErrForceRetryModelList.length > 0) {
            values.RelayErrForceRetryModelList = values.RelayErrForceRetryModelList.join(',');
        } else {
            values.RelayErrForceRetryModelList = '';
        }
        if (values.RelayErrForceThrowErrorKeywordList && values.RelayErrForceThrowErrorKeywordList.length > 0) {
            values.RelayErrForceThrowErrorKeywordList = values.RelayErrForceThrowErrorKeywordList.join(',');
        } else {
            values.RelayErrForceThrowErrorKeywordList = '';
        }
        if (values.RelayErrForceThrowErrorModelList && values.RelayErrForceThrowErrorModelList.length > 0) {
            values.RelayErrForceThrowErrorModelList = values.RelayErrForceThrowErrorModelList.join(',');
        } else {
            values.RelayErrForceThrowErrorModelList = '';
        }
        if (values.HideRelayErrorExceptList && values.HideRelayErrorExceptList.length > 0) {
            values.HideRelayErrorExceptList = values.HideRelayErrorExceptList.join(',');
        } else {
            values.HideRelayErrorExceptList = '';
        }
        if (values.DisableEntireChannelKeywords && values.DisableEntireChannelKeywords.length > 0) {
            values.DisableEntireChannelKeywords = values.DisableEntireChannelKeywords.join(',');
        } else {
            values.DisableEntireChannelKeywords = '';
        }
        
        // 处理CustomHideApiErrorTypes，确保它是正确的JSON格式
        if (values.CustomHideApiErrorTypes) {
            try {
                // 如果已经是JSON字符串，尝试解析确保格式正确
                const apiErrorTypes = JSON.parse(values.CustomHideApiErrorTypes);
                // 重新转为JSON字符串，确保格式正确
                values.CustomHideApiErrorTypes = JSON.stringify(apiErrorTypes);
            } catch (e) {
                // 如果解析失败，可能不是有效的JSON，给出提示
                messageApi.open({key: 'error', type: 'error', content: '自定义API错误类型格式不正确，应为JSON数组！'}).then();
                setSubmitting(false);
                return;
            }
        } else {
            values.CustomHideApiErrorTypes = '[]'; // 默认为空数组
        }
        
        const keys = Object.keys(values);
        const needUpdate = keys.filter(key => values[key] !== data[key]);
        const needUpdateData = needUpdate.map(key => ({key, value: values[key]}));
        if (needUpdateData.length === 0) {
            messageApi.open({key: 'info', type: 'info', content: '没有需要更新的数据', duration: 2}).then();
            setSubmitting(false);
            return;
        }
        messageApi.open({key: 'submit', type: 'loading', content: '正在提交...', duration: 0}).then();
        // Convert boolean type values to strings (end with Enabled)
        needUpdateData.forEach((item) => {
            if (item.key.endsWith('Enabled')) {
                item.value = item.value ? 'true' : 'false';
            }
        });
        await updateSettings(needUpdateData).then((success) => {
            if (success) {
                messageApi.open({key: 'submit', type: 'success', content: '提交成功！', duration: 1}).then();
            } else {
                messageApi.open({key: 'submit', type: 'error', content: '提交失败！',}).then();
            }
        });
        await getAllSettings().then((res) => setData(res));
        setSubmitting(false);
    }

    const CompatibilityModeDetails = () => (
        <div>
            <div className="mb-2">
                <b>支持的功能：</b>
                <ul className="my-1 pl-5">
                    <li>支持消息内容为字符串数组格式</li>
                    <li>自动合并多个字符串为单一内容</li>
                    <li>处理带role属性的旧格式消息</li>
                    <li>规范化工具调用格式</li>
                    <li>支持单个image_url对象自动转换为数组格式</li>
                    <li>自动将普通文本转换为标准的text类型格式</li>
                    <li>自动移除无效的tool_choice参数（当未指定tools时）</li>
                    <li>支持传统的prompt字段自动转换为messages格式</li>
                    <li>自动转换纯字符串内容为text对象格式</li>
                    <li>修复错误的image字段为标准的image_url格式</li>
                    <li>验证并清理无效的图片URL（仅支持http(s)和data URI格式）</li>
                    <li>自动处理base64图片格式转换</li>
                    <li>处理 embedding 和 moderation 模型的空字符串输入</li>
                    <li>自动调整超出模型限制的 max_tokens 值（如 GPT-4-mini 限制为 16384）</li>
                </ul>
            </div>
            <div className="mb-2">
                <b>格式转换示例：</b>
                <ul className="my-1 pl-5">
                    <li>图片请求：<code>{`{"type": "image_url"}`}</code> → <code>{`[{"type": "image_url"}]`}</code></li>
                    <li>文本数组：<code>{`["Hello", "World"]`}</code> → <code>{`"Hello World"`}</code></li>
                    <li>混合内容：<code>{`["text", {"role": "user"}]`}</code> → <code>{`[{"type": "text"}, {"type": "text"}]`}</code></li>
                    <li>工具调用：<code>{`{"tool_choice": "auto"}`}</code> → 当无tools时自动移除</li>
                    <li>传统格式：<code>{`{"prompt": "Hello"}`}</code> → <code>{`{"messages": [{"role": "user", "content": "Hello"}]}`}</code></li>
                    <li>纯字符串转换：<code>{`["直接文本"]`}</code> → <code>{`[{"type": "text", "text": "直接文本"}]`}</code></li>
                    <li>无效图片URL：<code>{`{"image_url": {"url": "src/local.jpg"}}`}</code> → 自动移除无效URL</li>
                    <li>Base64图片：<code>{`{"url": "base64string"}`}</code> → <code>{`{"url": "data:image/jpeg;base64,base64string"}`}</code></li>
                    <li>空字符串处理：<code>{`{"model": "text-embedding-ada-002", "input": [""]}`}</code> → <code>{`{"model": "text-embedding-ada-002", "input": ["."]}`}</code></li>
                    <li>Token限制：<code>{`{"model": "gpt-4-mini", "max_tokens": 40000}`}</code> → <code>{`{"model": "gpt-4-mini", "max_tokens": 16384}`}</code></li>
                </ul>
            </div>
            <div className="mb-2">
                <b>适用场景：</b>
                <ul className="my-1 pl-5">
                    <li>需要兼容旧版API格式的应用</li>
                    <li>使用非标准格式的第三方客户端</li>
                    <li>处理多样化的用户输入格式</li>
                    <li>需要自动验证和清理图片URL的场景</li>
                    <li>需要自动处理模型限制的场景</li>
                </ul>
            </div>
            <p className="text-yellow-500 my-2">
                注意：此功能会改变原有请求内容的格式，需要对每个请求进行解析判断。建议仅在以下情况启用：
                <ul className="my-1 pl-5 text-yellow-500">
                    <li>遇到格式兼容性问题时</li>
                    <li>需要支持多种客户端格式时</li>
                    <li>使用旧版API的应用迁移时</li>
                    <li>需要防止无效图片URL的场景</li>
                    <li>需要自动处理模型限制的场景</li>
                </ul>
                启用后可能会略微增加CPU占用，但可以显著提高请求的兼容性和安全性。
            </p>
        </div>
    );

    const RetryBillingTypeDetails = () => (
        <div>
            <div className="mb-4">
                <h4 style={{ color: '#1890ff', marginBottom: '12px' }}>功能说明</h4>
                <p>此功能用于控制重试时是否保持与第一次请求相同的计费类型，避免在同一个请求中出现混合计费的情况。</p>
            </div>
            
            <div className="mb-4">
                <h4 style={{ color: '#52c41a', marginBottom: '12px' }}>开启时的行为</h4>
                <ul className="my-1 pl-5">
                    <li><strong>按量计费优先：</strong>如果第一次请求使用了按量计费渠道，重试时只会在按量计费渠道中选择</li>
                    <li><strong>按次计费优先：</strong>如果第一次请求使用了按次计费渠道，重试时只会在按次计费渠道中选择</li>
                    <li><strong>计费透明：</strong>用户可以预期整个请求过程中的计费方式保持一致</li>
                    <li><strong>避免混淆：</strong>不会出现同一请求中既有按量又有按次计费的情况</li>
                </ul>
            </div>
            
            <div className="mb-4">
                <h4 style={{ color: '#faad14', marginBottom: '12px' }}>关闭时的行为</h4>
                <ul className="my-1 pl-5">
                    <li><strong>灵活降级：</strong>重试时如果优先计费类型的渠道都不可用，会降级到其他计费类型</li>
                    <li><strong>提高成功率：</strong>在渠道资源紧张时，可以最大化利用所有可用渠道</li>
                    <li><strong>可能混合计费：</strong>同一请求可能会产生不同类型的计费记录</li>
                </ul>
            </div>
            
            <div className="mb-4">
                <h4 style={{ color: '#722ed1', marginBottom: '12px' }}>适用场景</h4>
                <ul className="my-1 pl-5">
                    <li><strong>混合渠道组：</strong>当同一分组内同时配置了按量和按次计费渠道时</li>
                    <li><strong>计费透明度要求高：</strong>需要向用户提供清晰计费说明的场景</li>
                    <li><strong>财务管理：</strong>需要精确控制不同计费类型使用比例的情况</li>
                    <li><strong>用户体验：</strong>避免用户对计费方式产生困惑</li>
                </ul>
            </div>
            
            <div className="mb-4">
                <h4 style={{ color: '#f5222d', marginBottom: '12px' }}>使用建议</h4>
                <div style={{ backgroundColor: '#fff2f0', padding: '12px', borderRadius: '6px', border: '1px solid #ffccc7' }}>
                    <p><strong>推荐开启：</strong>在大多数情况下建议开启此功能，特别是：</p>
                    <ul className="my-1 pl-5">
                        <li>面向终端用户的服务</li>
                        <li>需要提供计费说明的商业环境</li>
                        <li>混合计费渠道配置的系统</li>
                    </ul>
                    <p style={{ marginTop: '8px' }}><strong>考虑关闭：</strong>仅在以下情况下考虑关闭：</p>
                    <ul className="my-1 pl-5">
                        <li>渠道资源非常紧张，需要最大化成功率</li>
                        <li>内部系统，对计费透明度要求不高</li>
                        <li>已有其他方式处理混合计费问题</li>
                    </ul>
                </div>
            </div>
            
            <div className="mb-2">
                <h4 style={{ color: '#13c2c2', marginBottom: '12px' }}>实际示例</h4>
                <div style={{ backgroundColor: '#f6ffed', padding: '12px', borderRadius: '6px', border: '1px solid #b7eb8f' }}>
                    <p><strong>场景：</strong>某分组配置了3个按量计费渠道和2个按次计费渠道，用户设置按量优先</p>
                    <p><strong>开启此功能时：</strong></p>
                    <ul className="my-1 pl-5">
                        <li>第1次请求：选择按量计费渠道A，请求失败</li>
                        <li>第2次重试：只在按量计费渠道B、C中选择，不会使用按次计费渠道</li>
                        <li>结果：整个请求过程保持按量计费</li>
                    </ul>
                    <p><strong>关闭此功能时：</strong></p>
                    <ul className="my-1 pl-5">
                        <li>第1次请求：选择按量计费渠道A，请求失败</li>
                        <li>第2次重试：按量计费渠道B失败后，可能会使用按次计费渠道</li>
                        <li>结果：可能出现混合计费情况</li>
                    </ul>
                </div>
            </div>
        </div>
    );

    return (
        <Spin spinning={loading}>
            {contextHolder}
            <Form
                {...configFormProps}
                form={form}
                onFinish={onFinish}
                name={"RelaySetting"}
            >
                <Space direction="vertical" style={{width: "100%"}}>
                    <Card title='爬虫优化设置' size="small" extra='用于优化请求,绕过目标服务的反爬机制'>
                        <Form.Item
                            label='全局自定义请求头'
                            name="GlobalCustomRequestHeaders"
                            tooltip='添加到所有请求中的自定义请求头,JSON格式'
                            style={getHighlightStyle('GlobalCustomRequestHeaders')}
                            data-field="GlobalCustomRequestHeaders"
                        >
                            <Input.TextArea autoSize={{minRows: 2, maxRows: 5}}
                                            placeholder='{"User-Agent": "Mozilla/5.0 ...", "Referer": "https://example.com"}'
                            />
                        </Form.Item>

                        <Form.Item
                            label='IP伪装'
                            name="SpoofIP"
                            tooltip='伪装请求的源IP地址(不需要代理支持) 记得关闭cfworkers否则图片下载还是走cfworkers'
                            style={getHighlightStyle('SpoofIP')}
                            data-field="SpoofIP"
                        >
                            <Input placeholder='例如: *************'/>
                        </Form.Item>

                        <Form.Item
                            label='启用IP伪装'
                            name="SpoofIPEnabled"
                            valuePropName="checked"
                            style={getHighlightStyle('SpoofIPEnabled')}
                            data-field="SpoofIPEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            label='反制成分判定'
                            name="AntiIngredientEnabled"
                            valuePropName="checked"
                            tooltip='会消耗更多服务器流量'
                        >
                            <Switch/>
                        </Form.Item>
                        {statusState.status.eise &&
                            <>
                                <Form.Item
                                    name={"EiseEnabled"}
                                    label="eise开关"
                                >
                                    <Switch/>
                                </Form.Item>
                                <Form.Item name={"EiseUrl"} label="eise url"
                                           tooltip="eise url 多个可以逗号拼接"
                                >
                                    <Input placeholder="eise url 多个可以逗号拼接"/>
                                </Form.Item>
                                <Form.Item name={"EiseKey"} label="eise key 32位"
                                           tooltip="必须32位不能多也不能少"
                                >
                                    <Input placeholder="eise key 32位"/>
                                </Form.Item>
                            </>
                        }
                    </Card>

                    <Card title={t('relay.dispatchOptions')} style={{ marginBottom: '20px' }}>
                        <Row align="middle" style={{ marginBottom: '24px' }}>
                            <Col span={8} style={{ textAlign: 'right', paddingRight: '12px' }}>
                                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                                    {t('relay.channelMetricsEnabled')}
                                    <Tag color="blue" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                        Beta
                                    </Tag>
                                    <Tooltip title={t('relay.channelMetricsEnabledTip')}>
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </span>
                            </Col>
                            <Col span={16}>
                                <Form.Item name="ChannelMetricsEnabled" valuePropName="checked" style={{ margin: 0 }}>
                                    <Switch />
                                </Form.Item>
                            </Col>
                        </Row>
                        
                        <Row align="middle">
                            <Col span={8} style={{ textAlign: 'right', paddingRight: '12px' }}>
                                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                                    {t('relay.channelScoreRoutingEnabled')}
                                    <Tag color="blue" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                        Beta
                                    </Tag>
                                    <Tooltip title={t('relay.channelScoreRoutingEnabledTip')}>
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </span>
                            </Col>
                            <Col span={16}>
                                <Form.Item name="ChannelScoreRoutingEnabled" valuePropName="checked" style={{ margin: 0 }}>
                                    <Switch />
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row align="middle" style={{ marginTop: '24px' }}>
                            <Col span={8} style={{ textAlign: 'right', paddingRight: '12px' }}>
                                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                                    全局忽略权重计算
                                    <Tag color="blue" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                        推荐
                                    </Tag>
                                    <Tooltip title={
                                        <div>
                                            <p><strong>功能说明：</strong>开启后将在相同优先级的情况下直接随机一个账号，不会根据权重进行计算，大大减少CPU占用。</p>
                                            <p><strong>适用场景：</strong>适合号池模式下账号较多（10个以上）的情况，可显著提高系统性能，降低CPU负载。</p>
                                            <p><strong>工作原理：</strong>系统会保留优先级（Sort）排序，但在同一优先级内随机选择账号，不再进行权重计算。</p>
                                            <p><strong>性能影响：</strong>通常可减少20-40%的CPU占用，号越多效果越明显。</p>
                                            <p><strong>注意事项：</strong>如果您依赖权重来控制特定账号的使用频率，开启此选项后所有同优先级账号被选中的概率将变为相同。</p>
                                        </div>
                                    }>
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </span>
                            </Col>
                            <Col span={16}>
                                <Form.Item name="GlobalIgnoreWeightCalculationEnabled" valuePropName="checked" style={{ margin: 0 }}>
                                    <Switch onChange={(checked) => {
                                        // 如果关闭了权重计算，也关闭优先级忽略（因为逻辑上优先级忽略依赖于权重忽略）
                                        if (!checked) {
                                            form.setFieldsValue({ GlobalIgnorePriorityEnabled: false });
                                        }
                                    }} />
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row align="middle" style={{ marginTop: '24px' }}>
                            <Col span={8} style={{ textAlign: 'right', paddingRight: '12px' }}>
                                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                                    全局忽略优先级排序
                                    <Tag color="green" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                        极速
                                    </Tag>
                                    <Tooltip title={
                                        <div>
                                            <p><strong>功能说明：</strong>开启后将直接在所有账号中随机选择，完全忽略优先级和权重计算，最大程度节约CPU开销。</p>
                                            <p><strong>自动关联：</strong>开启此选项会自动开启"全局忽略权重计算"；关闭"全局忽略权重计算"会自动关闭此选项。</p>
                                            <p><strong>适用场景：</strong>特别适合号池模式下账号质量相似且数量很多（数十个以上）的情况。</p>
                                            <p><strong>工作原理：</strong>系统会完全无视优先级和权重设置，在所有可用账号中随机选择一个。</p>
                                            <p><strong>性能影响：</strong>比"全局忽略权重计算"再多节省10-20%的CPU占用，可大幅提高系统处理能力。</p>
                                            <p><strong>重要提示：</strong>开启此选项后，您设置的优先级（Sort）将失效，所有账号被选中概率相同，不再区分高优先和低优先账号。</p>
                                        </div>
                                    }>
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </span>
                            </Col>
                            <Col span={16}>
                                <Form.Item name="GlobalIgnorePriorityEnabled" valuePropName="checked" style={{ margin: 0 }}>
                                    <Switch 
                                        onChange={(checked) => {
                                            // 如果开启了优先级忽略，确保权重计算也被忽略
                                            if (checked) {
                                                form.setFieldsValue({ GlobalIgnoreWeightCalculationEnabled: true });
                                            }
                                        }} 
                                    />
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row align="middle" style={{ marginTop: '24px' }}>
                            <Col span={8} style={{ textAlign: 'right', paddingRight: '12px' }}>
                                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                                    {t('relay.globalIgnoreBillingTypeFilteringEnabled')}
                                    <Tag color="green" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                        性能优化
                                    </Tag>
                                    <Tooltip title={t('relay.globalIgnoreBillingTypeFilteringEnabledTip')}>
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </span>
                            </Col>
                            <Col span={16}>
                                <Form.Item name="GlobalIgnoreBillingTypeFilteringEnabled" valuePropName="checked" style={{ margin: 0 }}>
                                    <Switch />
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row align="middle" style={{ marginTop: '24px' }}>
                            <Col span={8} style={{ textAlign: 'right', paddingRight: '12px' }}>
                                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                                    {t('relay.globalIgnoreFunctionCallFilteringEnabled')}
                                    <Tag color="green" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                        性能优化
                                    </Tag>
                                    <Tooltip title={t('relay.globalIgnoreFunctionCallFilteringEnabledTip')}>
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </span>
                            </Col>
                            <Col span={16}>
                                <Form.Item name="GlobalIgnoreFunctionCallFilteringEnabled" valuePropName="checked" style={{ margin: 0 }}>
                                    <Switch />
                                </Form.Item>
                            </Col>
                        </Row>

                        <Row align="middle" style={{ marginTop: '24px' }}>
                            <Col span={8} style={{ textAlign: 'right', paddingRight: '12px' }}>
                                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                                    {t('relay.globalIgnoreImageSupportFilteringEnabled')}
                                    <Tag color="green" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                        性能优化
                                    </Tag>
                                    <Tooltip title={t('relay.globalIgnoreImageSupportFilteringEnabledTip')}>
                                        <InfoCircleOutlined />
                                    </Tooltip>
                                </span>
                            </Col>
                            <Col span={16}>
                                <Form.Item name="GlobalIgnoreImageSupportFilteringEnabled" valuePropName="checked" style={{ margin: 0 }}>
                                    <Switch />
                                </Form.Item>
                            </Col>
                        </Row>
                    </Card>

                    <Card title={"图片代理设置"} size="small"
                          extra={<>（支持 <a href='https://github.com/Calcium-Ion/new-api-worker' target='_blank'
                                            rel='noreferrer'>new-api-worker</a>）</>}
                    >
                        <Form.Item label={"图片下载和计费开关"}
                                   tooltip={"默认开启,关闭后遇到图片既不下载也不计费,不懂不要随便动"}
                                   name="ImageDownloadEnabled">
                            <Switch checkedChildren={t('开启（默认会下载计算图片费用）')}
                                    unCheckedChildren={t('关闭（图片地址既不下载也不计费）')}/>
                        </Form.Item>
                        <Form.Item label={"图片错误忽略开关"}
                                   tooltip={"遇到图片下载错误时继续请求上游API，不报错也不移除图片。注意：图片报错删除配置会覆盖此设置"}
                                   name="IgnoreImageErrorButRequestEnabled"
                                   style={getHighlightStyle('IgnoreImageErrorButRequestEnabled')}
                                   data-field="IgnoreImageErrorButRequestEnabled">
                            <Switch checkedChildren={t('开启（忽略图片错误，继续请求上游API）')}
                                    unCheckedChildren={t('关闭（图片错误时返回错误）')}/>
                        </Form.Item>
                        <Form.Item label={"Worker地址"} tooltip={"Worker地址，不填写则不启用代理"} name="WorkerUrl">
                            <Input placeholder='例如：https://workername.yourdomain.workers.dev'/>
                        </Form.Item>
                        <Form.Item label={"Worker密钥"} tooltip={"Worker密钥，根据你部署的 Worker 填写"}
                                   name="WorkerValidKey">
                            <Input placeholder={"例如：your_secret_key"}/>
                        </Form.Item>
                    </Card>
                    <Card title={"吃掉拨测"} size="small">
                        <Form.Item label={t('吃掉拨测')} name="Say1DirectSuccessEnabled">
                            <Switch checkedChildren={t('开启（拨测直接返回指定内容）')}
                                    unCheckedChildren={t('关闭')}/>
                        </Form.Item>
                        <Form.Item label={t('拨测匹配键值对JSON格式')} name={'Say1DirectWordsMap'}
                                   rules={[jsonValidationRule]}>
                            <Input.TextArea autoSize={{minRows: 3, maxRows: 5}}/>
                        </Form.Item>
                    </Card>
                    <Card title={"请求截断"} size="small"
                          extra={"请求截断用于截断请求长度，防止过长的请求导致上游服务拒绝响应 优先匹配原始模型,如果没有匹配到则匹配映射后的模型"}
                    >
                        <Form.Item label={t('请求截断')} name="RequestTruncationEnabled">
                            <Switch checkedChildren={t('开启（请求根据配置的Token进行截断）')}
                                    unCheckedChildren={t('关闭（不截断）')}/>
                        </Form.Item>
                        <Form.Item label={t('原始请求截断键值对JSON格式')} name={'RequestTruncationOriginMap'}
                                   rules={[jsonValidationRule]}
                                   extra={'例如：{"gpt-3.5-turbo-16k":4000} 表示截断gpt-3.5-turbo-16k模型的请求长度为4000个Tokens(注意:此模型为映射前的模型)'}
                        >
                            <Input.TextArea autoSize={{minRows: 3, maxRows: 5}}/>
                        </Form.Item>
                        <Form.Item label={t('映射后请求截断键值对JSON格式')} name={'RequestTruncationMap'}
                                   rules={[jsonValidationRule]}
                                   extra={'例如：{"gpt-3.5-turbo-16k":4000} 表示截断gpt-3.5-turbo-16k模型的请求长度为4000个Tokens(注意:此模型为映射后的模型)'}
                        >
                            <Input.TextArea autoSize={{minRows: 3, maxRows: 5}}/>
                        </Form.Item>
                    </Card>
                    <Card title={"重试设置"} size="small">
                        <Form.Item
                            label={t('失败重试次数')}
                            extra={'relay和渠道复活的最大失败重试次数，超出后不再重试'}
                            name="RetryTimes"
                            style={getHighlightStyle('RetryTimes')}
                            data-field="RetryTimes"
                        >
                            <Input type='number' suffix="次"/>
                        </Form.Item>

                        <Form.Item
                            label={t('重启尝试次数')}
                            extra={'熔断后重启的尝试次数，超出后不再重试重启'}
                            name="ReviveRetryTimes"
                            style={getHighlightStyle('ReviveRetryTimes')}
                            data-field="ReviveRetryTimes"
                        >
                            <Input type='number' suffix="次"/>
                        </Form.Item>

                        <Form.Item
                            label={t('排除失败渠道')}
                            name="RetryWithoutFailedChannelEnabled"
                            style={getHighlightStyle('RetryWithoutFailedChannelEnabled')}
                            data-field="RetryWithoutFailedChannelEnabled"
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            label={t('隐藏报错信息')}
                            name="HideRelayErrorEnabled"
                            style={getHighlightStyle('HideRelayErrorEnabled')}
                            data-field="HideRelayErrorEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        {isHideRelayErrorEnabled &&
                            <Form.Item
                                label='隐藏报错信息例外'
                                name="HideRelayErrorExceptList"
                                extra={'输入报错信息文字添加到例外'}
                            >
                                <Select mode="tags" options={[]}/>
                            </Form.Item>
                        }

                        <Form.Item
                            label={t('统一上游错误类型')}
                            name="HideUpstreamApiTypeErrorEnabled"
                            tooltip={'开启后,所有上游的 *_api_error 类型错误将统一显示为 shell_api_error'}
                        >
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            label={'API错误类型后缀'}
                            name="ApiErrorTypeSuffix"
                            tooltip={'用于匹配API错误类型的后缀，默认为"_api_error"，所有以此为后缀的错误类型都会被替换为shell_api_error'}
                        >
                            <Input placeholder="_api_error"/>
                        </Form.Item>

                        <Form.Item
                            label={'自定义屏蔽API错误类型'}
                            name="CustomHideApiErrorTypes"
                            tooltip={'自定义需要被统一替换为shell_api_error的错误类型，JSON数组格式，例如：["custom_error_type", "other_error_type"]'}
                            rules={[jsonValidationRule]}
                        >
                            <Input.TextArea 
                                placeholder='["custom_error_type", "other_error_type"]'
                                autoSize={{minRows: 2, maxRows: 5}}
                            />
                        </Form.Item>

                        <Form.Item label={t('响应错误依旧计费')}
                                   extra={"主要用于非流式超时导致的上游计费但自身未计费的改善"}
                                   name="ResponseErrorStillChargeEnabled">
                            <Switch/>
                        </Form.Item>

                        <Form.Item
                            label={
                                <span style={{ display: 'inline-flex', alignItems: 'center' }}>
                                    重试保持计费类型一致性
                                    <Tag color="orange" style={{ marginLeft: '8px', marginRight: '8px', fontSize: '12px' }}>
                                        计费优化
                                    </Tag>
                                    <InfoCircleOutlined style={{ marginLeft: '4px', color: '#1890ff' }} />
                                </span>
                            }
                            name="RetryKeepBillingTypeEnabled"
                            tooltip="控制重试时是否保持与第一次请求相同的计费类型，避免混合计费导致的用户困惑。开启后，如果第一次请求使用按量计费渠道，重试时只会选择按量计费渠道。适用于同一分组内同时存在按量和按次计费渠道的场景。"
                            extra={
                                <div>
                                    <div style={{marginBottom: '4px'}}>
                                        控制重试时是否保持与第一次请求相同的计费类型，避免混合计费导致的用户困惑。
                                        <Button type="link" onClick={() => setIsRetryBillingModalOpen(true)}
                                                style={{padding: '0 4px'}}>
                                            查看详情
                                        </Button>
                                    </div>
                                    <div style={{color: '#faad14', fontSize: '12px'}}>
                                        适用于同一分组内同时存在按量和按次计费渠道的场景，建议开启以确保计费透明度。
                                    </div>
                                </div>
                            }
                            style={getHighlightStyle('RetryKeepBillingTypeEnabled')}
                            data-field="RetryKeepBillingTypeEnabled"
                        >
                            <Switch 
                                checkedChildren={t('开启（重试保持计费类型）')}
                                unCheckedChildren={t('关闭（允许降级计费类型）')}
                            />
                        </Form.Item>
                    </Card>

                    <Card title={"监控设置"} size="small">
                        <Form.Item
                            label={
                                <Tooltip title="当渠道测试失败时，系统会自动禁用该渠道，防止继续使用故障渠道">
                                    {t('失败时禁用渠道')} <InfoCircleOutlined/>
                                </Tooltip>
                            }
                            name="AutomaticDisableChannelEnabled"
                            style={getHighlightStyle('AutomaticDisableChannelEnabled')}
                            data-field="AutomaticDisableChannelEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            label={
                                <Tooltip title="当之前被禁用的渠道测试成功时，系统会自动重新启用该渠道">
                                    {t('成功时启用通道')} <InfoCircleOutlined/>
                                </Tooltip>
                            }
                            name="AutomaticEnableChannelEnabled"
                            style={getHighlightStyle('AutomaticEnableChannelEnabled')}
                            data-field="AutomaticEnableChannelEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            label={
                                <Tooltip
                                    title="在批量测试所有渠道时，如果某个渠道的响应时间超过此阈值，将被自动禁用。设置为0则不启用此功能">
                                    {t('最长响应时间')} <InfoCircleOutlined/>
                                </Tooltip>
                            }
                            name="ChannelDisableThreshold"
                            style={getHighlightStyle('ChannelDisableThreshold')}
                            data-field="ChannelDisableThreshold"
                        >
                            <Input type='number' suffix={"秒"}/>
                        </Form.Item>
                        <p style={{color: '#faad14', marginTop: '10px'}}>
                            注意：这些设置会影响渠道测试功能的行为。请确保您理解每个选项的作用before启用它们。
                        </p>
                    </Card>

                    <Card title='熔断/禁用/重试个性化设置' size="small"
                          extra='个性化设置用于覆盖系统默认的熔断禁用逻辑,优先触发熔断,熔断未触发则尝试触发禁用'>
                        <Form.Item
                            label={'失败时拆分模型禁用'}
                            tooltip={'开启后请求失败时会根据模型拆分禁用,而不是禁用整个渠道'}
                            name="ChannelAbilityDisableEnabled"
                            style={getHighlightStyle('ChannelAbilityDisableEnabled')}
                            data-field="ChannelAbilityDisableEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            label={'relay error 强制重试设置'}
                            tooltip={'开启后一旦报错信息命中将根据优先级强制重试其他渠道'}
                            name="RelayErrForceRetryKeywordEnabled"
                            style={getHighlightStyle('RelayErrForceRetryKeywordEnabled')}
                            data-field="RelayErrForceRetryKeywordEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        {isRelayErrForceRetryKeywordEnabled &&
                            <>
                                <Form.Item
                                    label='强制重试命中模型'
                                    name="RelayErrForceRetryModelList"
                                    extra={'输入模型名字全称(用户请求的名称,不是映射后的名称),回车添加。使用"*"表示匹配所有模型'}
                                    style={getHighlightStyle('RelayErrForceRetryModelList')}
                                    data-field="RelayErrForceRetryModelList"
                                >
                                    <Select 
                                        mode="tags" 
                                        options={[
                                            { label: '所有模型 (*)', value: '*' }
                                        ]}
                                        placeholder="输入模型名称或选择'所有模型'"
                                        onChange={(value) => {
                                            if (value.includes('*')) {
                                                // 如果选择了通配符，只保留通配符
                                                form.setFieldsValue({ RelayErrForceRetryModelList: ['*'] });
                                            }
                                        }}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label='强制重试命中内容'
                                    name="RelayErrForceRetryKeywordList"
                                    extra={'输入err message 命中内容，回车添加。使用"*"表示匹配所有错误信息'}
                                    style={getHighlightStyle('RelayErrForceRetryKeywordList')}
                                    data-field="RelayErrForceRetryKeywordList"
                                >
                                    <Select 
                                        mode="tags" 
                                        options={[
                                            { label: '所有错误信息 (*)', value: '*' }
                                        ]}
                                        placeholder="输入错误信息关键词或选择'所有错误信息'"
                                        onChange={(value) => {
                                            if (value.includes('*')) {
                                                // 如果选择了通配符，只保留通配符
                                                form.setFieldsValue({ RelayErrForceRetryKeywordList: ['*'] });
                                            }
                                        }}
                                    />
                                </Form.Item>
                            </>
                        }
                        
                        <Form.Item
                            label={'relay error 强制抛出设置'}
                            tooltip={'开启后一旦报错信息命中将直接抛出错误，优先级高于强制重试。如果两者都设置且都命中，将直接抛出错误而不会重试'}
                            name="RelayErrForceThrowErrorEnabled"
                            style={getHighlightStyle('RelayErrForceThrowErrorEnabled')}
                            data-field="RelayErrForceThrowErrorEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        {isRelayErrForceThrowErrorEnabled &&
                            <>
                                <Form.Item
                                    label='强制抛出命中模型'
                                    name="RelayErrForceThrowErrorModelList"
                                    extra={'输入模型名字全称(用户请求的名称,不是映射后的名称),回车添加。使用"*"表示匹配所有模型'}
                                    style={getHighlightStyle('RelayErrForceThrowErrorModelList')}
                                    data-field="RelayErrForceThrowErrorModelList"
                                >
                                    <Select 
                                        mode="tags" 
                                        options={[
                                            { label: '所有模型 (*)', value: '*' }
                                        ]}
                                        placeholder="输入模型名称或选择'所有模型'"
                                        onChange={(value) => {
                                            if (value.includes('*')) {
                                                // 如果选择了通配符，只保留通配符
                                                form.setFieldsValue({ RelayErrForceThrowErrorModelList: ['*'] });
                                            }
                                        }}
                                    />
                                </Form.Item>
                                <Form.Item
                                    label='强制抛出命中内容'
                                    name="RelayErrForceThrowErrorKeywordList"
                                    extra={'输入err message 命中内容，回车添加。使用"*"表示匹配所有错误信息'}
                                    style={getHighlightStyle('RelayErrForceThrowErrorKeywordList')}
                                    data-field="RelayErrForceThrowErrorKeywordList"
                                >
                                    <Select 
                                        mode="tags" 
                                        options={[
                                            { label: '所有错误信息 (*)', value: '*' }
                                        ]}
                                        placeholder="输入错误信息关键词或选择'所有错误信息'"
                                        onChange={(value) => {
                                            if (value.includes('*')) {
                                                // 如果选择了通配符，只保留通配符
                                                form.setFieldsValue({ RelayErrForceThrowErrorKeywordList: ['*'] });
                                            }
                                        }}
                                    />
                                </Form.Item>
                            </>
                        }
                        
                        <Form.Item
                            label={'个性化熔断设置'}
                            tooltip={'开启后一旦命中会导致不死模式-1模式失效'}
                            name="CustomCircuitBreakerEnabled"
                            style={getHighlightStyle('CustomCircuitBreakerEnabled')}
                            data-field="CustomCircuitBreakerEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        {isCustomCircuitBreakerEnabled &&
                            <Form.Item
                                label='熔断的http响应状态码'
                                name="CircuitBreakerHttpStatusCodeList"
                                extra={'输入http响应码数字，回车添加 一旦触发列表中的状态码直接熔断会导致不死模式-1失效'}
                                style={getHighlightStyle('CircuitBreakerHttpStatusCodeList')}
                                data-field="CircuitBreakerHttpStatusCodeList"
                            >
                                <Select mode="tags" options={[
                                    {label: '307', value: '307'},
                                    {label: '308', value: '308'},
                                    {label: '429', value: '429'},
                                    {label: '500', value: '500'},
                                    {label: '502', value: '502'},
                                    {label: '503', value: '503'},
                                    {label: '504', value: '504'}
                                ]}/>
                            </Form.Item>
                        }
                        <Form.Item
                            label={'个性化禁用设置'}
                            tooltip={'开启后，命中会导致不死模式（-1模式）失效'}
                            name="CustomDisableChannelEnabled"
                            style={getHighlightStyle('CustomDisableChannelEnabled')}
                            data-field="CustomDisableChannelEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        {isCustomDisableChannelEnabled &&
                            <Form.Item
                                label='禁用的http响应状态码'
                                name="DisableChannelHttpStatusCodeList"
                                extra={'输入http响应码数字，回车添加，触发列表中的状态码直接禁用会导致不死模式（-1模式）失效'}
                                style={getHighlightStyle('DisableChannelHttpStatusCodeList')}
                                data-field="DisableChannelHttpStatusCodeList"
                            >
                                <Select
                                    mode="tags"
                                    options={[
                                        {label: '307', value: '307'},
                                        {label: '308', value: '308'},
                                        {label: '429', value: '429'},
                                        {label: '500', value: '500'},
                                        {label: '502', value: '502'},
                                        {label: '503', value: '503'},
                                        {label: '504', value: '504'}
                                    ]}
                                />
                            </Form.Item>
                        }
                        
                        <Form.Item
                            label={
                                <Tooltip title="开启后，根据错误消息中的关键词禁用整个渠道，适用于账号被封禁的场景">
                                    禁用整个渠道关键词 <Tag color="red">危险</Tag> <InfoCircleOutlined/>
                                </Tooltip>
                            }
                            name="DisableEntireChannelKeywordsEnabled"
                            extra="当错误中包含指定关键词时直接禁用整个渠道（而非仅禁用能力），该渠道不会自动重启"
                            style={getHighlightStyle('DisableEntireChannelKeywordsEnabled')}
                            data-field="DisableEntireChannelKeywordsEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        
                        {isDisableEntireChannelKeywordsEnabled &&
                            <Form.Item
                                label='整个渠道禁用关键词'
                                name="DisableEntireChannelKeywords"
                                extra='输入关键词，回车添加。当错误消息包含这些关键词时，整个渠道将被禁用而不会尝试重启'
                                style={getHighlightStyle('DisableEntireChannelKeywords')}
                                data-field="DisableEntireChannelKeywords"
                            >
                                <Select
                                    mode="tags"
                                    options={[
                                        {label: '账号已被封禁', value: '账号已被封禁'},
                                        {label: 'account has been suspended', value: 'account has been suspended'},
                                        {label: 'account has been banned', value: 'account has been banned'},
                                        {label: 'account has been disabled', value: 'account has been disabled'},
                                        {label: 'This organization has been disabled', value: 'This organization has been disabled'},
                                        {label: 'account has been deactivated', value: 'account has been deactivated'},
                                        {label: 'API key has been deactivated', value: 'API key has been deactivated'},
                                        {
                                            label: 'The OpenAI account associated with this API key has been deactivated',
                                            value: 'The OpenAI account associated with this API key has been deactivated'
                                        }
                                    ]}
                                    placeholder="输入关键词并回车"
                                />
                            </Form.Item>
                        }
                    </Card>
                    <Card title='请求替换设置' size="small" extra='仅对chat格式一部分文本类模型有效'>
                        <Form.Item
                            label={<>
                                请求最大兼容模式 <Tag color="warning">Beta</Tag>
                            </>}
                            name="RequestMaxCompatibilityEnabled"
                            style={getHighlightStyle('RequestMaxCompatibilityEnabled')}
                            data-field="RequestMaxCompatibilityEnabled"
                            extra={
                                <div>
                                    <div style={{marginBottom: '4px'}}>
                                        支持多种格式转换和兼容性处理，包括旧版API格式、非标准客户端等。
                                        <Button type="link" onClick={() => setIsModalOpen(true)}
                                                style={{padding: '0 4px'}}>
                                            查看详情
                                        </Button>
                                    </div>
                                    <div style={{color: '#faad14', fontSize: '12px'}}>
                                        注意：启用后会对请求进行格式转换，可能略微增加CPU占用。
                                    </div>
                                </div>
                            }
                        >
                            <Switch/>
                        </Form.Item>
                        
                        <Form.Item
                            label={t('模拟官方响应格式')}
                            tooltip={'启用后将严格按照OpenAI最新响应格式返回，包括system_fingerprint等字段。用户可单独配置覆盖此设置。注意：启用后会覆盖掉所有伪造原始模型响应之类的设置！'}
                            name="MockOpenAICompleteFormatEnabled"
                            style={getHighlightStyle('MockOpenAICompleteFormatEnabled')}
                            data-field="MockOpenAICompleteFormatEnabled"
                            extra={<div style={{ color: '#ff4d4f' }}>注意：启用此功能会覆盖掉所有伪造原始模型响应之类的设置！</div>}
                        >
                            <Switch checkedChildren={'开启'} unCheckedChildren={'关闭'}/>
                        </Form.Item>
                        
                        <Form.Item
                            label={'文件链接解析'}
                            name="ParseFileUrlEnabled"
                            tooltip={'开启后会解析文本中的文件链接file_url,并将文件内容替换到文本中 [\n' +
                                '    {\n' +
                                '        "type": "text",\n' +
                                '        "text": "这是啥"\n' +
                                '    },\n' +
                                '    {\n' +
                                '        "type": "file_url",\n' +
                                '        "file_url": {\n' +
                                '            "url": "http://xxxx.pdf",\n' +
                                '            "name": "code.pdf",\n' +
                                '            "type": "application/pdf"\n' +
                                '        }\n' +
                                '    }\n' +
                                ']'}
                            style={getHighlightStyle('ParseFileUrlEnabled')}
                            data-field="ParseFileUrlEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            label={'空提示词替换'}
                            name="EmptyPromptReplaceEnabled"
                            tooltip={'有些人喜欢一直用空的提示词请求,导致一直报错,系统浪费了很多流量,所以可以开启这个开关,替换空请求为hello之类的'}
                            style={{
                                ...getHighlightStyle('EmptyPromptReplaceEnabled'),
                                position: 'relative',  // 确保元素可以正确定位
                                scrollMargin: '100px', // 添加滚动边距
                                padding: '8px',        // 添加内边距使高亮效果更明显
                            }}
                            data-field="EmptyPromptReplaceEnabled"
                            className="highlight-item" // 添加类名以便更容易定位
                        >
                            <Switch/>
                        </Form.Item>
                        {isEmptyPromptReplaceEnabled &&
                            <Form.Item
                                label='替换后的内容'
                                name={'EmptyPromptReplaceContent'}
                                style={getHighlightStyle('EmptyPromptReplaceContent')}
                                data-field="EmptyPromptReplaceContent"
                            >
                                <Input/>
                            </Form.Item>
                        }
                    </Card>

                    <Card title={'MaxToken自动检测'} size="small"
                          extra={<span>自动检测并回退到模型支持的最大token数</span>}>
                        <Form.Item
                            label='启用MaxToken自动检测'
                            name="MaxTokenAutoDetectionEnabled"
                            style={getHighlightStyle('MaxTokenAutoDetectionEnabled')}
                            data-field="MaxTokenAutoDetectionEnabled"
                            extra="启用后，当用户传入的max_tokens超过模型限制时，系统会自动调整为模型支持的最大值。只有配置的模型才会进行token限制检查，未配置的模型保持用户原始请求参数。"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item
                            label='模型Token限制配置'
                            name="ModelMaxTokensConfig"
                            style={getHighlightStyle('ModelMaxTokensConfig')}
                            data-field="ModelMaxTokensConfig"
                            extra={
                                <div>
                                    <div>JSON格式配置各模型的最大token限制。只有配置的模型才会进行token限制检查和覆盖，未配置的模型保持用户原始请求参数。</div>
                                    <div style={{marginTop: 8}}>
                                        <Button
                                            type="link"
                                            size="small"
                                            onClick={() => {
                                                const sampleConfig = {
                                                    // Claude 模型
                                                    "claude-3-5-sonnet-20241022": 8192,
                                                    "claude-3-5-sonnet-20240620": 8192,
                                                    "claude-3-5-haiku-20241022": 8192,
                                                    "claude-3-opus-20240229": 4096,
                                                    "claude-3-sonnet-20240229": 4096,
                                                    "claude-3-haiku-20240307": 4096,
                                                    "claude-3-7-sonnet-20250219": 8192,
                                                    "claude-3-7-sonnet-20250219-thinking": 8192,

                                                    // GPT 模型
                                                    "gpt-4": 8192,
                                                    "gpt-4-0613": 8192,
                                                    "gpt-4-32k": 32768,
                                                    "gpt-4-32k-0613": 32768,
                                                    "gpt-4-1106-preview": 4096,
                                                    "gpt-4-0125-preview": 4096,
                                                    "gpt-4-turbo": 4096,
                                                    "gpt-4-turbo-2024-04-09": 4096,
                                                    "gpt-4o": 16384,
                                                    "gpt-4o-2024-08-06": 16384,
                                                    "gpt-4o-2024-11-20": 16384,
                                                    "gpt-4o-2024-05-13": 16384,
                                                    "chatgpt-4o-latest": 16384,
                                                    "gpt-4o-mini": 16384,
                                                    "gpt-4o-mini-2024-07-18": 16384,
                                                    "gpt-3.5-turbo": 4096,
                                                    "gpt-3.5-turbo-0125": 4096,
                                                    "gpt-3.5-turbo-1106": 4096,
                                                    "gpt-3.5-turbo-16k": 16384,
                                                    "gpt-3.5-turbo-16k-0613": 16384,

                                                    // O1 模型 (使用max_completion_tokens)
                                                    "o1-preview": 32768,
                                                    "o1-preview-2024-09-12": 32768,
                                                    "o1-mini": 65536,
                                                    "o1-mini-2024-09-12": 65536,
                                                    "o1": 100000,
                                                    "o1-2024-12-17": 100000,
                                                    "o3-mini": 65536,
                                                    "o3-mini-2025-01-31": 65536,

                                                    // Gemini 模型
                                                    "gemini-pro": 8192,
                                                    "gemini-pro-vision": 8192,
                                                    "gemini-1.5-pro": 8192,
                                                    "gemini-1.5-flash": 8192,
                                                    "gemini-2.0-flash-exp": 8192
                                                };
                                                form.setFieldsValue({
                                                    ModelMaxTokensConfig: JSON.stringify(sampleConfig, null, 2)
                                                });
                                            }}
                                        >
                                            填入完整样例配置
                                        </Button>
                                        <Button
                                            type="link"
                                            size="small"
                                            onClick={() => {
                                                form.setFieldsValue({ModelMaxTokensConfig: ""});
                                            }}
                                        >
                                            清空配置
                                        </Button>
                                    </div>
                                </div>
                            }
                            rules={[
                                {
                                    validator: (_, value) => {
                                        if (!value || value.trim() === '') {
                                            return Promise.resolve();
                                        }
                                        try {
                                            const parsed = JSON.parse(value);
                                            if (typeof parsed !== 'object' || Array.isArray(parsed)) {
                                                return Promise.reject(new Error('配置必须是JSON对象格式'));
                                            }
                                            for (const [key, val] of Object.entries(parsed)) {
                                                if (typeof val !== 'number' || val <= 0) {
                                                    return Promise.reject(new Error(`模型 "${key}" 的token限制必须是正整数`));
                                                }
                                            }
                                            return Promise.resolve();
                                        } catch (e) {
                                            return Promise.reject(new Error('JSON格式错误'));
                                        }
                                    }
                                }
                            ]}
                        >
                            <Input.TextArea
                                autoSize={{minRows: 4, maxRows: 12}}
                                placeholder='{"gpt-4": 8192, "claude-3-5-sonnet-20241022": 8192, "gpt-4o": 16384}'
                            />
                        </Form.Item>
                    </Card>

                    <Card title={"Claude 消息整理"} size="small" extra="针对 /v1/messages 端点的 Claude 官方格式消息整理功能">
                        <Form.Item
                            label={
                                <Tooltip title={
                                    <div>
                                        <p><b>Claude 消息整理功能说明：</b></p>
                                        <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>
                                            <li>自动修复 max_tokens 参数错误（范围：1-200000）</li>
                                            <li>处理空的或无效的 messages 字段</li>
                                            <li>验证和修复 model 字段为有效的 Claude 模型名</li>
                                            <li>规范化 temperature 参数（范围：0.0-1.0）</li>
                                            <li>清理无效的 tools 配置</li>
                                            <li>处理 thinking 模式参数</li>
                                            <li>移除超过 5MB 限制的图片</li>
                                            <li>修复空的文本内容块</li>
                                        </ul>
                                        <p style={{ color: '#faad14', margin: '8px 0' }}>
                                            <b>常见错误处理：</b>
                                        </p>
                                        <ul style={{ paddingLeft: '20px', margin: '8px 0' }}>
                                            <li>"maximum allowed number of output tokens" → 自动调整 max_tokens</li>
                                            <li>"invalid model name" → 设置为默认 Claude 模型</li>
                                            <li>"field messages is required" → 添加默认消息</li>
                                            <li>"image exceeds 5 MB maximum" → 移除过大图片</li>
                                            <li>"text content blocks must be non-empty" → 填充默认内容</li>
                                            <li>"temperature: range" → 调整到有效范围</li>
                                        </ul>
                                        <p style={{ color: '#52c41a', margin: '8px 0' }}>
                                            <b>适用场景：</b> 提高 Claude API 请求的成功率，减少格式错误导致的请求失败
                                        </p>
                                    </div>
                                }>
                                    Claude 消息整理功能 <InfoCircleOutlined/>
                                </Tooltip>
                            }
                            name="ClaudeMessageNormalizationEnabled"
                            style={getHighlightStyle('ClaudeMessageNormalizationEnabled')}
                            data-field="ClaudeMessageNormalizationEnabled"
                        >
                            <Switch
                                checkedChildren="开启（自动整理）"
                                unCheckedChildren="关闭"
                            />
                        </Form.Item>

                        <div style={{
                            background: '#f6ffed',
                            border: '1px solid #b7eb8f',
                            borderRadius: '6px',
                            padding: '12px',
                            margin: '16px 0'
                        }}>
                            <p style={{ margin: '0 0 8px 0', fontWeight: 'bold', color: '#389e0d' }}>
                                💡 功能特点：
                            </p>
                            <ul style={{ margin: 0, paddingLeft: '20px' }}>
                                <li>仅对 /v1/messages 路径的 Claude 请求生效</li>
                                <li>支持用户级别配置覆盖全局设置</li>
                                <li>智能识别和修复常见的 Claude API 格式错误</li>
                                <li>提供友好的中文错误提示信息</li>
                                <li>不影响其他 API 端点的正常使用</li>
                            </ul>
                        </div>
                    </Card>

                    <Card title={"流式响应设置"} size="small" extra="控制流式响应的处理方式和用量统计">
                        <Form.Item
                            label="信任上游流式用量"
                            name="TrustUpstreamStreamUsageEnabled"
                            tooltip="开启后将完全信任上游流式返回的用量统计，关闭则重新计算。注意：非流式模式默认信任上游返回，如需重新计算请在特定渠道中设置"
                            style={getHighlightStyle('TrustUpstreamStreamUsageEnabled')}
                            data-field="TrustUpstreamStreamUsageEnabled"
                        >
                            <Switch 
                                checkedChildren="开启（信任上游统计）" 
                                unCheckedChildren="关闭（重新计算）"
                            />
                        </Form.Item>

                        <Form.Item
                            label="强制上游返回用量"
                            name="ForceStreamOptionEnabled"
                            tooltip="开启后将强制要求上游在流式响应中返回用量统计信息，可能会增加响应延迟"
                            style={getHighlightStyle('ForceStreamOptionEnabled')}
                            data-field="ForceStreamOptionEnabled"
                        >
                            <Switch 
                                checkedChildren="开启（强制返回用量）" 
                                unCheckedChildren="关闭"
                            />
                        </Form.Item>

                        <Form.Item
                            label="强制下游流式用量"
                            name="ForceDownstreamStreamUsageEnabled"
                            tooltip="开启后将在流式响应中强制返回用量统计信息给下游，可能会增加响应延迟"
                            style={getHighlightStyle('ForceDownstreamStreamUsageEnabled')}
                            data-field="ForceDownstreamStreamUsageEnabled"
                        >
                            <Switch 
                                checkedChildren="开启（强制返回用量）" 
                                unCheckedChildren="关闭"
                            />
                        </Form.Item>
                    </Card>

                    <Button type="primary" htmlType="submit" block>提交</Button>
                </Space>
            </Form>
            <Modal
                title="请求最大兼容模式说明"
                open={isModalOpen}
                onCancel={() => setIsModalOpen(false)}
                footer={null}
                width={600}
            >
                <CompatibilityModeDetails/>
            </Modal>
            <Modal
                title="重试保持计费类型一致性说明"
                open={isRetryBillingModalOpen}
                onCancel={() => setIsRetryBillingModalOpen(false)}
                footer={null}
                width={600}
            >
                <RetryBillingTypeDetails/>
            </Modal>
        </Spin>
    );
}

export default React.memo(RelaySetting);