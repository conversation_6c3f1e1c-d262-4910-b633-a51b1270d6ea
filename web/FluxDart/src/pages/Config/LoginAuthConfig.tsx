//LoginAuthConfig

import React, {useEffect, useState, CSSProperties} from "react";
import {App, Button, Card, Form, Input, Popover, Space, Spin, Switch, Alert} from "antd";
import {useTranslation} from "react-i18next";
import {getAllSettings, updateSettings} from "../../helpers/api-request-module";
import {configFormProps} from "../../constants";
import type {ConfigComponentProps} from './types';

const LoginAuthConfig: React.FC<ConfigComponentProps> = ({highlightConfig}) => {
    const [data, setData] = useState<any>([]);
    const [loading, setLoading] = useState(true);
    const [submitting, setSubmitting] = useState(false);
    const [form] = Form.useForm();
    const {t} = useTranslation();
    const {message: AntdMessage} = App.useApp()

    useEffect(() => {
        getAllSettings().then((res) => {
            setData(res);
            form.setFieldsValue(res);
            setLoading(false);
        });
    }, []);

    useEffect(() => {
        if (highlightConfig) {
            setTimeout(() => {
                const element = document.querySelector(`[data-field="${highlightConfig}"]`);
                if (element) {
                    element.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                    });
                    
                    element.classList.add('flash-highlight');
                    setTimeout(() => {
                        element.classList.remove('flash-highlight');
                    }, 2000);
                }
            }, 100);
        }
    }, [highlightConfig]);

    const getHighlightStyle = (fieldName: string): CSSProperties => {
        const isHighlighted = highlightConfig === fieldName;
        if (isHighlighted) {
            return {
                position: 'relative',
                animation: 'highlight-pulse 2s infinite',
                backgroundColor: '#ffd70033',
                padding: '16px',
                margin: '8px 0',
                borderRadius: '8px',
                border: '2px solid #ffd700',
                boxShadow: '0 0 10px rgba(255, 215, 0, 0.3)',
                transform: 'scale(1.02)',
                zIndex: 1
            };
        }
        return {};
    };

    const howToUseTelegramBot = (
        <>
            <p>1. 在 Telegram 中搜索 BotFather 并发送 /newbot 创建一个新的 Bot</p>
            <p>2. 输入你的 Bot 名称</p>
            <p>3. 输入你的 Bot 用户名，这个用户名就是需要填入的[Telegram Bot Name]</p>
            <p>4. 在成功创建后，BotFather 会返回一个 Token，这个 Token 就是需要填入的[Tegegram Bot Token]</p>
            <p>5. 发送 /setdomain 命令</p>
            <p>6. 等待 BotFather 回复后，发送你的域名，例如：api.example.com</p>
        </>
    )

    const onFinish = async (values: any) => {
        if (submitting) return;
        setSubmitting(true);
        const keys = Object.keys(values);
        const needUpdate = keys.filter(key => values[key] !== data[key]);
        const needUpdateData = needUpdate.map(key => ({key, value: values[key]}));
        if (needUpdateData.length === 0) {
            AntdMessage.info({key: 'info', content: '没有需要更新的数据', duration: 2}).then();
            setSubmitting(false);
            return;
        }
        AntdMessage.loading({key: 'submit', content: '正在提交...', duration: 0}).then();
        needUpdateData.forEach((item) => {
            if (item.key.endsWith('Enabled')) {
                item.value = item.value ? 'true' : 'false';
            }
        });
        await updateSettings(needUpdateData).then(({success, message}) => {
            if (success) {
                AntdMessage.success({key: 'submit', content: '提交成功！', duration: 1}).then();
            } else {
                AntdMessage.error({key: 'submit', content: '提交失败！' + message, duration: 2}).then();
            }
        });
        setSubmitting(false);
        await getAllSettings().then((res) => setData(res));
    }

    return (
        <Spin spinning={loading}>
            <Form
                {...configFormProps}
                form={form}
                onFinish={onFinish}
                name={'LoginAuthConfig'}
            >
                <Space direction="vertical" style={{width: "100%"}}>
                    <Card title="GitHub OAuth"
                          extra={<span>用以支持通过 GitHub 进行登录注册，<a href="https://github.com/settings/developers"
                                                                   rel="noreferrer"
                                                                   target="_blank">点击此处</a>管理你的 GitHub OAuth App</span>}
                          size="small">
                        <Form.Item label='GitHub登录' name="GitHubOAuthEnabled" style={getHighlightStyle('GitHubOAuthEnabled')} data-field="GitHubOAuthEnabled">
                            <Switch/>
                        </Form.Item>
                        <Form.Item label=" " colon={false}>
                            <Alert
                                message={
                                    <>
                                        Homepage URL 填 <b>{data.ServerAddress}</b>，
                                        Authorization callback URL 填 <b>{`${data.ServerAddress}/oauth/github`}</b>
                                    </>
                                }
                                type="info"
                                showIcon
                                style={{ marginBottom: '16px' }}
                            />
                        </Form.Item>
                        <Form.Item label="GitHub Client ID" name={"GitHubClientId"} style={getHighlightStyle('GitHubClientId')} data-field="GitHubClientId">
                            <Input/>
                        </Form.Item>
                        <Form.Item label="GitHub Client Secret" name={"GitHubClientSecret"} style={getHighlightStyle('GitHubClientSecret')} data-field="GitHubClientSecret">
                            <Input.Password placeholder="敏感信息不会发送到前端显示" autoComplete="new-password"/>
                        </Form.Item>
                    </Card>

                    <Card title="Google OAuth"
                          extra={<span>用以支持通过 Google 进行登录注册，<a href="https://console.cloud.google.com/apis/credentials"
                                                                   rel="noreferrer"
                                                                   target="_blank">点击此处</a>管理你的 Google OAuth App</span>}
                          size="small">
                        <Form.Item label='Google登录' name="GoogleOAuthEnabled" style={getHighlightStyle('GoogleOAuthEnabled')} data-field="GoogleOAuthEnabled">
                            <Switch/>
                        </Form.Item>
                        <Form.Item label=" " colon={false}>
                            <Alert
                                message={
                                    <>
                                        授权重定向 URI 填 <b>{`${data.ServerAddress}/oauth/google`}</b>
                                    </>
                                }
                                type="info"
                                showIcon
                                style={{ marginBottom: '16px' }}
                            />
                        </Form.Item>
                        <Form.Item label="Google Client ID" name={"GoogleClientId"} style={getHighlightStyle('GoogleClientId')} data-field="GoogleClientId">
                            <Input/>
                        </Form.Item>
                        <Form.Item label="Google Client Secret" name={"GoogleClientSecret"} style={getHighlightStyle('GoogleClientSecret')} data-field="GoogleClientSecret">
                            <Input.Password placeholder="敏感信息不会发送到前端显示" autoComplete="new-password"/>
                        </Form.Item>
                    </Card>

                    <Card
                        title="WeChat Server"
                        extra={<span>用以支持通过微信进行登录注册，<a
                            href="https://github.com/songquanpeng/wechat-server" rel="noreferrer"
                            target="_blank">点击此处</a>查看详情</span>}
                        size="small"
                    >
                        <Form.Item label='微信登录' name="WeChatAuthEnabled" style={getHighlightStyle('WeChatAuthEnabled')} data-field="WeChatAuthEnabled">
                            <Switch/>
                        </Form.Item>
                        <Form.Item label="WeChat Server 地址" name="WeChatServerAddress" style={getHighlightStyle('WeChatServerAddress')} data-field="WeChatServerAddress">
                            <Input placeholder="例如：https://example.com"/>
                        </Form.Item>
                        <Form.Item label="公众号二维码链接" name="WeChatAccountQRCodeImageURL" style={getHighlightStyle('WeChatAccountQRCodeImageURL')} data-field="WeChatAccountQRCodeImageURL" rules={[{type: 'url', message: '请输入正确的URL', warningOnly: true}]}>
                            <Input placeholder="输入一个图片链接，例如：https://example.com/qrcode.jpg"/>
                        </Form.Item>
                        <Form.Item label="WeChat Server访问凭证" name="WeChatServerToken" style={getHighlightStyle('WeChatServerToken')} data-field="WeChatServerToken">
                            <Input.Password placeholder="敏感信息不会发送到前端显示" autoComplete="new-password"/>
                        </Form.Item>
                    </Card>

                    <Card title="Telegram OAuth" size="small"
                          extra={<span>用以支持通过 Telegram 进行登录注册，<Popover content={howToUseTelegramBot}
                                                                           title={'如何使用 Telegram Bot'}><a>配置方法</a></Popover></span>}>
                        <Form.Item label='Telegram登录' name="TelegramOAuthEnabled" style={getHighlightStyle('TelegramOAuthEnabled')} data-field="TelegramOAuthEnabled">
                            <Switch/>
                        </Form.Item>
                        <Form.Item label="Telegram Bot Name" name="TelegramBotName" style={getHighlightStyle('TelegramBotName')} data-field="TelegramBotName">
                            <Input placeholder='Telegram Bot 名称'/>
                        </Form.Item>
                        <Form.Item label="Telegram Bot Token" name="TelegramBotToken" style={getHighlightStyle('TelegramBotToken')} data-field="TelegramBotToken">
                            <Input.Password placeholder="敏感信息不会发送到前端显示" autoComplete="new-password"/>
                        </Form.Item>
                    </Card>

                    <Card title="飞书授权登录" size="small" extra={
                        <>
                            <span>
                               用以支持通过飞书进行登录注册，
                               <a href="https://open.feishu.cn/app" rel="noreferrer" target="_blank">点击此处</a>
                               管理你的飞书应用，
                            </span>
                            <Popover
                                title="如何配置飞书应用"
                                content={
                                    <>
                                       主页链接填 <code>{data.ServerAddress}</code>，
                                       重定向 URL 填 <code>{`${data.ServerAddress}/oauth/lark`}</code>
                                    </>
                                }
                            >
                                <a>配置方法</a>
                            </Popover>
                        </>
                    }>
                        <Form.Item 
                            label='App ID' 
                            name="LarkClientId"
                            style={getHighlightStyle('LarkClientId')}
                            data-field="LarkClientId"
                        >
                            <Input placeholder="请输入飞书应用的 App ID"/>
                        </Form.Item>
                        <Form.Item 
                            label="App Secret" 
                            name="LarkClientSecret"
                            style={getHighlightStyle('LarkClientSecret')}
                            data-field="LarkClientSecret"
                        >
                            <Input.Password placeholder="敏感信息不会发送到前端显示" autoComplete="new-password"/>
                        </Form.Item>
                    </Card>

                    <Card title="短信验证码" size="small"
                          extra={<span>实验性功能，仅支持阿里云短信服务，<a href="https://dysms.console.aliyun.com"
                                                                  rel="noreferrer"
                                                                  target="_blank">点击此处</a>查看文档</span>}>
                        <Form.Item 
                            label='总开关' 
                            name="SMSVerificationEnabled" 
                            extra={'请提交配置后再开启'}
                            style={getHighlightStyle('SMSVerificationEnabled')}
                            data-field="SMSVerificationEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item 
                            label='手机验证码登录' 
                            name="SMSLoginEnabled"
                            style={getHighlightStyle('SMSLoginEnabled')}
                            data-field="SMSLoginEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item 
                            label='密码注册时验证手机' 
                            name="SMSRegisterEnabled"
                            style={getHighlightStyle('SMSRegisterEnabled')}
                            data-field="SMSRegisterEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                        <Form.Item 
                            label='AccessKeyId' 
                            name="SMSAccessKeyId"
                            style={getHighlightStyle('SMSAccessKeyId')}
                            data-field="SMSAccessKeyId"
                        >
                            <Input/>
                        </Form.Item>
                        <Form.Item 
                            label='AccessKeySecret' 
                            name="SMSAccessKeySecret"
                            style={getHighlightStyle('SMSAccessKeySecret')}
                            data-field="SMSAccessKeySecret"
                        >
                            <Input.Password placeholder="敏感信息不会发送到前端显示" autoComplete="new-password"/>
                        </Form.Item>
                        <Form.Item 
                            label='短信签名' 
                            name="SMSSignName"
                            style={getHighlightStyle('SMSSignName')}
                            data-field="SMSSignName"
                        >
                            <Input placeholder="例如：ShellAPI"/>
                        </Form.Item>
                        <Form.Item 
                            label='短信模板' 
                            name="SMSTemplateCode"
                            style={getHighlightStyle('SMSTemplateCode')}
                            data-field="SMSTemplateCode"
                        >
                            <Input placeholder="例如：SMS_12345678"/>
                        </Form.Item>
                    </Card>
                    <Card title={"JWT 鉴权"} size="small">
                        <Form.Item 
                            label={'允许 JWT 鉴权（包含api和relay）'} 
                            name={'JWTAuthEnabled'}
                            style={getHighlightStyle('JWTAuthEnabled')}
                            data-field="JWTAuthEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                    </Card>
                    <Card title={"JWT 跨域登录"} size="small">
                        <Form.Item 
                            label={'允许 JWT 跨域登录'} 
                            name={'JWTCrossLoginEnabled'}
                            style={getHighlightStyle('JWTCrossLoginEnabled')}
                            data-field="JWTCrossLoginEnabled"
                        >
                            <Switch/>
                        </Form.Item>
                    </Card>
                    <Button type="primary" htmlType="submit" loading={submitting} block>保存</Button>
                </Space>
            </Form>
        </Spin>
    )
}

export default React.memo(LoginAuthConfig);