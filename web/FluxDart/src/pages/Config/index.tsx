import React, {lazy, Suspense, useState} from 'react';
import {
    ApiOutlined,
    AuditOutlined,
    CalculatorOutlined,
    ClockCircleOutlined,
    FileProtectOutlined,
    GlobalOutlined,
    HourglassOutlined,
    PictureOutlined,
    SendOutlined,
    SettingOutlined,
    SkinOutlined,
    TeamOutlined,
    VerifiedOutlined
} from '@ant-design/icons';
import {App, AutoComplete, Col, Input, Row, Segmented} from 'antd';
import type {SizeType} from 'antd/es/config-provider/SizeContext';
import Loading from '../../components/Loading';
import {isMobile} from "../../helpers";
import {useTranslation} from "react-i18next";

const PersonalPreferenceSetting = lazy(() => import('./PersonalPreferenceSetting'));
const PublicSetting = lazy(() => import('./PublicSetting'));
const RelaySetting = lazy(() => import('./RelaySetting'));
const OperationSetting = lazy(() => import('./OperationSetting'));
const RatioSetting = lazy(() => import('./RatioSetting'));
const SystemConfig = lazy(() => import('./SystemConfig'));
const VerificationSetting = lazy(() => import('./VerificationSetting'));
const UpdateSetting = lazy(() => import('./UpdateSetting'));
const LoginAuthConfig = lazy(() => import('./LoginAuthConfig'));
const PusherConfig = lazy(() => import('./PusherConfig'));
const SensitiveWordConfig = lazy(() => import('./SensitiveWordConfig'));
const JobConfig = lazy(() => import('./JobConfig'));
const MidjourneySetting = lazy(() => import('./MidjourneySetting'));
const TimeoutConfig = lazy(() => import('./TimeoutConfig'));

const { Search } = Input;

interface StylePreset {
    segmentedWrapper?: React.CSSProperties;
    segmented?: React.CSSProperties;
    segmentedItem?: React.CSSProperties;
}

// 定义搜索建议的类型
interface SearchSuggestion {
    value: string;
    label: string;
}

// 定义配置映射的类型
interface ConfigItem {
    [key: string]: string;
}

interface ConfigMap {
    [key: string]: ConfigItem;
}

function ConfigPage() {
    const [value, setValue] = useState(localStorage.getItem('lastConfigPage') || 'PublicSetting');
    const [searchText, setSearchText] = useState('');
    const [highlightConfig, setHighlightConfig] = useState('');
    const { t } = useTranslation();
    const { message: AntdMessage } = App.useApp();

    // 配置映射表 - 使用国际化后的文本作为键
    const configMap: ConfigMap = {
        'PublicSetting': {
            // 服务信息
            '系统名称设置': 'SystemName',
            '服务地址': 'ServerAddress',
            '访客对话': 'GuestChatPageEnabled',
            'PPT生成菜单': 'PptGenPageEnabled',
            '代理商菜单': 'AgentMenuEnabled',
            '对话链接': 'ChatLink',
            '充值链接': 'TopUpLink',
            'Logo': 'Logo',
            '站点描述': 'SiteDescription',
            
            // 公开信息
            '首页内容': 'HomePageContent',
            '新首页配置': 'NewHomeConf',
            '纯净首页': 'PureHomePageEnabled',
            '关于页面': 'About',
            '公告内容': 'Notice',
            '公告标记': 'NoticeVersion',
            '注册信息': 'RegisterInfo',
            '状态页地址': 'StatusPageUrl',
            
            // 浮动按钮
            '浮动按钮': 'FloatButtonEnabled',
            '文档信息': 'DocumentInfo',
            '微信信息': 'WechatInfo',
            'QQ信息': 'QqInfo',
            
            // 个性化
            '头部脚本': 'HeaderScript',
            '自定义主题': 'CustomThemeConfig',
            '导航栏附加菜单': 'NavExtMenus',
            
            // 协议内容
            '隐私政策': 'PrivacyPolicy',
            '服务协议': 'ServiceAgreement',
            
            // 友情链接
            '友情链接': 'CustomAppList'
        },
        'RelaySetting': {
            // 爬虫优化设置
            '全局自定义请求头': 'GlobalCustomRequestHeaders',
            'IP伪装': 'SpoofIP',
            '启用IP伪装': 'SpoofIPEnabled',
            '反制成分判定': 'AntiIngredientEnabled',
            
            // 调度选项
            '权重精确计算': 'PreciseCalculationOfWeightsEnabled',
            '启用渠道指标统计': 'ChannelMetricsEnabled',
            '启用基于渠道得分的路由': 'ChannelScoreRoutingEnabled',
            
            // 流式响应设置
            '信任上游流式用量': 'TrustUpstreamStreamUsageEnabled',
            '强制上游返回用量': 'ForceStreamOptionEnabled',
            '强制下游流式用量': 'ForceDownstreamStreamUsageEnabled',
            
            // 重试设置
            '失败重试次数': 'RetryTimes',
            '重启尝试次数': 'ReviveRetryTimes',
            '排除失败渠道': 'RetryWithoutFailedChannelEnabled',
            '隐藏报错信息': 'HideRelayErrorEnabled',
            '统一上游错误类型': 'HideUpstreamApiTypeErrorEnabled',
            '响应错误依旧计费': 'ResponseErrorStillChargeEnabled',
            
            // 监控设置
            '失败时禁用渠道': 'AutomaticDisableChannelEnabled',
            '成功时启用通道': 'AutomaticEnableChannelEnabled',
            '最长响应时间': 'ChannelDisableThreshold',
            
            // 熔断/禁用/重试个性化设置
            '失败时拆分模型禁用': 'ChannelAbilityDisableEnabled',
            'relay error 强制重试': 'RelayErrForceRetryKeywordEnabled',
            '强制重试命中模型': 'RelayErrForceRetryModelList',
            '强制重试命中内容': 'RelayErrForceRetryKeywordList',
            '个性化熔断设置': 'CustomCircuitBreakerEnabled',
            '熔断HTTP状态码': 'CircuitBreakerHttpStatusCodeList',
            '个性化禁用设置': 'CustomDisableChannelEnabled',
            '禁用HTTP状态码': 'DisableChannelHttpStatusCodeList',
            
            // 请求替换设置
            '请求最大兼容模式': 'RequestMaxCompatibilityEnabled',
            '文件链接解析': 'ParseFileUrlEnabled',
            '空提示词替换': 'EmptyPromptReplaceEnabled',
            '空提示词替换内容': 'EmptyPromptReplaceContent',

            // MaxToken自动检测
            '启用MaxToken自动检测': 'MaxTokenAutoDetectionEnabled',
            '模型Token限制配置': 'ModelMaxTokensConfig'
        },
        'MidjourneySetting': {
            '菜单显示绘图日志': 'MidjourneyEnabled',
            '启用 Plus': 'MidjourneyPlusEnabled',
            '任务更新模式': 'BatchUpdateMjMode',
            '去除提示词前斜杠': 'MidjourneyRemoveSlashEnabled',
            '违禁词失败补偿': 'MJSensitiveWordsRefundEnabled',
            '任务进度轮询间隔': 'MidjourneyPollDuration',
            '工作协程数量': 'BatchUpdateMjWorkerSize',
            '批量查询数量': 'BatchUpdateMjBatchSize',
            'MJ-Base64本地存储域名': 'MidjourneyBase64StorageAddress',
            'GPT图片本地存储域名': 'GptImageStorageAddress',
            '本地存储保留时间': 'MidjourneyBase64StorageRetentionDays',
            '启用自定义图片回显': 'MidjourneyCustomImageUrlEnabled',
            '自定义图片回显地址': 'MidjourneyCustomImageUrl',
            '自定义discord反代': 'MjDiscordCdnProxy'
        },
        'OperationSetting': {
            // 额度相关
            '注册赠送额度': 'QuotaForNewUser',
            '邀请奖励额度': 'QuotaForInviter',
            '受邀奖励额度': 'QuotaForInvitee',
            '请求预扣费额度': 'PreConsumedQuota',
            '额度提醒阈值': 'QuotaRemindThreshold',
            
            // 签到设置
            '启用签到': 'CheckinEnabled',
            '签到赠送额度': 'CheckinQuota',
            '签到验证码难度动态提升': 'CheckinCaptchaDifficultyIncreaseEnabled',
            '签到验证码随机背景色': 'CheckinCaptchaRandomBackgroundColorEnabled',
            '签到验证码位数每': 'CheckinCaptchaLengthDuration',
            '签到验证码位数增加': 'CheckinCaptchaLengthIncrease',
            '签到验证码噪点数每': 'CheckinCaptchaNoiseDuration',
            '签到验证码噪点数量增加': 'CheckinCaptchaNoiseIncrease',
            
            // 计费选项
            '开启新版tiktoken计费': 'NewTiktokenEnabled',
            '近似估算token': 'ApproximateTokenEnabled',
            '用户令牌分组自由切换': 'TokenGroupChangeEnabled',
            '单位美金额度': 'QuotaPerUnit',
            
            // 日志选项
            '记录消费日志': 'LogConsumeEnabled',
            '启用访客令牌查询': 'GuestQueryEnabled',
            '允许用户查看日志说明': 'UserLogViewEnabled',
            '日志消费时长展示类型': 'LogDurationType',
            '耗时计算包含重试': 'LogDurationIncludeRetryEnabled',
            '记录详细日志': 'LogDetailConsumeEnabled',
            '记录下游错误日志': 'LogDownstreamErrorEnabled',
            '错误日志采样率': 'LogErrorSamplingRate',
            '记录上游原始响应': 'LogUpstreamResponseEnabled',
            '记录最终响应内容': 'LogFullResponseEnabled',
            '提示词日志记录最大长度限制': 'MaxPromptLogLength',
            '详细日志白名单': 'LogDetailsModelWhitelistEnabled',
            '详细日志白名单模型': 'LogDetailsModelWhitelist'
        },
        'RatioSetting': {
            '倍率模式': 'RatioMode',
            '模型倍率': 'ModelRatio',
            '分组倍率': 'GroupRatio',
            '用户倍率': 'UserRatio'
        },
        'LoginAuthConfig': {
            // GitHub OAuth
            'GitHub登录': 'GitHubOAuthEnabled',
            'GitHub Client ID': 'GitHubClientId',
            'GitHub Client Secret': 'GitHubClientSecret',

            // Google OAuth
            'Google登录': 'GoogleOAuthEnabled',
            'Google Client ID': 'GoogleClientId',
            'Google Client Secret': 'GoogleClientSecret',
            
            // WeChat Server
            '微信登录': 'WeChatAuthEnabled',
            'WeChat Server 地址': 'WeChatServerAddress',
            '公众号二维码链接': 'WeChatAccountQRCodeImageURL',
            'WeChat Server访问凭证': 'WeChatServerToken',
            
            // Telegram OAuth
            'Telegram登录': 'TelegramOAuthEnabled',
            'Telegram Bot Name': 'TelegramBotName',
            'Telegram Bot Token': 'TelegramBotToken',
            
            // 飞书授权登录
            '飞书 App ID': 'LarkClientId',
            '飞书 App Secret': 'LarkClientSecret',
            
            // 短信验证码
            '短信验证总开关': 'SMSVerificationEnabled',
            '手机验证码登录': 'SMSLoginEnabled',
            '密码注册时验证手机': 'SMSRegisterEnabled',
            '阿里云AccessKeyId': 'SMSAccessKeyId',
            '阿里云AccessKeySecret': 'SMSAccessKeySecret',
            '短信签名': 'SMSSignName',
            '短信模板': 'SMSTemplateCode',
            
            // JWT
            'JWT鉴权': 'JWTAuthEnabled',
            'JWT跨域登录': 'JWTCrossLoginEnabled'
        },
        'PusherConfig': {
            'SMTP 服务器': 'SMTPServer',
            'SMTP 端口': 'SMTPPort',
            'SMTP 账户': 'SMTPAccount',
            'SMTP 发送者邮箱': 'SMTPFrom',
            'SMTP 访问凭证': 'SMTPToken',
            '邮件代理服务器': 'EmailProxyServer',
            '邮件代理服务器密钥': 'EmailProxyAuth',
            '密码注册时验证邮箱': 'EmailVerificationEnabled',
            '启用白名单': 'EmailDomainRestrictionEnabled',
            '拦截字母QQ邮箱': 'EmailDomainQQNumberOnlyEnabled',
            '域名白名单域名': 'EmailDomainWhitelist',
            '接收者UID': 'RootUserWxPusherUid',
            'AppToken': 'WxPusherAppToken',
            'WebhookUrl': 'QyWxBotWebhookUrl',
            '启用黑名单': 'NotificationKeywordBlacklistEnabled',
            '关键词黑名单': 'NotificationKeywordBlacklist',
            '邮件通知': 'RootUserEmailNotificationEnabled',
            'WxPusher通知': 'RootUserWxPusherNotificationEnabled',
            '企微机器人通知': 'RootUserQyWxBotNotificationEnabled',
            '严格报错通知': 'RootUserRelayErrorNotificationEnabled',
            '飞书推送': 'FeishuPushEnabled',
            '飞书 App ID': 'FeishuAppId',
            '飞书 App Secret': 'FeishuAppSecret',
            '飞书接收者ID': 'FeishuReceiverId'
        },
        'SystemConfig': {
            // 系统监控
            '新版RPM统计': 'NewRPMEnabled',
            
            // 在线支付
            '展示在线充值': 'OnlineTopupEnabled',
            '易支付地址1': 'PayAddress',
            '自定义回调地址1': 'CustomEpayCallbackAddress',
            '易支付商户ID1': 'EpayId',
            '易支付商户密钥1': 'EpayKey',
            '支持支付方式1': 'CustomAvailablePayMethods',
            '易支付地址2': 'PayAddress2',
            '自定义回调地址2': 'CustomEpayCallbackAddress2',
            '易支付商户ID2': 'EpayId2',
            '易支付商户密钥2': 'EpayKey2',
            '支持支付方式2': 'CustomAvailablePayMethods2',
            '美金单价': 'Price',
            '自定义PayPalUSD汇率': 'CustomPayPalUsdRate',
            'PayPal最低扣除金额': 'PayPalMinimumFee',
            '自定义USDT汇率': 'CustomUsdtRate',
            '自定义ETH汇率': 'CustomEthRate',
            '充值额度上限': 'MaxTopUpLimit',
            
            // 余额有效期
            '启用余额过期': 'QuotaExpireEnabled',
            '余额有效期': 'QuotaExpireDays',
            
            // 账户间转账
            '启用转账': 'TransferEnabled',
            '转账手续费': 'TransferFee',
            
            // 其他选项
            '允许新用户注册': 'RegisterEnabled',
            '允许通过密码注册': 'PasswordRegisterEnabled',
            '密码注册时验证邮箱': 'EmailVerificationEnabled',
            '允许注销登录': 'UnsubscribeEnabled',
            
            // 文件系统配置
            '启用文件系统': 'FileSystemServerEnabled',
            '启用文件系统日志': 'FileSystemClientInfoLogEnabled',
            '文件服务器地址': 'FileSystemServerAddress',
            '启用文件系统代理模式': 'FileSystemProxyModeEnabled',
            '文件系统代理URL': 'FileSystemProxyURL',
            '文件系统上传字段名': 'FileSystemProxyUploadFieldName',
            '文件系统请求方法': 'FileSystemProxyMethod',
            '文件系统请求头': 'FileSystemProxyHeaders',
            '文件系统启用认证': 'FileSystemProxyAuthEnabled',
            '文件系统认证类型': 'FileSystemProxyAuthType',
            '文件系统认证值': 'FileSystemProxyAuthValue',
            
            // 日志优化配置
            '启用日志优化': 'ShellApiLogOptimizerEnabled',
            '优先使用优化器': 'PreferOptimizerQueryEnabled',
            '优化器网关': 'ShellApiLogOptimizerGateWay',
            '优化器索引': 'ShellApiLogOptimizerDynamicIndex',
            '优化器访问秘钥': 'ShellApiLogOptimizerAccessToken',
            
            // 敏感词配置
            '敏感词提示': 'SensitiveWordsTips',



            // 特殊路由配置
            '特殊路由': 'LimitedAccessConfigs'
        },
        'VerificationSetting': {
            // Turnstile 设置
            'Turnstile启用': 'TurnstileCheckEnabled',
            'TurnstileSiteKey': 'TurnstileSiteKey',
            'TurnstileSecretKey': 'TurnstileSecretKey',
            
            // 图形验证码设置
            '图形验证码启用': 'CaptchaCheckEnabled',
            '验证码长度': 'CaptchaKeyLong',
            '验证码噪声': 'CaptchaNoiseCount',
            '验证码背景色': 'CaptchaBgColor',
            
            // 自定义验证方式
            '登录验证': 'CustomVerificationType_login',
            '注册验证': 'CustomVerificationType_register',
            '重置密码验证': 'CustomVerificationType_reset_password',
            '发送短信验证': 'CustomVerificationType_send_sms',
            '签到验证': 'CustomVerificationType_checkin',
            '验证码验证': 'CustomVerificationType_verification'
        },
        'SensitiveWordConfig': {
            '敏感词开关': 'SensitiveWordsEnabled',
            '敏感词库': 'SensitiveWords',
            '命中记录': 'SensitiveWordsLog'
        },
        'JobConfig': {
            '任务开关': 'JobEnabled',
            '执行间隔': 'JobInterval',
            '任务类型': 'JobType'
        },
        'PersonalPreferenceSetting': {
            '主题设置': 'Theme',
            '语言设置': 'Language',
            '自动统计': 'CustomAutoGetLogStat'
        },
        'TimeoutConfig': {
            '超时配置': 'TimeoutConfig',
            '用户超时设置': 'UserTimeoutSettings',
            '首字节超时': 'FirstByteTimeout',
            '总超时时间': 'TotalTimeout',
            'TPS阈值': 'TpsThreshold',
            '费用承担方': 'TimeoutCostBearer',
            '管理员承担': 'AdminBears',
            '用户承担': 'UserBears'
        }
    };

    // 页面标题映射
    const pageTitleMap = {
        'PublicSetting': '公开设置',
        'RelaySetting': '中继设置',
        'MidjourneySetting': '绘图设置',
        'OperationSetting': '运营设置',
        'RatioSetting': '倍率设置',
        'LoginAuthConfig': '登录设置',
        'PusherConfig': '推送设置',
        'SystemConfig': '系统设置',
        'VerificationSetting': '验证码设置',
        'JobConfig': '计划任务',
        'SensitiveWordConfig': '敏感词设置',
        'PersonalPreferenceSetting': '偏好设置',
        'ShellApi': '更新设置',
        'TimeoutConfig': '超时配置'
    };

    const handleSearch = (text: string) => {
        if (!text) return;
        
        let found = false;
        console.log('Searching for:', text);

        // 搜索匹配的配置项
        for (const [page, configs] of Object.entries(configMap)) {
            for (const [label, key] of Object.entries(configs)) {
                if (label.toLowerCase().includes(text.toLowerCase())) {
                    console.log('Found match:', {page, label, key});
                    
                    // 重要：先清除之前的高亮，然后设置新的高亮
                    setHighlightConfig('');
                    // 使用 setTimeout 确保状态更新完成
                    setTimeout(() => {
                        setValue(page);
                        setHighlightConfig(key);
                    }, 0);
                    
                    found = true;
                    AntdMessage.success(`找到配置项: ${label} (在${pageTitleMap[page]}下)`);
                    break;
                }
            }
            if (found) break;
        }
        
        if (!found) {
            setHighlightConfig('');
            AntdMessage.info('未找到匹配的配置项');
        }
    };

    // 生成搜索建议
    const getSearchSuggestions = (text: string): SearchSuggestion[] => {
        if (!text) return [];
        
        const suggestions: SearchSuggestion[] = [];
        const addedLabels = new Set(); // 用于去重
        
        for (const [page, configs] of Object.entries(configMap)) {
            for (const [label, key] of Object.entries(configs)) {
                if (label.toLowerCase().includes(text.toLowerCase()) && !addedLabels.has(label)) {
                    suggestions.push({
                        value: label,
                        label: `${label} (在${pageTitleMap[page]}下)`
                    });
                    addedLabels.add(label); // 添加到已处理集合
                }
            }
        }
        
        return suggestions;
    };

    // 移动端样式配置
    const mobileStyles: StylePreset = isMobile() ? {
        segmentedWrapper: {
            overflowX: 'auto',
            width: '100%',
            padding: '8px 0',
            WebkitOverflowScrolling: 'touch',
            backgroundColor: '#f5f5f5', // 背景色设置在外层容器
            borderRadius: 8,
            minWidth: '100%'
        },
        segmented: {
            width: 'max-content',
            minWidth: '100%',
            padding: '4px',
            background: 'transparent' // 内层透明
        },
        segmentedItem: {
            minWidth: 60,
            padding: '0 8px',
            whiteSpace: 'nowrap'
        }
    } : {};

    // PC端样式配置
    const pcStyles: StylePreset = {
        segmentedWrapper: {
            maxWidth: 1600,
            margin: '0 auto',
            width: '100%'
        },
        segmented: {
            background: '#f5f5f5',
            padding: '4px 8px',
            borderRadius: 8,
            fontSize: 14
        },
        segmentedItem: {
            padding: '0 12px',
            whiteSpace: 'nowrap',
            minWidth: 90
        }
    };

    const options = [
        { label: '公开', value: 'PublicSetting', icon: <GlobalOutlined /> },
        {label: '中继', value: 'RelaySetting', icon: <ApiOutlined/>},
        {label: '绘图', value: 'MidjourneySetting', icon: <PictureOutlined/>},
        {label: '运营', value: 'OperationSetting', icon: <TeamOutlined/>},
        {label: '倍率', value: 'RatioSetting', icon: <CalculatorOutlined/>},
        {label: '登录', value: 'LoginAuthConfig', icon: <VerifiedOutlined/>},
        {label: '推送', value: 'PusherConfig', icon: <SendOutlined/>},
        {label: '系统', value: 'SystemConfig', icon: <SettingOutlined/>},
        {label: '验证码', value: 'VerificationSetting', icon: <HourglassOutlined/>},
        {label: '计划任务', value: 'JobConfig', icon: <ClockCircleOutlined />},
        {label: '敏感词', value: 'SensitiveWordConfig', icon: <AuditOutlined/>},
        {label: '偏好', value: 'PersonalPreferenceSetting', icon: <SkinOutlined/>},
        // {label: '更新', value: 'ShellApi', icon: <FileProtectOutlined/>},
        { label: '超时', value: 'TimeoutConfig', icon: <ClockCircleOutlined /> },
    ].map(item => ({
        ...item,
        label: isMobile() ? '' : item.label,
        style: isMobile() ? mobileStyles.segmentedItem : pcStyles.segmentedItem
    }));

    return (
        <>
            <Row justify="center" style={{ marginBottom: 16 }}>
                <Col xs={24} md={20} lg={16}>
                    <AutoComplete
                        placeholder="搜索配置项..."
                        options={searchText ? getSearchSuggestions(searchText) : []}
                        onChange={(value: string) => setSearchText(value)}
                        onSelect={handleSearch}
                        style={{ width: '100%' }}
                    />
                </Col>
            </Row>

            <Row justify="center" style={{ marginBottom: isMobile() ? 0 : 24 }}>
                <Col
                    xs={24}
                    md={24}
                    lg={22}
                    xl={20}
                    xxl={18}
                    style={isMobile() ? {} : pcStyles.segmentedWrapper}
                >
                    <div style={isMobile() ? mobileStyles.segmentedWrapper : {}}>
                        <Segmented
                            options={options}
                            value={value}
                            onChange={(value) => {
                                setValue(value as string);
                                localStorage.setItem('lastConfigPage', value as string);
                            }}
                            block={!isMobile()}
                            size={(isMobile() ? 'large' : 'default') as SizeType}
                            style={{
                                ...(isMobile() ? mobileStyles.segmented : pcStyles.segmented),
                                width: '100%'
                            }}
                        />
                    </div>
                </Col>
            </Row>

            <Row justify="center">
                <Col
                    xs={24}
                    md={20}
                    lg={16}
                    xl={14}
                    style={{
                        marginTop: 24,
                        padding: '0 24px',
                        maxWidth: 1600,
                        width: '100%'
                    }}
                >
                    <Suspense fallback={<Loading />}>
                        {value === 'PublicSetting' && (
                            <PublicSetting 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'RelaySetting' && (
                            <RelaySetting 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'MidjourneySetting' && (
                            <MidjourneySetting 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'OperationSetting' && (
                            <OperationSetting 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'RatioSetting' && (
                            <RatioSetting 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'LoginAuthConfig' && (
                            <LoginAuthConfig 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'PusherConfig' && (
                            <PusherConfig 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'SystemConfig' && (
                            <SystemConfig 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'VerificationSetting' && (
                            <VerificationSetting 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'JobConfig' && (
                            <JobConfig 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'SensitiveWordConfig' && (
                            <SensitiveWordConfig 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {value === 'PersonalPreferenceSetting' && (
                            <PersonalPreferenceSetting 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                        {/* {value === 'ShellApi' && <UpdateSetting/>} */}
                        {value === 'TimeoutConfig' && (
                            <TimeoutConfig 
                                key={highlightConfig}
                                highlightConfig={highlightConfig} 
                            />
                        )}
                    </Suspense>
                </Col>
            </Row>
        </>
    );
}

export default ConfigPage;