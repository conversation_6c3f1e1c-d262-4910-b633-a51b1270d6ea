import React, {useCallback, useContext, useEffect, useState} from 'react';
import {API, copy, getAdminStatus, hasPermission, hasReadPermission, hasWritePermission, isMobile, Permission, showError} from '../../helpers';
import {UserContext} from '../../context/User';
import {useGroup} from '../../context/Group';
import {useAdminStatus} from '../../context/AdminStatus';
import {
    App,
    Button,
    Drawer,
    Dropdown,
    InputNumber,
    Menu,
    MenuProps,
    Popconfirm,
    Popover,
    Select,
    Space,
    Switch,
    Tag,
    Tooltip,
    Modal,
    Alert,
    Input,
    Typography,
    Card,
    Row,
    Col,
    Statistic,
    Table,
    Spin
} from 'antd';
import {
    actionButtonProps,
    AUTOCOMPLETE_MODEL_NAMES,
    CHANNEL_TYPE_OPTIONS,
    DefaultResponse,
    paginationProps
} from '../../constants'
import {
    createRenderFunctions,
    renderChannelBillingTypesTag,
    renderResponseTime,
    renderUserGroupTag,
} from '../../helpers/render'
import {
    BulbFilled,
    BulbTwoTone,
    CopyTwoTone,
    createFromIconfontCN,
    DeleteFilled,
    DeleteOutlined,
    DiffFilled,
    DollarCircleOutlined,
    EditFilled,
    EditTwoTone,
    FileTextTwoTone,
    HistoryOutlined,
    HourglassTwoTone,
    InfoCircleOutlined,
    OpenAIOutlined,
    PlayCircleTwoTone,
    ReloadOutlined,
    StopTwoTone, 
    ThunderboltOutlined,
    ToolTwoTone,
    WalletFilled,
    BarChartOutlined,
    PieChartOutlined
} from '@ant-design/icons';
import {ParamsType, ProTable} from '@ant-design/pro-components';
import type { ProColumns } from '@ant-design/pro-components';
import {useNavigate} from 'react-router-dom';
import {getCountByPathAndParam, getDataByPathAndParam} from "../../helpers/api-request-module";
import {Trans, useTranslation} from "react-i18next";
import AdvanceTestFormModal from "./AdvanceTestFormModal";
import {useActionRef} from "../../hooks/useActionRef";
import moment from 'moment';
import ReactECharts from 'echarts-for-react';
import * as echarts from 'echarts';

const EditChannelFormModal = React.lazy(() => import('./EditChannelFormModal'));

const { Text } = Typography;

// 添加 GroupOption 接口定义
interface GroupOption {
    id: number;
    name: string;
    display_name: string;
    description: string;
    convert_ratio: number;
    current_group_ratio: number;
    current_topup_ratio: number;
    color_mapping: string;
}

// 添加统计数据接口定义
interface ChannelStatisticsItem {
    group_name: string;
    total_count: number;
    active_count: number;
    disabled_count: number;
    unknown_count: number;
    disabled_rate: number;
    filtered_count: number;
    filtered_rate: number;
    latest_active_created_time?: number; // 最新一条正常状态渠道的创建时间（Unix时间戳）
    average_used_quota: number; // 渠道已用余额的平均值
}

// 添加创建速度数据接口定义
interface ChannelCreationSpeedItem {
    group_name: string;
    time_point: number; // 时间点（Unix时间戳）
    creation_count: number; // 该时间点创建的渠道数量
    cumulative_count: number; // 累计创建的渠道数量
}

// 添加 DefaultOptionType 导入
import type { DefaultOptionType } from 'antd/es/select';

const DescriptionText = ({ text }: { text: string }) => {
    const [expanded, setExpanded] = useState(false);
    const shouldShowExpand = text.length > 20;  // 改为20个字符
    
    return (
        <div style={{ 
            fontSize: '12px',
            color: 'rgba(0, 0, 0, 0.45)',
            lineHeight: '1.5',
            whiteSpace: 'normal',
            wordBreak: 'break-word'
        }}>
            {expanded ? text : (shouldShowExpand ? `${text.slice(0, 20)}...` : text)}
            {shouldShowExpand && (
                <Button 
                    type="link" 
                    size="small" 
                    onClick={(e) => {
                        e.stopPropagation();
                        setExpanded(!expanded);
                    }}
                    style={{ padding: '0 4px', height: '16px' }}
                >
                    {expanded ? '收起' : '展开'}
                </Button>
            )}
        </div>
    );
};

const ChannelsTable = () => {
    const { t, i18n } = useTranslation();
    const { renderModels, renderQuota,renderRoleTag,renderQuotaExpireTime, renderChannelStatusTag } = createRenderFunctions(t);
    const [, updateState] = useState({});
    const forceUpdate = useCallback(() => updateState({}), []);
    const navigate = useNavigate();
    const {message: AntdMessage, modal} = App.useApp();
    const [userState] = useContext(UserContext);
    const {groups, loading: groupsLoading, fetchGroups} = useGroup();
    const {state: adminStatusState, fetchAdminStatus} = useAdminStatus();
    const [channelsCount, setChannelsCount] = useState(0);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [advTestVisible, setAdvTestVisible] = useState(false);
    const [editingChannelId, setEditingChannelId] = useState<number>(0);
    const [isCopy, setIsCopy] = useState(false);
    const [updatingBalance, setUpdatingBalance] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [selectedRowKeys, setSelectedRowKeys]: any = useState([]);//多选时选中的渠道Key
    // 添加列可见性状态，使用简单的键值对类型
    const [columnsVisible, setColumnsVisible] = useState<Record<string, { show: boolean }>>({});

    //获取渠道组 id 对应的渠道组名称
    const [channelGroups, setChannelGroups]: any = useState([]);

    const [isSortByIds, setIsSortByIds] = useState(false);//是否按照 id 倒序排序
    const [unbindOnly, setUnbindOnly] = useState(false);//是否仅显示未绑定的渠道

    const [deletingChannels, setDeletingChannels] = useState(false);//正在批量删除
    const [deletingDisabledChannels, setDeletingDisabledChannels] = useState(false);//正在删除所有禁用渠道
    const [channelStatusCount, setChannelStatusCount] = useState();//渠道状态统计
    const [testResponse, setTestResponse] = useState<{
        warnings?: string[];
        [key: string]: any;
    }>({});//测试结果
    const [testingChannelIds, setTestingChannelIds]: any = useState([]);//正在测试的渠道
    const [isTestResponseDrawerVisible, setIsTestResponseDrawerVisible] = useState(false);//测试结果抽屉是否可见

    const [userGroups, setUserGroups] = useState(Object.keys({"default": 1}));

    const { ref, safeAction } = useActionRef();

    const IconFont = createFromIconfontCN({scriptUrl: ['/font_4308471_sv1r52ct53n.js']});

    // 使用context中的groups数据，不再需要本地state

    // 添加双数据库同步状态
    const [syncBothDB, setSyncBothDB] = useState(false);

    const [topupVisible, setTopupVisible] = useState(false); // 充值模态框是否可见
    const [topupChannelId, setTopupChannelId] = useState<number>(0); // 当前充值的渠道ID
    const [topupCode, setTopupCode] = useState(""); // 充值码
    const [topupLoading, setTopupLoading] = useState(false); // 充值加载状态
    const [topupChannel, setTopupChannel] = useState<any>(null); // 当前充值的渠道信息

    // 初始化读取本地存储的列状态
    const [columnsStateMap, setColumnsStateMap] = useState(() => {
        try {
            const saved = localStorage.getItem('channelsTableColumnsConfig');
            return saved ? JSON.parse(saved) : {};
        } catch (e) {
            console.error('解析保存的列配置失败:', e);
            return {};
        }
    });

    // 添加统计相关状态
    const [statisticsVisible, setStatisticsVisible] = useState(false);
    const [statisticsData, setStatisticsData] = useState<ChannelStatisticsItem[]>([]);
    const [statisticsLoading, setStatisticsLoading] = useState(false);
    const [statisticsFilter, setStatisticsFilter] = useState({
        statusFilter: 0, // 0=全部, 1=启用, 3=禁用
        disableReason: 'all', // 禁用原因过滤
        groupBy: 'server' // 分组方式：server=按服务器, domain=按域名
    });
    // 添加统计表格分页状态
    const [statisticsPagination, setStatisticsPagination] = useState({
        current: 1,
        pageSize: isMobile() ? 5 : 10,
        showSizeChanger: true, // 手机端也显示分页大小选择器
        showQuickJumper: !isMobile(), // 手机端不显示快速跳转
        simple: false, // 手机端也使用完整分页器
        pageSizeOptions: ['5', '10', '20', '50', '100'], // 分页大小选项
        showTotal: (total: number, range: [number, number]) => 
            isMobile() ? `${range[0]}-${range[1]}/${total}` : `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
    });

    // 添加渠道创建RPM状态
    const [channelCreationRPM, setChannelCreationRPM] = useState(0);
    const [rpmLoading, setRpmLoading] = useState(false);

    // 添加创建速度图表相关状态
    const [creationSpeedData, setCreationSpeedData] = useState<Record<string, ChannelCreationSpeedItem[]>>({});
    const [speedTimeGranularity, setSpeedTimeGranularity] = useState<'day' | 'hour'>('hour'); // 默认按小时统计
    const [speedDataLoading, setSpeedDataLoading] = useState(false);
    const [activeSpeedTab, setActiveSpeedTab] = useState('table');
    const [showOverallChart, setShowOverallChart] = useState(false); // 显示整体汇总图表
    const [selectedGroupDetail, setSelectedGroupDetail] = useState<string>(''); // 选中的分组详情

    // 时间戳格式化函数
    const formatTimestamp = (timestamp: number, granularity: 'day' | 'hour' = speedTimeGranularity): string => {
        const date = new Date(timestamp * 1000); // 转换为毫秒
        if (granularity === 'hour') {
            return date.toLocaleString('zh-CN', {
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } else {
            return date.toLocaleDateString('zh-CN', {
                month: '2-digit',
                day: '2-digit'
            });
        }
    };

    // 获取渠道创建RPM数据的函数
    const fetchChannelCreationRPM = async () => {
        try {
            const response = await API.get('/api/channel/creation_rpm');
            if (response.data.success) {
                setChannelCreationRPM(response.data.data.rpm);
            } else {
                setChannelCreationRPM(0);
            }
        } catch (error) {
            console.error('Failed to fetch channel creation RPM:', error);
            setChannelCreationRPM(0);
        }
    };

    // 保存列配置的处理函数
    const handleColumnsStateChange = (map) => {
        setColumnsStateMap(map);
        try {
            localStorage.setItem('channelsTableColumnsConfig', JSON.stringify(map));
        } catch (e) {
            console.error('保存列配置失败:', e);
        }
    };

    // 使用context获取分组数据
    useEffect(() => {
        fetchGroups();
    }, []);

    /**
     * 获取渠道状态统计
     * @returns {Promise<void>}
     * @constructor
     * //{"data": {"1": 84,"2": 3,"3": 61},"message": "","success": true}
     */
    const getChannelCountByStatus = async (): Promise<void> => {
        try {
            const res = await API.get('/api/channel/count_by_status');
            const {success, data} = res.data;
            if (success) {
                console.log(data);
                setChannelStatusCount(data);
            }
        } catch (error) {
            showError(error as Error);
        }
    };

    useEffect(() => {
        // 异步加载用户分组数据
        const loadUserGroups = async () => {
            await fetchAdminStatus();
            const groupRatio = adminStatusState.GroupRatio;
            const groups = Object.keys(groupRatio);
            setUserGroups(groups || ['default']);
        };

        //获取渠道组列表，用于展示渠道对于的渠道组名称
        const fetchChannelGroups = async () => {
            const response = await API.get(`/api/channelGroup/?p=0&pageSize=100`);
            if (response.data.success) {
                setChannelGroups(response.data.data);
            } else {
                AntdMessage.error(t('channelsTable.messages.fetchGroupError', { response }));
            }
        };

        Promise.allSettled([loadUserGroups(), fetchChannelGroups()]).then();
    }, []);

    // 添加获取渠道创建RPM数据的useEffect
    useEffect(() => {
        fetchChannelCreationRPM();
        // 移除自动刷新功能以提升性能，只在页面初始化时加载一次
    }, []);

    const handleEditClick = (channelId: number, isCopy: boolean) => {
        setIsCopy(isCopy);
        setEditingChannelId(channelId); // 设置当前编辑的渠道 ID
        setIsModalVisible(true); // 显示模态框
    };

    const handleAdvTestClick = (channelId: number) => {
        setEditingChannelId(channelId); // 设置当前编辑的渠道 ID
        setAdvTestVisible(true); // 显示模态框
    };

    const request = async (params: ParamsType) => {
        if (unbindOnly) params.channelGroupId = -1;
        if (isSortByIds) params.orderByStr = '[{"id":"desc"}]';
        try {
            const [res, count] = await Promise.all([
                getDataByPathAndParam('channel', params),
                getCountByPathAndParam('channel', params)
            ]);
            const {success, data, message} = res as DefaultResponse;
            if (!success) AntdMessage.error(t('channelsTable.messages.fetchChannelError', { message }));
            setChannelsCount(count);
            return {success: success && count !== -1, data: data || [], total: count};
        } catch (error) {
            showError(error);
            return {success: false, data: [], total: 0};
        }
    }

    //批量删除渠道
    const deleteChannelsByIds = async (ids: string | any[]) => {
        if (!ids || ids.length === 0) {
            AntdMessage.warning(t('channelsTable.messages.selectChannelFirst'));
            return;
        }
        
        let localSyncBothDB = false;
        
        Modal.confirm({
            title: '确认批量删除',
            content: (
                <div>
                    <p>确定要删除选中的 <strong>{ids.length}</strong> 个渠道吗？删除后无法恢复。</p>
                    <div style={{ marginTop: 16 }}>
                        <Switch 
                            checked={localSyncBothDB}
                            onChange={(checked) => { localSyncBothDB = checked; }}
                            checkedChildren="双写" 
                            unCheckedChildren="单写"
                        />
                        <span style={{ marginLeft: 8 }}>是否同时删除SQL和NoSQL数据库中的数据</span>
                    </div>
                </div>
            ),
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async () => {
                setDeletingChannels(true);
                try {
                    const syncParam = localSyncBothDB ? '?syncBothDB=true' : '';
                    const res = await API.delete(`/api/channel/deleteByIds${syncParam}`, {
                        data: ids,
                        transformRequest: [
                            (data, headers) => {
                                headers['Content-Type'] = 'application/json';
                                return JSON.stringify(data);
                            }],
                    });
                    const {data, success, message} = res.data;
                    if (success) {
                        AntdMessage.success(t('channelsTable.messages.deleteSuccess', { count: data }));
                    } else {
                        AntdMessage.error(t('channelsTable.messages.deleteFailed', { message }));
                    }
                    safeAction('reload');
                    setSelectedRowKeys([]);
                } catch (error) {
                    showError(error);
                } finally {
                    setDeletingChannels(false);
                    setIsEditing(false);
                }
            }
        });
    };

    //删除渠道
    const deleteChannel = async (id: any, name: any) => {
        let localSyncBothDB = false;
        
        Modal.confirm({
            title: '确认删除',
            content: (
                <div>
                    <p>确定要删除渠道 <strong>{name}</strong> 吗？删除后无法恢复。</p>
                    <div style={{ marginTop: 16 }}>
                        <Switch 
                            checked={localSyncBothDB}
                            onChange={(checked) => { localSyncBothDB = checked; }}
                            checkedChildren="双写" 
                            unCheckedChildren="单写"
                        />
                        <span style={{ marginLeft: 8 }}>是否同时删除SQL和NoSQL数据库中的数据</span>
                    </div>
                </div>
            ),
            okText: '确认删除',
            cancelText: '取消',
            okType: 'danger',
            onOk: async () => {
                try {
                    const syncParam = localSyncBothDB ? '?syncBothDB=true' : '';
                    const res = await API.delete(`/api/channel/${id}/${syncParam}`);
                    const {success, message} = res.data;
                    if (success) {
                        AntdMessage.success('删除成功');
                        await ref.current?.reload();
                    } else {
                        AntdMessage.error('删除失败：' + message);
                    }
                } catch (error) {
                    showError(error);
                }
            }
        });
    };

    //操作渠道
    const manageChannel = async (id: number, action: "delete" | "enable" | "disable" | "cleanUsage" | "priority" | "weight" | "sort" | "retryInterval", value?: any) => {
        let data: any = {id};
        let res: any;
        const syncParam = syncBothDB ? '?syncBothDB=true' : '';
        
        switch (action) {
            case 'delete':
                res = await API.delete(`/api/channel/${id}/${syncParam}`);
                break;
            case 'enable':
                data.status = 1;
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            case 'disable':
                data.status = 2;
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            case 'cleanUsage':
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}cleanUsage=true`, data);
                break;
            case 'priority':
                if (value === '') return;
                data.priority = parseInt(value);
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            case 'weight':
                if (value === '') return;
                data.weight = parseInt(value);
                if (data.weight < 0) data.weight = 0;
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            case 'sort':
                if (value === '') return;
                data.sort = parseInt(value);
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            case 'retryInterval':
                if (value === '') return;
                data.retryInterval = parseInt(value);
                res = await API.put(`/api/channel/${syncParam}${syncParam ? '&' : '?'}status_only=true`, data);
                break;
            default:
                return;
        }
        const {success, message} = res.data;
        if (success) {
            safeAction('reload');
            if (isSortByIds) {
                AntdMessage.success(t('channelsTable.messages.operationSuccess'));
            } else {
                AntdMessage.success(t('channelsTable.messages.operationSuccessWithSort'));
            }
        } else {
            AntdMessage.error(t('channelsTable.messages.operationFailed', { message }));
        }
    };

    //渠道测试
    const testChannel = async (id: number, name: string, model?: string) => {
        if (testingChannelIds.includes(id)) {
            console.log(t('channelsTable.messages.testRunning', { name, id }));
            return;
        }
        setTestingChannelIds((prevIds: any) => [...prevIds, id]);
        try {
            const url = model ? `/api/channel/test/${id}?model=${model}` : `/api/channel/test/${id}/`;
            const res = await API.get(url);
            const {success, message, time, statusCode, responseBody, warnings} = res.data;
            if (success && statusCode === 200) {
                // 显示成功消息
                if (warnings && warnings.length > 0) {
                    // 如果有警告信息，显示成功但有警告
                    AntdMessage.warning({
                        content: (
                            <div>
                                <div>{t('channelsTable.messages.testSuccessWithWarnings', { 
                                    name, 
                                    id, 
                                    model: model ? t('channelsTable.messages.modelPrefix', { model }) : '',
                                    time: time.toFixed(2)
                                })}</div>
                                <Button 
                                    type="link" 
                                    size="small" 
                                    onClick={() => {
                                        AntdMessage.destroy();
                                        setTestResponse({
                                            [t('channelsTable.messages.channelInfo')]: t('channelsTable.messages.channelDetail', {
                                                name,
                                                id,
                                                modelInfo: model ? t('channelsTable.messages.modelPrefix', { model }) : ''
                                            }),
                                            ...res.data
                                        });
                                        setIsTestResponseDrawerVisible(true);
                                    }}
                                >
                                    {t('channelsTable.messages.viewDetails')}
                                </Button>
                            </div>
                        ),
                        duration: 6,
                    });
                } else {
                    // 普通成功消息
                    AntdMessage.success(t('channelsTable.messages.testSuccess', { 
                        name, 
                        id, 
                        model: model ? t('channelsTable.messages.modelPrefix', { model }) : '',
                        time: time.toFixed(2)
                    }));
                }
                safeAction('reload');
            } else {
                AntdMessage.error({
                    content: t('channelsTable.messages.testFailed', {
                        name,
                        id,
                        model: model ? t('channelsTable.messages.modelPrefix', { model }) : '',
                        code: statusCode,
                        reason: message || t('common.unknown')
                    }),
                    onClick: () => {
                        AntdMessage.destroy();
                        setTestResponse({
                            [t('channelsTable.messages.channelInfo')]: t('channelsTable.messages.channelDetail', {
                                name,
                                id,
                                modelInfo: model ? t('channelsTable.messages.modelPrefix', { model }) : ''
                            }),
                            ...res.data
                        });
                        setIsTestResponseDrawerVisible(true);
                    },
                });
            }
        } catch (error) {
            showError(error as Error);
        } finally {
            setTestingChannelIds((prevIds: any[]) => prevIds.filter((i: any) => i !== id));
        }
    };

    const testChannels = async (status?: number) => {
        const statusText = status === undefined ? t('channelsTable.status.all') :
            status === 1 ? t('channelsTable.status.enabled') :
            status === 2 ? t('channelsTable.status.manualDisabled') :
            status === 3 ? t('channelsTable.status.waitingRetry') :
            status === 4 ? t('channelsTable.status.suspended') : 
            t('channelsTable.status.specified');

        modal.confirm({
            title: t('channelsTable.confirm.testTitle'),
            content: (
                <div>
                    <p>{t('channelsTable.confirm.testContent', { status: statusText })}</p>
                    <p style={{ color: '#faad14' }}>
                        {t('channelsTable.confirm.testNote')}
                    </p>
                </div>
            ),
            onOk: async () => {
                const url = status !== undefined ? `/api/channel/test?status=${status}` : '/api/channel/test';
                const res = await API.get(url);
                const {success, message} = res.data;
                if (success) {
                    AntdMessage.info({
                        content: t('channelsTable.messages.testStarted', { 
                            status: statusText,
                        }),
                        duration: 5,
                        key: 'testChannels'
                    });
                } else {
                    AntdMessage.error({
                        content: message || t('channelsTable.messages.testFailed'), 
                        duration: 3, 
                        key: 'testChannels'
                    });
                }
            },
            getContainer: () => document.getElementById('globalModalContainer') as HTMLElement,
        });
    };

    // 删除指定禁用类型的渠道 2: 手动禁用，3: 自动禁用，4: 暂停使用，99: 全部
    const deleteDisabledChannelsByType = async (type: 2 | 3 | 4 | 99) => {
        modal.confirm({
            title: t('channelsTable.confirm.deleteDisabledTitle'),
            content: t('channelsTable.confirm.deleteDisabledContent', {
                type: type === 99 ? t('channelsTable.status.allDisabled') : t('channelsTable.status.specifiedDisabled')
            }),
            onOk: async () => {
                setDeletingDisabledChannels(true);
                try {
                    const res = await API.delete(`/api/channel/delete_disabled_channel_by_type/${type}/`);
                    const {success, message, data} = res.data;
                    if (success) {
                        safeAction('reload');
                        AntdMessage.success(t('channelsTable.messages.deleteDisabledSuccess', {
                            type: type === 99 ? t('channelsTable.status.allDisabled') : t('channelsTable.status.specifiedDisabled'),
                            count: data
                        }));
                    } else {
                        AntdMessage.error(message || t('channelsTable.messages.deleteFailed'));
                    }
                } catch (error) {
                    showError(error);
                } finally {
                    setDeletingDisabledChannels(false);
                }
            },
            getContainer: () => document.getElementById('globalModalContainer') as HTMLElement,
        });
    }

    // 删除指定禁用原因的渠道，如account_deactivated表示删除停用账号
    const deleteChannelByDisableReason = async (reason: string) => {
        // 创建一个临时的状态管理
        let syncBothDBRef = { current: false };
        
        const ConfirmContent = () => {
            const [localSyncBothDB, setLocalSyncBothDB] = useState(false);
            
            // 同步引用值
            useEffect(() => {
                syncBothDBRef.current = localSyncBothDB;
            }, [localSyncBothDB]);
            
            return (
                <div>
                    <p>{t('channelsTable.confirm.deleteDisabledContent', {
                        type: t('channelsTable.disableReasons.' + reason)
                    })}</p>
                    <div style={{ marginTop: 16 }}>
                        <Switch 
                            checked={localSyncBothDB}
                            onChange={setLocalSyncBothDB}
                            checkedChildren="双写" 
                            unCheckedChildren="单写"
                        />
                        <span style={{ marginLeft: 8 }}>是否同时删除SQL和NoSQL数据库中的数据</span>
                    </div>
                </div>
            );
        };
        
        modal.confirm({
            title: t('channelsTable.confirm.deleteDisabledTitle'),
            content: <ConfirmContent />,
            onOk: async () => {
                setDeletingDisabledChannels(true);
                try {
                    const syncParam = syncBothDBRef.current ? '?syncBothDB=true' : '';
                    const res = await API.delete(`/api/channel/delete_channel_by_disable_reason/${reason}${syncParam}`);
                    const {success, message, data} = res.data;
                    if (success) {
                        safeAction('reload');
                        AntdMessage.success(t('channelsTable.messages.deleteDisabledSuccess', {
                            type: t('channelsTable.disableReasons.' + reason),
                            count: data
                        }));
                    } else {
                        AntdMessage.error(message || t('channelsTable.messages.deleteFailed'));
                    }
                } catch (error) {
                    showError(error);
                } finally {
                    setDeletingDisabledChannels(false);
                }
            },
            getContainer: () => document.getElementById('globalModalContainer') as HTMLElement,
        });
    }

    //更新指定渠道余额
    const updateChannelBalance = async (id: number, name: string) => {
        const res = await API.get(`/api/channel/update_balance/${id}/`);
        const {success, message} = res.data;
        if (success) {
            AntdMessage.success(t('channelsTable.messages.updateBalanceSuccess', { name : name}));
            safeAction('reload');
        } else {
            AntdMessage.error(t('channelsTable.messages.updateBalanceFailed', { name : name, message : message }));
        }
    };

    //更新所有余额
    const updateAllChannelsBalance = async () => {
        setUpdatingBalance(true);
        AntdMessage.info(t('channelsTable.messages.updateAllBalanceStarted'));
        const res = await API.get(`/api/channel/update_balance`);
        const {success, message} = res.data;
        if (success) {
            AntdMessage.success(t('channelsTable.messages.updateAllBalanceSuccess'));
            safeAction('reload');
            setUpdatingBalance(false);
        } else {
            AntdMessage.error(t('channelsTable.messages.operationFailed', { message }));
        }
    };

    // 处理上游渠道充值
    const handleTopupClick = async (channelId: number) => {
        setTopupChannelId(channelId);
        setTopupCode("");
        
        try {
            // 获取渠道详细信息
            const res = await API.get(`/api/channel/${channelId}`);
            const {success, message, data} = res.data;
            if (success) {
                setTopupChannel(data);
                setTopupVisible(true);
            } else {
                AntdMessage.error(t('channelsTable.messages.fetchChannelDetailError', { message }));
            }
        } catch (error) {
            showError(error);
        }
    };

    // 执行上游充值请求
    const executeTopup = async () => {
        if (!topupCode) {
            AntdMessage.warning(t('topup.enterCardKey'));
            return;
        }

        setTopupLoading(true);
        try {
            // 构建请求体、请求头等
            const baseUrl = topupChannel.base_url || "";
            if (!baseUrl) {
                AntdMessage.error("渠道没有设置基础URL");
                setTopupLoading(false);
                return;
            }

            // 从当前渠道数据中获取所需参数
            const accessToken = topupChannel.platform_access_token || "";
            const upstreamUserId = topupChannel.upstream_user_id || "";
            
            if (!accessToken) {
                AntdMessage.error("渠道缺少访问令牌，请在编辑渠道时设置");
                setTopupLoading(false);
                return;
            }

            // 构建请求体
            const requestData = {
                key: topupCode
            };

            // 发送API请求
            const topupUrl = `${baseUrl}/api/user/topup`;
            const headers = {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            };

            // 如果有上游用户ID，添加到请求头
            if (upstreamUserId) {
                headers['New-API-User'] = upstreamUserId;
            }

            const res = await API.post('/api/proxy', {
                url: topupUrl,
                method: 'POST',
                headers: headers,
                data: requestData
            });

            const {success, message, data} = res.data;
            if (success) {
                AntdMessage.success(t('topup.redeemSuccess', { amount: `$${(data / 500000).toFixed(2)}` }));
                setTopupVisible(false);
                updateChannelBalance(topupChannelId, topupChannel.name);
            } else {
                AntdMessage.error(message || t('topup.redeemError'));
            }
        } catch (error) {
            showError(error);
            AntdMessage.error(t('topup.redeemError'));
        } finally {
            setTopupLoading(false);
        }
    };

    //下拉菜单
    const renderMenu = (channel: { id: any; name: any; }) => (
        <Menu>
            <Menu.Item key="0" onClick={() => handleAdvTestClick(channel.id)}>
                <BulbFilled/> {t('channelsTable.actions.advancedTest')}
            </Menu.Item>
            <Menu.Item key="1" onClick={() => handleEditClick(channel.id, true)}>
                <CopyTwoTone/> {t('channelsTable.actions.copy')}
            </Menu.Item>
            <Menu.Item key="2" onClick={() => navigate(`/log?current=1&pageSize=10&channel=${channel.id}`)}>
                <FileTextTwoTone/> {t('channelsTable.actions.viewLog')}
            </Menu.Item>
            <Menu.Item key="3" onClick={() => navigate(`/ability?channel_id=${channel.id}`)}>
                <ThunderboltOutlined /> {t('channelsTable.actions.viewAbility')}
            </Menu.Item>
            <Menu.Item key="4" onClick={async () => {
                await deleteChannel(channel.id, channel.name);
            }} danger>
                <DeleteOutlined/> {t('channelsTable.actions.delete')}
            </Menu.Item>
            <Menu.Item key="5" onClick={async () => {
                modal.confirm({
                    title: t('channelsTable.confirm.cleanUsageTitle'),
                    content: t('channelsTable.confirm.cleanUsageContent', { name: channel.name, id: channel.id }),
                    onOk: async () => {
                        await manageChannel(channel.id, "cleanUsage");
                        AntdMessage.success(t('channelsTable.messages.operationSuccess'));
                        safeAction('reload');
                    },
                    getContainer: () => document.getElementById('globalModalContainer') as HTMLElement,
                });
            }} danger>
                <DeleteOutlined/> {t('channelsTable.actions.cleanUsage')}
            </Menu.Item>
            <Menu.Item key="6" onClick={() => handleTopupClick(channel.id)}>
                <WalletFilled /> {t('topup.cardRedemption')}
            </Menu.Item>
        </Menu>
    );

    //超频熔断选择器
    const OverFrequencySwitch = ({record}) => {
        const [updating, setUpdating] = useState(false); // 初始化更新状态
        // 处理开关状态变化
        const handleToggle = async () => {
            setUpdating(true);
            const res = await API.put('/api/channel/?status_only=true', {
                ...record,
                overFrequencyAutoDisable: !record.overFrequencyAutoDisable
            });
            const {success, message} = res.data;
            if (success) {
                safeAction('reload');
                AntdMessage.success('更新成功');
            } else {
                AntdMessage.error('更新失败：' + message);
            }
            setUpdating(false);
        };

        return <Switch 
            checked={record.overFrequencyAutoDisable} 
            onChange={handleToggle} 
            checkedChildren={t('common.on')}
            unCheckedChildren={t('common.off')}
            loading={updating}
        />;
    };

    //列定义
    const columns = [
        {
            width: '4%',
            title: t('channelsTable.columns.id'),
            fixed: isMobile() ? '' : 'left',
            dataIndex: 'id',
            disable: true,
            search: {
                order: 99,
            },
        },
        {
            width: '12%',
            title: t('channelsTable.columns.name'),
            dataIndex: 'name',
            disable: true,
            search: {
                order: 98,
            },
            render: (text, record) => {
                const displayName = record.remark ? record.remark : text;
                const createdTime = record.created_time ? new Date(record.created_time * 1000).toLocaleString() : '-';
                return (
                    <Tooltip
                        title={
                            <>
                                <div>{t('channelsTable.columns.name')}: {text}</div>
                                {record.remark && <div>{t('common.remark')}: {record.remark}</div>}
                                <div>{t('channelsTable.columns.createdTime')}: {createdTime}</div>
                            </>
                        }
                    >
                        <span>{displayName}</span>
                    </Tooltip>
                );
            },
        },
        {
            width: '8%',
            title: t('channelsTable.columns.group'),
            dataIndex: 'channel_group_id',
            render: (_: any, record: { channel_group_id: number; }) => {
                const group = channelGroups.find((g: { id: number; }) => g.id === record.channel_group_id);
                return group ? group.name : t('common.none');
            },
            hideInSearch: true,
            hideInTable: isMobile() || unbindOnly,
        },
        {
            width: '6%',
            title: t('channelsTable.columns.billingType'),
            dataIndex: 'billing_type',
            render: (billing_type: number) => renderChannelBillingTypesTag(billing_type),
            renderFormItem: ({defaultRender, ...rest}) => {
                return <Select {...rest} placeholder={t('common.selectPlaceholder', { name: t('channelsTable.columns.billingType') })} allowClear>
                    <Select.Option value="1">{t('billingTypes.quota')}</Select.Option>
                    <Select.Option value="2">{t('billingTypes.times')}</Select.Option>
                </Select>;
            },
        },
        {
            width: '9%',
            title: t('channelsTable.columns.type'),
            search: false,
            dataIndex: 'type',
            render: (_: any, record: { type: number, base_url: string | "", id: number }) => {
                const channel = CHANNEL_TYPE_OPTIONS.find(option => option.key === record.type) || {
                    text: '未知',
                    color: 'default',
                    icon: <OpenAIOutlined/>
                };
                if (record.type === 7007 && record.base_url !== '') {//Shell API特殊优化
                    return (
                        <Popover
                            title={t('channelsTable.popover.channelInfo')}
                            content={
                                <>
                                    <a href={`${record.base_url}`} target="_blank" rel="noopener noreferrer">
                                        {`${record.base_url}`}
                                    </a>
                                    {/* 根据用户权限决定是否显示复制密钥按钮 */}
                                    {(userState.user.role === 100 || // 超级管理员
                                      (userState.user.role <= 10 && hasPermission(userState.user.admin_access_flags, Permission.ChannelKey))) && (
                                        <Button type="link" onClick={async () => {
                                            const res = await API.get(`/api/channel/${record.id}`);
                                            const {success, message, data} = res.data;
                                            if (success && data && data.key) {
                                                await copy(data.key);
                                                AntdMessage.success(t('channelsTable.messages.copySuccess'));
                                            } else if (data && data.key && data.key.length === 0) {
                                                AntdMessage.error(t('channelsTable.messages.emptyKey'));
                                            } else {
                                                AntdMessage.error(t('channelsTable.messages.copyFailed', { message }));
                                            }
                                        }}>
                                            {t('channelsTable.actions.copyKey')}
                                        </Button>
                                    )}
                                </>
                            }
                        >
                            <Tag color={channel.color} icon={<IconFont type="icon-run_shell"/>}>
                                {channel.text}
                            </Tag>
                        </Popover>
                    );
                }
                return channel ?
                    <Tag color={channel.color} icon={channel.icon ? <IconFont type={channel.icon as string}/> :
                        <OpenAIOutlined/>}>{channel.text}</Tag> : '';
            },
        },
        {
            width: '4.5%',
            title: t('channelsTable.columns.responseTime'),
            search: false,
            dataIndex: 'response_time',
            render: (_text: any, record: any) => renderResponseTime(record.response_time),
        },
        {
            width: '11%',
            title: t('channelsTable.columns.group'),
            dataIndex: 'group',
            renderFormItem: () => {
                return (
                    <Select
                        mode="multiple"
                        style={{ width: '100%' }}
                        dropdownStyle={{ 
                            minWidth: '320px',    // 进一步减小下拉菜单的最小宽度
                            maxWidth: '480px'     // 进一步减小下拉菜单的最大宽度
                        }}
                        placeholder={t('channelsTable.placeholder.selectGroup')}
                        allowClear
                        showSearch
                        optionLabelProp="label"
                        filterOption={(input: string, option?: DefaultOptionType) => {
                            if (!option) return false;
                            const group = groups.find(g => g.name === option.value);
                            if (!group) return false;
                            
                            const searchText = input.toLowerCase();
                            return (
                                group.name.toLowerCase().includes(searchText) ||
                                group.display_name.toLowerCase().includes(searchText) ||
                                group.description?.toLowerCase().includes(searchText)
                            );
                        }}
                    >
                        {groups.map(group => (
                            <Select.Option 
                                key={group.id} 
                                value={group.name}
                                label={group.display_name}
                            >
                                <div style={{ 
                                    padding: '8px 12px',
                                    minWidth: '300px',     // 进一步减小选项内容最小宽度
                                    maxWidth: '460px'      // 进一步减小选项内容最大宽度
                                }}>
                                    <div style={{ 
                                        display: 'flex', 
                                        justifyContent: 'space-between',
                                        alignItems: 'center',
                                        marginBottom: '4px'
                                    }}>
                                        <span style={{ 
                                            color: group.color_mapping,
                                            fontWeight: 500,
                                            flex: 1,  // 让名称占据剩余空间
                                            marginRight: '8px'  // 与倍率标签保持间距
                                        }}>
                                            {group.display_name}
                                        </span>
                                        <span style={{ 
                                            padding: '2px 8px',
                                            borderRadius: '4px',
                                            fontSize: '12px',
                                            whiteSpace: 'nowrap',  // 防止倍率标签换行
                                            backgroundColor: group.convert_ratio > 1 ? 'rgba(255, 77, 79, 0.1)' : 'rgba(82, 196, 26, 0.1)',
                                            color: group.convert_ratio > 1 ? '#ff4d4f' : '#52c41a'
                                        }}>
                                            {group.convert_ratio.toFixed(1)}x
                                        </span>
                                    </div>
                                    {group.description && (
                                        <DescriptionText text={group.description} />
                                    )}
                                </div>
                            </Select.Option>
                        ))}
                    </Select>
                );
            },
            render: (text: string | string[]) => {
                // 如果text为null或undefined，返回空
                if (!text) {
                    return null;
                }

                // 如果是字符串且包含逗号，先转换成数组
                if (typeof text === 'string' && text.includes(',')) {
                    text = text.split(',');
                }

                if (Array.isArray(text)) {
                    return text.map(group => {
                        const groupInfo = groups.find(g => g.name === group);
                        return groupInfo ? (
                            <Tooltip 
                                key={group}
                                title={
                                    <div>
                                        <div><strong>{t('common.name')}:</strong> {group}</div>
                                        <div><strong>{t('common.displayName')}:</strong> {groupInfo.display_name}</div>
                                        {groupInfo.description && (
                                            <div><strong>{t('common.description')}:</strong> {groupInfo.description}</div>
                                        )}
                                        <div><strong>{t('common.ratio')}:</strong> {groupInfo.convert_ratio}x</div>
                                    </div>
                                }
                            >
                                <Tag 
                                    color={groupInfo.color_mapping}
                                    style={{ marginBottom: '4px', cursor: 'help' }}
                                >
                                    {groupInfo.display_name}
                                </Tag>
                            </Tooltip>
                        ) : renderUserGroupTag(group);
                    });
                } else {
                    const groupInfo = groups.find(g => g.name === text);
                    return groupInfo ? (
                        <Tooltip
                            title={
                                <div>
                                    <div><strong>{t('common.name')}:</strong> {text}</div>
                                    <div><strong>{t('common.displayName')}:</strong> {groupInfo.display_name}</div>
                                    {groupInfo.description && (
                                        <div><strong>{t('common.description')}:</strong> {groupInfo.description}</div>
                                    )}
                                    <div><strong>{t('common.ratio')}:</strong> {groupInfo.convert_ratio}x</div>
                                </div>
                            }
                        >
                            <Tag 
                                color={groupInfo.color_mapping}
                                style={{ cursor: 'help' }}
                            >
                                {groupInfo.display_name}
                            </Tag>
                        </Tooltip>
                    ) : renderUserGroupTag(text);
                }
            },
            search: {
                order: 98,
            },
        },
        {
            width: '6.5%',
            title: t('channelsTable.columns.status'),
            dataIndex: 'status',
            renderFormItem: () => {
                return <Select placeholder={t('channelsTable.placeholder.selectStatus')} allowClear>
                    <Select.Option value="1">{t('channelsTable.status.normal')}</Select.Option>
                    <Select.Option value="2">{t('channelsTable.status.manualDisabled')}</Select.Option>
                    <Select.Option value="3">{t('channelsTable.status.waitingRetry')}</Select.Option>
                    <Select.Option value="4">{t('channelsTable.status.suspended')}</Select.Option>
                    <Select.Option value="5">{t('channelsTable.status.partiallyDisabled')}</Select.Option>
                </Select>;
            },
            render: (_text: any, record: { status: number; disableReason: string; }) => renderChannelStatusTag(record),
            disable: true,
        },
        {
            title: t('channelsTable.columns.disableReason'),
            dataIndex: 'disableReason',
            key: 'disableReason',
            width: 200,
            hideInTable: true,
            renderFormItem: ({defaultRender, ...rest}) => {
                return <Select {...rest} placeholder={t('common.selectPlaceholder', { name: t('channelsTable.columns.disableReason') })} allowClear>
                    <Select.Option value="account_deactivated">{t('channelsTable.disableReasons.account_deactivated')}</Select.Option>
                    <Select.Option value="quota_exceeded">{t('channelsTable.disableReasons.quota_exceeded')}</Select.Option>
                    <Select.Option value="rate_limit_exceeded">{t('channelsTable.disableReasons.rate_limit_exceeded')}</Select.Option>
                    <Select.Option value="invalid_key">{t('channelsTable.disableReasons.invalid_key')}</Select.Option>
                    <Select.Option value="internal_server_error">{t('channelsTable.disableReasons.internal_server_error')}</Select.Option>
                    <Select.Option value="connection_error">{t('channelsTable.disableReasons.connection_error')}</Select.Option>
                </Select>;
            },
            search: {
                transform: (value) => {
                    return {
                        disableReason: value
                    };
                }
            }
        },
        {
            width: '5.5%',
            title: t('channelsTable.columns.models'),
            dataIndex: 'models',
            renderFormItem: (_, { type, defaultRender, formItemProps, fieldProps, ...rest }, form) => {
                if (type === 'form') {
                    return null;
                }
                return (
                    <Select
                        mode="tags"
                        style={{ width: '100%' }}
                        options={AUTOCOMPLETE_MODEL_NAMES.map(name => ({ value: name, label: name }))}
                        {...fieldProps}
                        placeholder={t('channelsTable.placeholder.inputSelectModel')}
                        allowClear
                        showSearch
                        filterOption={(input, option) => {
                            if (typeof option?.label === 'string') {
                                return option.label.toLowerCase().includes(input.toLowerCase());
                            }
                            return false;
                        }}
                    />
                );
            },
            render: (_, record) => renderModels(record.models, 0, 0),
            search: {
                order: 98,
            },
        },
        {
            width: '6%',
            title: t('channelsTable.columns.quota'),
            search: false,
            key: 'balance_and_used',
            render: (_: any, record: any) => {
                const isCustomBalance = record.custom_balance_limit && record.custom_balance_limit > 0;
                const isShellApiUnlimitedQuotaToken = record.type === 7007 && record.balance > 9999990;
                const supportedType = [1, 4, 8, 5, 10, 12, 13, 7007, 7005, 7002, 7011]

                if (!isCustomBalance && !supportedType.includes(record.type)) {
                    const usageAmount = `$${(record.used_quota / 500000).toFixed(2)}`;
                    return (
                        <Popover
                            title={t('channelsTable.quota.usageAmount').replace('{amount}', usageAmount)}
                            content={t('channelsTable.quota.updateNotSupported')}
                            mouseEnterDelay={0.5}
                        >
                            <Tag
                                icon={<HistoryOutlined/>}
                                bordered={false}
                                color={"default"}
                            >
                                {(record.used_quota / 500000).toFixed(2)}
                            </Tag>
                        </Popover>
                    );
                }

                return (
                    <Popover
                        title={t('channelsTable.quota.details')}
                        content={
                            <>
                                <p>{t('channelsTable.quota.usageAmount').replace('{amount}', `$${(record.used_quota / 500000).toFixed(2)}`)}</p>
                                <p>{t('channelsTable.quota.remainingAmount').replace('{amount}', `$${(record.balance).toFixed(2)}`)}</p>
                                {isCustomBalance ?
                                    <p>{t('channelsTable.quota.customTotalAmount').replace('{amount}', `$${(record.custom_balance_limit).toFixed(2)}`)}</p> : <></>}
                            </>
                        }
                        mouseEnterDelay={0.5}
                    >
                        <Tag
                            icon={<DollarCircleOutlined/>}
                            bordered={false}
                            onClick={() => updateChannelBalance(record.id, record.name)}
                            color={isCustomBalance ? 'volcano' : isShellApiUnlimitedQuotaToken ? 'green' : 'cyan'}
                            style={{cursor: 'pointer'}}
                        >
                            {isShellApiUnlimitedQuotaToken ? t('channelsTable.quota.sufficient') : (record.balance).toFixed(2)}
                        </Tag>
                    </Popover>
                );
            }
        },
        {
            width: '4.5%',
            title: t('channelsTable.columns.fusing'),
            align: 'center',
            dataIndex: 'overFrequencyAutoDisable',
            render: (_: any, record: any) => <OverFrequencySwitch record={record}/>,
            renderFormItem: ({defaultRender, ...rest}) => {
                return <Select {...rest} placeholder={t('channelsTable.placeholder.selectFusingStatus')} allowClear>
                    <Select.Option value="1">{t('common.enabled')}</Select.Option>
                    <Select.Option value="0">{t('common.disabled')}</Select.Option>
                </Select>;
            },
        },
        {
            width: '4.5%',
            title: t('channelsTable.columns.priority'),
            align: 'center',
            search: false,
            dataIndex: 'sort',
            render: (_, channel: { sort: any; id: number; }) => {
                return <InputNumber
                    variant={"filled"}
                    controls={false}
                    size="small"
                    value={channel.sort}
                    min={0}
                    style={{maxWidth: '60px'}}
                    onPressEnter={async (e) => {
                        ('value' in e.target) && await manageChannel(channel.id, 'sort', e.target.value);
                    }}
                />;
            },
        },
        {
            width: '4.5%',
            title: t('channelsTable.columns.weight'),
            align: 'center',
            search: false,
            dataIndex: 'weight',

            render: (_: any, channel: { weight: any; id: number; }) => {
                return <InputNumber
                    variant={"filled"}
                    controls={false}
                    size="small"
                    value={channel.weight}
                    min={0}
                    style={{maxWidth: '60px'}}
                    onPressEnter={async (e) => {
                        ('value' in e.target) && await manageChannel(channel.id, 'weight', e.target.value);
                    }}
                />;
            },
        },
        {
            width: '4.5%',
            title: t('channelsTable.columns.retryInterval'),
            align: 'center',
            search: false,
            dataIndex: 'retryInterval',
            render: (_: any, record: { retryInterval: any; id: number; }) => {
                return <InputNumber
                    variant={"filled"}
                    controls={false}
                    size="small"
                    step="60"
                    min={-1}
                    value={record.retryInterval}
                    style={{maxWidth: '60px'}}
                    onPressEnter={async (e) => {
                        ('value' in e.target) && await manageChannel(record.id, 'retryInterval', e.target.value);
                    }}
                />;
            },
        },
        {
            width: '10.5%',
            title: t('channelsTable.columns.actions'),
            key: 'action',
            search: false,
            fixed: isMobile() ? '' : 'right',
            render: (record: { id: any; name: any; status?: any; models: string; }) => (
                <Space>
                    <Dropdown
                        overlay={
                            <Menu
                                style={{ maxHeight: '300px', overflowY: 'auto' }}
                            >
                                {record.models.split(',').map(model => (
                                    <Menu.Item key={model} onClick={() => testChannel(record.id, record.name, model)}>
                                        {model}
                                    </Menu.Item>
                                ))}
                            </Menu>
                        }
                    >
                        <Button
                            {...actionButtonProps}
                            icon={testingChannelIds.includes(record.id) ? <HourglassTwoTone spin/> : <BulbTwoTone/>}
                            onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                testChannel(record.id, record.name);
                            }}
                        />
                    </Dropdown>
                    <Tooltip mouseEnterDelay={0.6} title={record.status === 1 ? '禁用' : '启用'}>
                        <Button
                            {...actionButtonProps}
                            icon={record.status === 1 ? <StopTwoTone/> : <PlayCircleTwoTone/>} onClick={async () => {
                            await manageChannel(record.id, record.status === 1 ? 'disable' : 'enable');
                        }}/>
                    </Tooltip>
                    {/* 根据用户权限决定是否显示编辑按钮 */}
                    {(userState.user.role === 100 || // 超级管理员
                      hasWritePermission(userState.user.admin_access_flags, Permission.Channel, Permission.ChannelWrite)) && (
                        <Button
                            {...actionButtonProps}
                            icon={<EditTwoTone/>}
                            onClick={() => handleEditClick(record.id, false)}
                        />
                    )}
                    <Dropdown overlay={renderMenu(record)}>
                        <Button
                            {...actionButtonProps}
                            icon={<ToolTwoTone/>}
                        />
                    </Dropdown>
                </Space>
            ),

        },
        {
            title: t('channelsTable.columns.key'),
            dataIndex: 'key',
            hideInTable: true,
        },
        {
            title: t('channelsTable.columns.base'),
            dataIndex: 'base_url',
            hideInTable: true,
        },
    ];

    function switchUnbindMode() {
        setUnbindOnly(!unbindOnly);
        safeAction('reset');
    }

    function switchSortMode() {
        setIsSortByIds(!isSortByIds);
        safeAction('reset');
    }

    const ondeleteMenuItemsClick: MenuProps['onClick'] = async ({key}) => {
        switch (key) {
            case '1':
                await deleteDisabledChannelsByType(2);
                break;
            case '2':
                await deleteDisabledChannelsByType(3);
                break;
            case '3':
                await deleteDisabledChannelsByType(4);
                break;
            case '5':
                await deleteChannelByDisableReason('account_deactivated');
                break;
            case '6':
                await deleteChannelByDisableReason('quota_exceeded');
                break;
            case '7':
                await deleteChannelByDisableReason('rate_limit_exceeded');
                break;
            case '8':
                await deleteChannelByDisableReason('invalid_key');
                break;
            case '9':
                await deleteChannelByDisableReason('connection_error');
                break;
            case '10':
                await deleteChannelByDisableReason('internal_server_error');
                break;
            default:
                break; // 可以根据需要添加更多的情况
        }
    };

    const onTestMenuItemsClick: MenuProps['onClick'] = async ({key}) => {
        if (key === 'all') {
            await testChannels();
        } else {
            await testChannels(parseInt(key));
        }
    };

    const deleteMenuItems: MenuProps['items'] = [
        {
            label: t('channelsTable.menu.deleteManualDisabled'),
            key: '1',
        },
        {
            label: t('channelsTable.menu.deleteWaitingRetry'),
            key: '2',
        },
        {
            label: t('channelsTable.menu.deleteSuspended'),
            key: '3',
        },
        {
            type: 'divider',
        },
        {
            label: t('channelsTable.menu.deleteDisabledAccount'),
            key: '5',
        },
        {
            label: t('channelsTable.menu.deleteQuotaExceeded'),
            key: '6',
        },
        {
            label: t('channelsTable.menu.deleteRateLimitExceeded'),
            key: '7',
        },
        {
            label: t('channelsTable.menu.deleteInvalidKey'),
            key: '8',
        },
        {
            label: t('channelsTable.menu.deleteConnectionError'),
            key: '9',
        },
        {
            label: t('channelsTable.menu.deleteInternalServerError'),
            key: '10',
        },
    ];

    const testMenuItems: MenuProps['items'] = [
        {
            label: t('channelsTable.menu.testAll'),
            key: 'all',
        },
        {
            label: t('channelsTable.menu.testNormal'),
            key: '1',
        },
        {
            label: t('channelsTable.menu.testManualDisabled'),
            key: '2',
        },
        {
            label: t('channelsTable.menu.testWaitingRetry'),
            key: '3',
        },
        {
            label: t('channelsTable.menu.testSuspended'),
            key: '4',
        },
    ];

    const TestButtonWithTooltip = () => (
        <Tooltip title={t('channelsTable.tooltip.testNote')}>
            <Space>
                {t('buttonText.test')}
                <InfoCircleOutlined style={{ color: '#faad14' }} />
            </Space>
        </Tooltip>
    );

    // 获取渠道统计数据
    const fetchChannelStatistics = async () => {
        setStatisticsLoading(true);
        try {
            const params = new URLSearchParams();
            if (statisticsFilter.statusFilter !== 0) {
                params.append('statusFilter', statisticsFilter.statusFilter.toString());
            }
            if (statisticsFilter.disableReason !== 'all') {
                params.append('disableReason', statisticsFilter.disableReason);
            }
            if (statisticsFilter.groupBy !== 'server') {
                params.append('groupBy', statisticsFilter.groupBy);
            }
            
            const response = await API.get(`/api/channel/statistics?${params.toString()}`);
            const { success, data, message } = response.data;
            
            if (success) {
                setStatisticsData(data || []);
            } else {
                AntdMessage.error(`获取统计数据失败: ${message}`);
            }
        } catch (error) {
            showError(error);
        } finally {
            setStatisticsLoading(false);
        }
    };

    // 打开统计模态框
    const handleStatisticsClick = () => {
        setStatisticsVisible(true);
        fetchChannelStatistics();
        // 自动获取创建速度数据
        fetchCreationSpeedData();
    };

    // 统计过滤条件变化
    const handleStatisticsFilterChange = (key, value) => {
        setStatisticsFilter(prev => ({
            ...prev,
            [key]: value
        }));
    };

    // 应用统计过滤
    const applyStatisticsFilter = () => {
        fetchChannelStatistics();
    };

    // 统计表格列定义
    const statisticsColumns: any[] = [
        {
            title: statisticsFilter.groupBy === 'domain' ? '渠道名称(域名邮箱)' : '备注(服务器名称)',
            dataIndex: 'group_name',
            key: 'group_name',
            width: isMobile() ? 120 : 200,
            render: (text: string) => (
                <div style={{ 
                    fontSize: isMobile() ? '12px' : '14px',
                    wordBreak: 'break-word',
                    lineHeight: '1.2'
                }}>
                    {text || '未知'}
                </div>
            ),
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => (a.group_name || '').localeCompare(b.group_name || ''),
        },
        {
            title: '总数',
            dataIndex: 'total_count',
            key: 'total_count',
            width: isMobile() ? 60 : 80,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => a.total_count - b.total_count,
            render: (value: number) => <Statistic value={value} valueStyle={{ fontSize: isMobile() ? '12px' : '14px' }} />,
        },
        {
            title: '正常',
            dataIndex: 'active_count',
            key: 'active_count',
            width: isMobile() ? 60 : 80,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => a.active_count - b.active_count,
            render: (value: number) => (
                <Statistic 
                    value={value} 
                    valueStyle={{ fontSize: isMobile() ? '12px' : '14px', color: '#52c41a' }} 
                />
            ),
        },
        {
            title: '禁用',
            dataIndex: 'disabled_count',
            key: 'disabled_count',
            width: isMobile() ? 60 : 80,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => a.disabled_count - b.disabled_count,
            render: (value: number) => (
                <Statistic 
                    value={value} 
                    valueStyle={{ fontSize: isMobile() ? '12px' : '14px', color: '#ff4d4f' }} 
                />
            ),
        },
        {
            title: '其他',
            dataIndex: 'unknown_count',
            key: 'unknown_count',
            width: isMobile() ? 60 : 80,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => a.unknown_count - b.unknown_count,
            render: (value: number) => (
                <Statistic 
                    value={value} 
                    valueStyle={{ fontSize: isMobile() ? '12px' : '14px', color: '#faad14' }} 
                />
            ),
        },
        {
            title: isMobile() ? '禁用率' : '禁用率',
            dataIndex: 'disabled_rate',
            key: 'disabled_rate',
            width: isMobile() ? 70 : 90,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => a.disabled_rate - b.disabled_rate,
            render: (value: number) => (
                <Statistic 
                    value={value} 
                    precision={1}
                    suffix="%" 
                    valueStyle={{ 
                        fontSize: isMobile() ? '12px' : '14px', 
                        color: value > 50 ? '#ff4d4f' : value > 20 ? '#faad14' : '#52c41a' 
                    }} 
                />
            ),
        },
        {
            title: '符合条件',
            dataIndex: 'filtered_count',
            key: 'filtered_count',
            width: isMobile() ? 70 : 90,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => a.filtered_count - b.filtered_count,
            render: (value: number) => (
                <Statistic 
                    value={value} 
                    valueStyle={{ fontSize: isMobile() ? '12px' : '14px', color: '#1890ff' }} 
                />
            ),
        },
        {
            title: '条件比例',
            dataIndex: 'filtered_rate',
            key: 'filtered_rate',
            width: isMobile() ? 70 : 90,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => a.filtered_rate - b.filtered_rate,
            render: (value: number) => (
                <Statistic 
                    value={value} 
                    precision={1}
                    suffix="%" 
                    valueStyle={{ fontSize: isMobile() ? '12px' : '14px', color: '#1890ff' }} 
                />
            ),
        },
        {
            title: isMobile() ? '平均余额' : '平均已用余额',
            dataIndex: 'average_used_quota',
            key: 'average_used_quota',
            width: isMobile() ? 80 : 120,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => a.average_used_quota - b.average_used_quota,
            render: (value: number) => (
                <Statistic 
                    value={value / 500000} 
                    precision={2}
                    prefix="$"
                    valueStyle={{ fontSize: isMobile() ? '12px' : '14px', color: '#722ed1' }} 
                />
            ),
        },
        {
            title: isMobile() ? '最新创建' : '最新正常渠道创建时间',
            dataIndex: 'latest_active_created_time',
            key: 'latest_active_created_time',
            width: isMobile() ? 100 : 180,
            sorter: (a: ChannelStatisticsItem, b: ChannelStatisticsItem) => (a.latest_active_created_time || 0) - (b.latest_active_created_time || 0),
            render: (timestamp: number | undefined) => {
                if (!timestamp) {
                    return <Text type="secondary" style={{ fontSize: isMobile() ? '10px' : '12px' }}>无数据</Text>;
                }
                const now = moment();
                const createTime = moment.unix(timestamp);
                const diffHours = now.diff(createTime, 'hours');
                
                let color = '#52c41a'; // 绿色 - 正常
                if (diffHours > 24) {
                    color = '#ff4d4f'; // 红色 - 超过24小时
                } else if (diffHours > 12) {
                    color = '#faad14'; // 黄色 - 超过12小时
                }
                
                return (
                    <div>
                        <div style={{ 
                            color, 
                            fontSize: isMobile() ? '10px' : '12px', 
                            fontWeight: 'bold',
                            lineHeight: '1.2'
                        }}>
                            {createTime.format(isMobile() ? 'MM-DD HH:mm' : 'MM-DD HH:mm')}
                        </div>
                        <div style={{ 
                            fontSize: isMobile() ? '9px' : '10px', 
                            color: '#999',
                            lineHeight: '1.2'
                        }}>
                            {diffHours < 1 ? '刚刚' : `${diffHours}小时前`}
                        </div>
                    </div>
                );
            },
        },
        {
            title: '创建趋势',
            dataIndex: 'group_name',
            key: 'creation_trend',
            width: isMobile() ? 80 : 100,
            render: (groupName: string) => {
                // 使用真实的创建速度数据，如果没有数据则显示提示
                const realData = creationSpeedData[groupName] || [];
                
                // 如果没有数据，显示提示
                if (realData.length === 0) {
                    return (
                        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                            <div style={{ 
                                width: '60px', 
                                height: '30px', 
                                display: 'flex', 
                                alignItems: 'center', 
                                justifyContent: 'center',
                                fontSize: '10px',
                                color: '#999',
                                border: '1px dashed #d9d9d9',
                                borderRadius: '4px'
                            }}>
                                无数据
                            </div>
                            <Button 
                                type="link" 
                                size="small" 
                                style={{ padding: '0', height: 'auto', fontSize: '10px' }}
                                onClick={() => handleDetailClick(groupName)}
                                disabled={realData.length === 0}
                            >
                                详情
                            </Button>
                        </div>
                    );
                }

                // 使用真实数据，取最近7个数据点
                const displayData = realData.slice(-7);

                const option = {
                    animation: false,
                    grid: {
                        left: 2,
                        right: 2,
                        top: 2,
                        bottom: 2,
                        containLabel: false
                    },
                    xAxis: {
                        type: 'category',
                        data: displayData.map(item => item.time_point),
                        show: false
                    },
                    yAxis: {
                        type: 'value',
                        show: false
                    },
                    series: [{
                        data: displayData.map(item => item.creation_count),
                        type: 'line',
                        smooth: true,
                        symbol: 'circle',
                        symbolSize: 2,
                        lineStyle: {
                            color: '#1890ff',
                            width: 1.5
                        },
                        itemStyle: {
                            color: '#1890ff'
                        },
                        areaStyle: {
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                                { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
                            ])
                        }
                    }],
                    tooltip: {
                        trigger: 'axis',
                        formatter: function(params: any) {
                            const dataIndex = params[0].dataIndex;
                            const item = displayData[dataIndex];
                            return `
                                <div style="font-size: 12px;">
                                    <div><strong>${groupName}</strong></div>
                                    <div>时间: ${formatTimestamp(item.time_point, speedTimeGranularity)}</div>
                                    <div>创建数: ${item.creation_count}</div>
                                    <div>累计: ${item.cumulative_count}</div>
                                </div>
                            `;
                        },
                        textStyle: {
                            fontSize: 10
                        }
                    }
                };

                return (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <ReactECharts
                            option={option}
                            style={{ width: '60px', height: '30px' }}
                            opts={{ renderer: 'canvas' }}
                        />
                        <Button 
                            type="link" 
                            size="small" 
                            style={{ padding: '0', height: 'auto', fontSize: '10px' }}
                            onClick={() => handleDetailClick(groupName)}
                        >
                            详情
                        </Button>
                    </div>
                );
            },
        },
    ];

    // 统计模态框内容
    const renderStatisticsModal = () => (
        <Modal
            title={
                <Space>
                    <BarChartOutlined />
                    {statisticsFilter.groupBy === 'domain' ? '渠道统计 - 按域名分组' : '渠道统计 - 按服务器分组'}
                </Space>
            }
            open={statisticsVisible}
            onCancel={() => setStatisticsVisible(false)}
            width={isMobile() ? '95%' : 1200}
            footer={[
                <Button key="close" onClick={() => setStatisticsVisible(false)}>
                    关闭
                </Button>,
                <Button key="refresh" type="primary" onClick={fetchChannelStatistics} loading={statisticsLoading}>
                    刷新数据
                </Button>
            ]}
        >
            <div style={{ marginBottom: 16 }}>
                <Card size="small" title="过滤条件">
                    <Row gutter={[8, 8]}>
                        <Col xs={24} sm={12} md={6}>
                            <label>分组方式:</label>
                            <Select
                                style={{ width: '100%', marginTop: 4 }}
                                value={statisticsFilter.groupBy}
                                onChange={(value) => handleStatisticsFilterChange('groupBy', value)}
                            >
                                <Select.Option value="server">备注(服务器名称)</Select.Option>
                                <Select.Option value="domain">渠道名称(域名邮箱)</Select.Option>
                            </Select>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                            <label>状态过滤:</label>
                            <Select
                                style={{ width: '100%', marginTop: 4 }}
                                value={statisticsFilter.statusFilter}
                                onChange={(value) => handleStatisticsFilterChange('statusFilter', value)}
                            >
                                <Select.Option value={0}>全部状态</Select.Option>
                                <Select.Option value={1}>仅启用</Select.Option>
                                <Select.Option value={3}>仅禁用</Select.Option>
                            </Select>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                            <label>禁用原因:</label>
                            <Select
                                style={{ width: '100%', marginTop: 4 }}
                                value={statisticsFilter.disableReason}
                                onChange={(value) => handleStatisticsFilterChange('disableReason', value)}
                            >
                                <Select.Option value="all">全部原因</Select.Option>
                                <Select.Option value="account_deactivated">账号停用</Select.Option>
                                <Select.Option value="quota_exceeded">配额超限</Select.Option>
                                <Select.Option value="rate_limit_exceeded">速率限制</Select.Option>
                                <Select.Option value="invalid_key">密钥无效</Select.Option>
                                <Select.Option value="connection_error">连接错误</Select.Option>
                            </Select>
                        </Col>
                        <Col xs={24} sm={12} md={6}>
                            <label>&nbsp;</label>
                            <Button 
                                type="primary" 
                                style={{ width: '100%', marginTop: 4 }}
                                onClick={applyStatisticsFilter}
                                loading={statisticsLoading}
                            >
                                应用过滤
                            </Button>
                        </Col>
                    </Row>
                </Card>
            </div>

            <Spin spinning={statisticsLoading}>
                <div style={{ marginBottom: 16 }}>
                    <Row gutter={[8, 8]}>
                        <Col xs={12} sm={8} md={5}>
                            <Card>
                                <Statistic
                                    title={isMobile() ? (statisticsFilter.groupBy === 'domain' ? '总域名数' : '总服务器数') : (statisticsFilter.groupBy === 'domain' ? '总域名数' : '总服务器数')}
                                    value={statisticsData.length}
                                    prefix={<PieChartOutlined />}
                                    valueStyle={{ fontSize: isMobile() ? '18px' : '24px' }}
                                />
                            </Card>
                        </Col>
                        <Col xs={12} sm={8} md={5}>
                            <Card>
                                <Statistic
                                    title="总渠道数"
                                    value={statisticsData.reduce((sum, item) => sum + item.total_count, 0)}
                                    prefix={<BarChartOutlined />}
                                    valueStyle={{ fontSize: isMobile() ? '18px' : '24px' }}
                                />
                            </Card>
                        </Col>
                        <Col xs={12} sm={8} md={5}>
                            <Card>
                                <Statistic
                                    title={isMobile() ? '平均禁用率' : '平均禁用率'}
                                    value={statisticsData.length > 0 ? 
                                        (statisticsData.reduce((sum, item) => sum + item.disabled_rate, 0) / statisticsData.length) : 0
                                    }
                                    precision={1}
                                    suffix="%"
                                    valueStyle={{ 
                                        fontSize: isMobile() ? '18px' : '24px',
                                        color: statisticsData.length > 0 && 
                                        (statisticsData.reduce((sum, item) => sum + item.disabled_rate, 0) / statisticsData.length) > 30 
                                        ? '#ff4d4f' : '#52c41a' 
                                    }}
                                />
                            </Card>
                        </Col>
                        <Col xs={12} sm={8} md={5}>
                            <Card>
                                <Statistic
                                    title={isMobile() ? '符合条件' : '符合条件总数'}
                                    value={statisticsData.reduce((sum, item) => sum + item.filtered_count, 0)}
                                    valueStyle={{ fontSize: isMobile() ? '18px' : '24px', color: '#1890ff' }}
                                />
                            </Card>
                        </Col>
                        <Col xs={12} sm={8} md={4}>
                            <Card>
                                <Statistic
                                    title={isMobile() ? '平均余额' : '平均已用余额'}
                                    value={statisticsData.length > 0 ? 
                                        (statisticsData.reduce((sum, item) => sum + item.average_used_quota, 0) / statisticsData.length / 500000) : 0
                                    }
                                    precision={2}
                                    prefix="$"
                                    valueStyle={{ fontSize: isMobile() ? '18px' : '24px', color: '#722ed1' }}
                                />
                            </Card>
                        </Col>
                    </Row>
                </div>

                {/* 添加手机端分页器样式 */}
                <style>
                    {isMobile() ? `
                        .mobile-pagination .ant-pagination-options {
                            display: flex !important;
                            flex-wrap: wrap;
                            justify-content: center;
                            margin-left: 8px !important;
                        }
                        .mobile-pagination .ant-pagination-options-size-changer {
                            margin-right: 8px !important;
                        }
                        .mobile-pagination .ant-select-selector {
                            font-size: 12px !important;
                            min-height: 28px !important;
                        }
                        .mobile-pagination .ant-pagination-item {
                            min-width: 28px !important;
                            height: 28px !important;
                            line-height: 26px !important;
                            font-size: 12px !important;
                        }
                        .mobile-pagination .ant-pagination-prev,
                        .mobile-pagination .ant-pagination-next {
                            min-width: 28px !important;
                            height: 28px !important;
                            line-height: 26px !important;
                        }
                    ` : ''}
                </style>

                <Table
                    columns={isMobile() ? statisticsColumns.filter((col, index) => {
                        // 手机端只显示关键列：分组名称、总数、正常、禁用、禁用率、平均余额
                        return [0, 1, 2, 3, 5, 8].includes(index);
                    }) : statisticsColumns}
                    dataSource={statisticsData}
                    rowKey="group_name"
                    size="small"
                    pagination={{
                        ...statisticsPagination,
                        total: statisticsData.length,
                        size: isMobile() ? 'small' : 'default', // 手机端使用小尺寸分页器
                        onChange: (page, pageSize) => {
                            setStatisticsPagination(prev => ({
                                ...prev,
                                current: page,
                                pageSize: pageSize || prev.pageSize
                            }));
                        },
                        onShowSizeChange: (current, size) => {
                            setStatisticsPagination(prev => ({
                                ...prev,
                                current: 1, // 切换每页条数时回到第一页
                                pageSize: size
                            }));
                        },
                        // 手机端优化的分页器样式
                        className: isMobile() ? 'mobile-pagination' : '',
                        style: isMobile() ? { 
                            textAlign: 'center', 
                            padding: '16px 8px',
                            fontSize: '12px'
                        } : {}
                    }}
                    scroll={{ x: isMobile() ? 600 : 800 }}
                />

                {/* 简化的图表控制区域 */}
                <div style={{ marginTop: 16 }}>
                    <Card size="small" title="时间粒度设置">
                        <Row gutter={[8, 8]}>
                            <Col xs={24} sm={12} md={8}>
                                <label>时间粒度:</label>
                                <Select
                                    style={{ width: '100%', marginTop: 4 }}
                                    value={speedTimeGranularity}
                                    onChange={(value) => {
                                        setSpeedTimeGranularity(value);
                                        // 时间粒度改变时自动重新获取数据，直接传递新值
                                        setTimeout(() => fetchCreationSpeedData(value), 100);
                                    }}
                                >
                                    <Select.Option value="day">按天统计</Select.Option>
                                    <Select.Option value="hour">按小时统计</Select.Option>
                                </Select>
                            </Col>
                            <Col xs={24} sm={12} md={16}>
                                <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                                    <div>按天: 显示最近30天数据 | 按小时: 显示最近24小时数据</div>
                                    <div>图表会自动加载并显示在下方</div>
                                </div>
                            </Col>
                        </Row>
                    </Card>
                </div>

                {/* 汇总对比图表区域 - 始终显示 */}
                <div style={{ marginTop: 16 }}>
                    <Card size="small" title={`创建数量TOP10分组对比图 (${speedTimeGranularity === 'day' ? '按天' : '按小时'})`}>
                        {speedDataLoading ? (
                            <div style={{ textAlign: 'center', padding: '60px 0' }}>
                                <Spin size="large" />
                                <div style={{ marginTop: 16, color: '#666' }}>正在加载图表数据...</div>
                            </div>
                        ) : Object.keys(creationSpeedData).length > 0 ? (
                            renderOverallChart()
                        ) : (
                            <div style={{ textAlign: 'center', padding: '60px 0' }}>
                                <div style={{ fontSize: '14px', color: '#999', marginBottom: 8 }}>
                                    暂无创建速度数据
                                </div>
                                <div style={{ fontSize: '12px', color: '#ccc' }}>
                                    请检查是否有渠道创建记录
                                </div>
                            </div>
                        )}
                    </Card>
                </div>
            </Spin>
        </Modal>
    );

    // 获取创建速度数据
    const fetchCreationSpeedData = async (granularity?: 'day' | 'hour') => {
        setSpeedDataLoading(true);
        try {
            const params = new URLSearchParams({
                status: statisticsFilter.statusFilter.toString(),
                disable_reason: statisticsFilter.disableReason,
                group_by: statisticsFilter.groupBy,
                time_granularity: granularity || speedTimeGranularity
            });

            const response = await API.get(`/api/channel/creation_speed_statistics?${params}`);
            if (response.data.success) {
                // 接口返回的数据格式是 {data: {groupName: [items...]}, success: true}
                // 直接使用返回的分组数据
                const groupedData: Record<string, ChannelCreationSpeedItem[]> = response.data.data || {};
                
                // 对每个组的数据按时间排序
                Object.keys(groupedData).forEach(groupName => {
                    if (groupedData[groupName] && Array.isArray(groupedData[groupName])) {
                        groupedData[groupName].sort((a, b) => a.time_point - b.time_point);
                    }
                });
                
                setCreationSpeedData(groupedData);
                AntdMessage.success(`获取创建速度数据成功，共 ${Object.keys(groupedData).length} 个分组`);
            } else {
                AntdMessage.error(`获取创建速度数据失败: ${response.data.message}`);
                setCreationSpeedData({});
            }
        } catch (error) {
            console.error('获取创建速度数据失败:', error);
            AntdMessage.error('获取创建速度数据失败');
            setCreationSpeedData({});
        } finally {
            setSpeedDataLoading(false);
        }
    };

    // 渲染整体汇总图表
    const renderOverallChart = () => {
        if (!creationSpeedData || Object.keys(creationSpeedData).length === 0) {
            return (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                    <div style={{ fontSize: '14px', color: '#999' }}>
                        暂无数据，请先获取图表数据
                    </div>
                </div>
            );
        }

        // 计算每个分组的总创建数，并过滤掉0值
        const groupTotals = Object.keys(creationSpeedData).map(groupName => {
            const totalCreation = creationSpeedData[groupName].reduce((sum, item) => sum + item.creation_count, 0);
            return {
                groupName,
                totalCreation,
                data: creationSpeedData[groupName]
            };
        }).filter(group => group.totalCreation > 0); // 只保留有创建数据的分组

        // 按总创建数降序排序，取前10名
        const top10Groups = groupTotals
            .sort((a, b) => b.totalCreation - a.totalCreation)
            .slice(0, 10);

        if (top10Groups.length === 0) {
            return (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                    <div style={{ fontSize: '14px', color: '#999' }}>
                        暂无有效的创建数据
                    </div>
                </div>
            );
        }

        // 重新构建creationSpeedData，只包含前10名
        const filteredCreationSpeedData: Record<string, ChannelCreationSpeedItem[]> = {};
        top10Groups.forEach(group => {
            filteredCreationSpeedData[group.groupName] = group.data;
        });

        // 获取所有时间点
        const allTimePoints = new Set<number>();
        Object.values(filteredCreationSpeedData).forEach(groupData => {
            groupData.forEach(item => allTimePoints.add(item.time_point));
        });
        const sortedTimePoints = Array.from(allTimePoints).sort((a, b) => a - b);

        // 生成颜色数组
        const colors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#fa8c16', '#13c2c2', '#eb2f96', '#2f54eb', '#389e0d'];

        // 为每个组生成数据系列
        const series: any[] = [];
        const groupNames = Object.keys(filteredCreationSpeedData);
        
        groupNames.forEach((groupName, index) => {
            const groupData = filteredCreationSpeedData[groupName];
            const dataMap = new Map(groupData.map(item => [item.time_point, item]));
            
            // 创建数量系列
            series.push({
                name: `${groupName} - 创建数`,
                type: 'line',
                data: sortedTimePoints.map(timePoint => {
                    const item = dataMap.get(timePoint);
                    return item ? item.creation_count : 0;
                }),
                smooth: true,
                lineStyle: {
                    color: colors[index % colors.length],
                    width: 2
                },
                itemStyle: {
                    color: colors[index % colors.length]
                },
                symbol: 'circle',
                symbolSize: 4
            });

            // 累计数量系列
            series.push({
                name: `${groupName} - 累计数`,
                type: 'line',
                data: sortedTimePoints.map(timePoint => {
                    const item = dataMap.get(timePoint);
                    return item ? item.cumulative_count : 0;
                }),
                smooth: true,
                lineStyle: {
                    color: colors[index % colors.length],
                    width: 2,
                    type: 'dashed'
                },
                itemStyle: {
                    color: colors[index % colors.length]
                },
                symbol: 'diamond',
                symbolSize: 4
            });
        });

        const option = {
            title: {
                text: `创建数量TOP10分组对比图 (${speedTimeGranularity === 'day' ? '按天' : '按小时'})`,
                subtext: `共 ${Object.keys(creationSpeedData).length} 个分组，显示前 ${top10Groups.length} 名`,
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                },
                subtextStyle: {
                    fontSize: 12,
                    color: '#666'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params: any) {
                    let result = `<div style="font-size: 12px;"><strong>${params[0].axisValue}</strong><br/>`;
                    params.forEach((param: any) => {
                        const color = param.color;
                        const seriesName = param.seriesName;
                        const value = param.value;
                        result += `<div style="margin: 2px 0;"><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>${seriesName}: ${value}</div>`;
                    });
                    result += '</div>';
                    return result;
                }
            },
            legend: {
                type: 'scroll',
                bottom: 10,
                data: series.map(s => s.name)
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: sortedTimePoints.map(timestamp => formatTimestamp(timestamp, speedTimeGranularity)),
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                name: '数量',
                nameTextStyle: {
                    fontSize: 12
                },
                axisLabel: {
                    fontSize: 10
                }
            },
            series: series,
            animation: true,
            animationDuration: 1000
        };

        return (
            <ReactECharts
                option={option}
                style={{ width: '100%', height: '600px' }}
                opts={{ renderer: 'canvas' }}
            />
        );
    };

    // 渲染单个分组的详细图表
    const renderGroupDetailChart = (groupName: string) => {
        const groupData = creationSpeedData[groupName];
        if (!groupData || groupData.length === 0) {
            return (
                <div style={{ textAlign: 'center', padding: '40px' }}>
                    <div style={{ fontSize: '14px', color: '#999' }}>
                        该分组暂无数据
                    </div>
                </div>
            );
        }

        const option = {
            title: {
                text: `${groupName} - 详细创建趋势`,
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'bold'
                }
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function(params: any) {
                    let result = `<div style="font-size: 12px;"><strong>${params[0].axisValue}</strong><br/>`;
                    params.forEach((param: any) => {
                        const color = param.color;
                        const seriesName = param.seriesName;
                        const value = param.value;
                        result += `<div style="margin: 2px 0;"><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>${seriesName}: ${value}</div>`;
                    });
                    result += '</div>';
                    return result;
                }
            },
            legend: {
                data: ['单期创建数', '累计创建数'],
                bottom: 10
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                boundaryGap: false,
                data: groupData.map(item => formatTimestamp(item.time_point, speedTimeGranularity)),
                axisLabel: {
                    rotate: 45,
                    fontSize: 10
                }
            },
            yAxis: {
                type: 'value',
                name: '数量',
                nameTextStyle: {
                    fontSize: 12
                },
                axisLabel: {
                    fontSize: 10
                }
            },
            series: [
                {
                    name: '单期创建数',
                    type: 'line',
                    data: groupData.map(item => item.creation_count),
                    smooth: true,
                    lineStyle: {
                        color: '#1890ff',
                        width: 2
                    },
                    itemStyle: {
                        color: '#1890ff'
                    },
                    symbol: 'circle',
                    symbolSize: 4,
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                            { offset: 1, color: 'rgba(24, 144, 255, 0.1)' }
                        ])
                    }
                },
                {
                    name: '累计创建数',
                    type: 'line',
                    data: groupData.map(item => item.cumulative_count),
                    smooth: true,
                    lineStyle: {
                        color: '#52c41a',
                        width: 2,
                        type: 'dashed'
                    },
                    itemStyle: {
                        color: '#52c41a'
                    },
                    symbol: 'diamond',
                    symbolSize: 4
                }
            ],
            animation: true,
            animationDuration: 1000
        };

        return (
            <ReactECharts
                option={option}
                style={{ width: '100%', height: '500px' }}
                opts={{ renderer: 'canvas' }}
            />
        );
    };

    // 处理详情按钮点击
    const handleDetailClick = (groupName: string) => {
        setSelectedGroupDetail(groupName);
        
        // 显示详情模态框
        Modal.info({
            title: `${groupName} - 创建趋势详情`,
            width: isMobile() ? '95%' : 1000,
            content: (
                <div style={{ marginTop: 16 }}>
                    {renderGroupDetailChart(groupName)}
                    <div style={{ marginTop: 16 }}>
                        <Row gutter={[16, 16]}>
                            <Col xs={24} sm={8}>
                                <Card size="small">
                                    <Statistic
                                        title="总创建数"
                                        value={creationSpeedData[groupName]?.reduce((sum, item) => sum + item.creation_count, 0) || 0}
                                        valueStyle={{ color: '#1890ff' }}
                                    />
                                </Card>
                            </Col>
                            <Col xs={24} sm={8}>
                                <Card size="small">
                                    <Statistic
                                        title="平均速度"
                                        value={creationSpeedData[groupName]?.length > 0 ? 
                                            (creationSpeedData[groupName].reduce((sum, item) => sum + item.creation_count, 0) / creationSpeedData[groupName].length).toFixed(1) : 0
                                        }
                                        suffix={`/${speedTimeGranularity === 'day' ? '天' : '小时'}`}
                                        valueStyle={{ color: '#52c41a' }}
                                    />
                                </Card>
                            </Col>
                            <Col xs={24} sm={8}>
                                <Card size="small">
                                    <Statistic
                                        title="峰值速度"
                                        value={Math.max(...(creationSpeedData[groupName]?.map(item => item.creation_count) || [0]))}
                                        suffix={`/${speedTimeGranularity === 'day' ? '天' : '小时'}`}
                                        valueStyle={{ color: '#fa8c16' }}
                                    />
                                </Card>
                            </Col>
                        </Row>
                    </div>
                </div>
            ),
            onOk() {},
            getContainer: () => document.getElementById('globalModalContainer') as HTMLElement,
        });
    };

    return (
        <>
            <ProTable
                rowSelection={isEditing && {
                    selectedRowKeys: selectedRowKeys,
                    onChange: setSelectedRowKeys,
                }}
                actionRef={ref as any}
                rowKey="id"
                scroll={{x: 1550}}
                request={request as any}
                search={{labelWidth: 'auto', span: isMobile() ? undefined : 4}}
                columns={columns as any}
                defaultSize="small"
                pagination={{
                    ...paginationProps,
                    total: channelsCount,
                }}
                columnsStateMap={columnsStateMap}
                onColumnsStateChange={handleColumnsStateChange}
                tableAlertRender={false}
                tableAlertOptionRender={false}
                cardBordered
                toolbar={{
                    title: t('channelPage.title'),
                    subTitle: (
                        <span onClick={async () => {
                            setRpmLoading(true);
                            await Promise.all([
                                getChannelCountByStatus(),
                                fetchChannelCreationRPM()
                            ]);
                            setRpmLoading(false);
                            // 强制更新组件
                            forceUpdate();
                        }} style={{ cursor: 'pointer' }}>
                          {channelStatusCount === undefined ? (
                              t('channelStatusCount.title')
                          ) : (
                              isMobile() ? (
                                  // 移动端显示所有维度，但采用更紧凑的布局
                                  <div style={{ fontSize: '11px', lineHeight: '1.2' }}>
                                      <div>启用: {channelStatusCount[1] || 0} | 禁用: {channelStatusCount[2] || 0}</div>
                                      <div>重试: {channelStatusCount[3] || 0} | 暂停: {channelStatusCount[4] || 0} | 部分: {channelStatusCount[5] || 0}</div>
                                      <div style={{ color: '#1890ff' }}>
                                          渠道创建RPM: {rpmLoading ? '更新中...' : channelCreationRPM}
                                      </div>
                                  </div>
                              ) : (
                                  // 桌面端显示完整信息
                                  <div>
                                      <Trans i18nKey="channelStatusCount.summary">
                                          Enabled {{enabled: channelStatusCount[1] || 0}} |
                                          Disabled {{disabled: channelStatusCount[2] || 0}} |
                                          Retry {{retry: channelStatusCount[3] || 0}} |
                                          Stopped {{stopped: channelStatusCount[4] || 0}} |
                                          Partially {{partial: channelStatusCount[5] || 0}}
                                      </Trans>
                                      <span style={{ marginLeft: '16px', color: '#1890ff', fontWeight: 'bold' }}>
                                          渠道创建RPM: {rpmLoading ? '更新中...' : channelCreationRPM}
                                      </span>
                                  </div>
                              )
                          )}
                        </span>
                    ),
                    actions: [
                        <Space>
                            {!isMobile() &&
                                <>
                                    <Switch checked={!unbindOnly} checkedChildren="全部" unCheckedChildren="未绑"
                                            onChange={switchUnbindMode}/>
                                    <Switch checked={!isSortByIds} checkedChildren="优先级" unCheckedChildren="按ID"
                                            onChange={switchSortMode}/>
                                    <Switch 
                                        checked={syncBothDB} 
                                        checkedChildren="双写" 
                                        unCheckedChildren="单写"
                                        onChange={setSyncBothDB}
                                        title="开启后操作将同时更新SQL和NoSQL数据库"
                                    />
                                </>
                            }
                            <Button 
                                icon={<BarChartOutlined />} 
                                onClick={handleStatisticsClick}
                                title="查看渠道统计分析"
                            >
                                统计
                            </Button>
                            {/* 根据用户权限决定是否显示删除按钮 */}
                            {(userState.user.role === 100 || // 超级管理员
                              hasWritePermission(userState.user.admin_access_flags, Permission.Channel, Permission.ChannelWrite)) && (
                                isEditing && !isMobile() ?
                                    <Popconfirm title="批量删除渠道？"
                                                description={'正在批量删除 ' + selectedRowKeys.length + ' 个渠道，该操作不可逆'}
                                                onConfirm={async () => {
                                                    await deleteChannelsByIds(selectedRowKeys);
                                                }}>
                                        <Button loading={deletingChannels} type="dashed" danger
                                                icon={<DeleteFilled/>}>删除</Button>
                                    </Popconfirm>
                                    :
                                    <Dropdown.Button
                                        onClick={() => deleteDisabledChannelsByType(99)}
                                        danger
                                        menu={{items: deleteMenuItems, onClick: ondeleteMenuItemsClick}}
                                        loading={deletingDisabledChannels}
                                        icon={<DeleteFilled/>}
                                    >
                                        {t('buttonText.delete')}
                                    </Dropdown.Button>
                            )}
                            {!isMobile() &&
                                <Button
                                    icon={<EditFilled/>}
                                    type={isEditing ? 'dashed' : 'default'}
                                    onClick={() => {
                                        if (isEditing) {
                                            setIsEditing(false);
                                            setSelectedRowKeys([]);
                                        } else {
                                            setIsEditing(true);
                                        }
                                    }}
                                >
                                    {isEditing ? t('buttonText.cancel') : t('buttonText.multiple')}
                                </Button>}
                            {!isMobile() && (
                                <Dropdown.Button
                                    onClick={() => testChannels()}
                                    menu={{items: testMenuItems, onClick: onTestMenuItemsClick}}
                                    icon={<BulbFilled/>}
                                >
                                    <TestButtonWithTooltip />
                                </Dropdown.Button>
                            )}
                            <Button icon={<WalletFilled/>} onClick={updateAllChannelsBalance}
                                    loading={updatingBalance}>{t('buttonText.updateBalance')}</Button>
                            {/* 根据用户权限决定是否显示添加按钮 */}
                            {(userState.user.role === 100 || // 超级管理员
                              hasWritePermission(userState.user.admin_access_flags, Permission.Channel, Permission.ChannelWrite)) && (
                                <Button icon={<DiffFilled/>} onClick={() => handleEditClick(-1, false)}
                                        type="primary">{t('buttonText.add')}</Button>
                            )}
                        </Space>,
                    ],
                }}
            />

            {/* 统计模态框 */}
            {renderStatisticsModal()}

            <EditChannelFormModal
                editingChannelId={editingChannelId}
                setEditingChannelId={setEditingChannelId}
                isEditChannelModalVisible={isModalVisible}
                setIsEditChannelModalVisible={setIsModalVisible}
                isCloneMode={isCopy}
                setIsCloneMode={setIsCopy}
                reload={() => safeAction('reload')}
            />
            <AdvanceTestFormModal
                editingChannelId={editingChannelId}
                setEditingChannelId={setEditingChannelId}
                isEditChannelModalVisible={advTestVisible}
                setIsEditChannelModalVisible={setAdvTestVisible}
                reload={() => safeAction('reload')}
            />
            <Drawer
                getContainer={() => document.getElementById('globalModalContainer') as HTMLElement}
                title="渠道测试详情"
                placement="top"
                onClose={() => setIsTestResponseDrawerVisible(false)}
                open={isTestResponseDrawerVisible}
                height="35vh"
            >
                {testResponse.warnings && testResponse.warnings.length > 0 && (
                    <div style={{ marginBottom: '16px' }}>
                        <h4 style={{ color: '#faad14', marginBottom: '8px' }}>警告信息：</h4>
                        <ul style={{ color: '#faad14', paddingLeft: '20px' }}>
                            {testResponse.warnings.map((warning, index) => (
                                <li key={index}>{warning}</li>
                            ))}
                        </ul>
                    </div>
                )}
                <pre>{JSON.stringify(testResponse, null, 2)}</pre>
            </Drawer>

            {/* 上游渠道充值抽屉 */}
            <Modal
                title={t('topup.cardRedemption')}
                open={topupVisible}
                onCancel={() => setTopupVisible(false)}
                footer={[
                    <Button key="cancel" onClick={() => setTopupVisible(false)}>
                        {t('common.cancel')}
                    </Button>,
                    <Button
                        key="submit"
                        type="primary"
                        loading={topupLoading}
                        onClick={executeTopup}
                    >
                        {t('topup.redeem')}
                    </Button>
                ]}
                destroyOnClose
                getContainer={() => document.getElementById('globalModalContainer') as HTMLElement}
            >
                <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                    {topupChannel && (
                        <Space direction="vertical" style={{ width: '100%' }}>
                            <div>
                                <Text strong>{t('channelsTable.columns.name')}:</Text> {topupChannel.name}
                            </div>
                            {topupChannel.type === 7007 && (
                                <div>
                                    <Text strong>API地址:</Text> {topupChannel.base_url}
                                </div>
                            )}
                        </Space>
                    )}
                    <Input
                        placeholder={t('topup.cardKeyPlaceholder')}
                        value={topupCode}
                        onChange={e => setTopupCode(e.target.value)}
                        onPressEnter={executeTopup}
                    />
                    <Alert
                        type="info"
                        showIcon
                        message={t('topup.rechargeReminder')}
                        description={
                            <ul style={{ paddingLeft: '20px', margin: '10px 0' }}>
                                <li>{t('channelsTable.topup.reminder1')}</li>
                                <li>{t('channelsTable.topup.reminder2')}</li>
                            </ul>
                        }
                    />
                </Space>
            </Modal>
        </>
    );
};
export default ChannelsTable;
