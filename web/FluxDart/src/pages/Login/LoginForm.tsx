import React, { lazy, useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
    createFromIconfontCN,
    GithubFilled,
    GoogleOutlined,
    LockOutlined,
    MobileOutlined,
    SafetyCertificateOutlined,
    UserOutlined,
    WechatFilled,
} from '@ant-design/icons';
import { LoginForm, ProFormCaptcha, ProFormText } from '@ant-design/pro-components';
import { App, Button, Checkbox, Col, Form, Image, Popover, Row, Space, Tabs } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import { API, isMobile, showError } from '../../helpers';
import { UserContext } from '../../context/User';
import { StatusContext } from '../../context/Status';
import { onGitHubOAuthClicked, onGoogleOAuthClicked } from '../../components/utils';
import Turnstile from 'react-turnstile';
import { getDefaultToken, getNew<PERSON><PERSON>tch<PERSON>, sendSMS, useUpdateStatusState } from '../../helpers/api-request-module';
import { loginIconStyles } from '../../constants';
import TelegramLoginButton from 'react-telegram-login';

const WeChatLoginModal = lazy(() => import('./WeChatLoginModal'));

const LoginPage: React.FC = () => {
    const { t } = useTranslation();
    useUpdateStatusState();
    const { message: AntdMessage } = App.useApp();
    const [userState, userDispatch] = useContext(UserContext);
    const [statusState] = useContext(StatusContext);
    const [loginType, setLoginType] = useState<'account' | 'phone'>('account');
    const [agreementChecked, setAgreementChecked] = useState(true);
    const turnstileEnabled = statusState.status.CustomVerificationTypesConfig.login.verificationTypes.includes('turnstile') && statusState.status.turnstile_check;
    const debugEnabled = statusState.status.DebugEnabled;

    const passwordLoginCaptchaEnabled = statusState.status.CustomVerificationTypesConfig.login.verificationTypes.includes('captcha');
    const smsLoginCaptchaEnabled = statusState.status.CustomVerificationTypesConfig.send_sms.verificationTypes.includes('captcha');

    const [turnstileToken, setTurnstileToken] = useState('');
    const [captchaId, setCaptchaId] = useState('');
    const [picPath, setPicPath] = useState('');
    const [showWeChatLoginModal, setShowWeChatLoginModal] = useState(false);
    const [timing, setTiming] = useState(false);
    const [timingCount, setTimingCount] = useState(60);
    const height = isMobile() ? '' : '69vh';

    const RegisterEnabled = statusState.status.PasswordRegisterEnabled && statusState.status.RegisterEnabled;

    const githubLoginEnabled = statusState.status.github_oauth;
    const googleLoginEnabled = statusState.status.GoogleOAuthEnabled;
    const wechatLoginEnabled = statusState.status.wechat_login;
    const telegramLoginEnabled = statusState.status.TelegramOAuthEnabled;
    const [showTgLoginPopover, setShowTgLoginPopover] = useState(false);

    const navigate = useNavigate();
    const [form] = Form.useForm();

    const IconFont = createFromIconfontCN({ scriptUrl: ['/font_4308471_sv1r52ct53n.js'] });

    useEffect(() => {
        if (userState.user.id !== 0) {
            AntdMessage.success(t('login.alreadyLoggedIn'), 0.6);
            navigate('/token');
        }
    }, []);

    useEffect(() => {
        let interval: NodeJS.Timeout | null = null;
        if (timing && timingCount > 0) {
            interval = setInterval(() => {
                setTimingCount((currentCount) => currentCount - 1);
            }, 1000);
        } else {
            setTimingCount(60);
            setTiming(false);
        }
        return () => {
            if (interval !== null) {
                clearInterval(interval);
            }
        };
    }, [timing, timingCount]);

    useEffect(() => {
        const handelVerificationTypes = async () => {
            if (passwordLoginCaptchaEnabled || smsLoginCaptchaEnabled) {
                await getNewCaptcha(setCaptchaId, setPicPath);
            }
        };
        handelVerificationTypes();
    }, []);

    const loginPreCheck = async (ignoreTurnstile: boolean = false): Promise<boolean> => {
        if (!agreementChecked) {
            AntdMessage.warning(t('login.agreementWarning'));
            return false;
        }
        if (!ignoreTurnstile && turnstileEnabled && !debugEnabled && turnstileToken === '') {
            AntdMessage.warning(t('login.turnstileWarning'));
            return false;
        }
        return true;
    };

    const onAccountLogin = async (values: { captcha?: string; username: string; password: string }) => {
        if (!await loginPreCheck()) return;
        const res = await API.post(
            `/api/user/login?turnstile=${turnstileToken}&captchaId=${captchaId}&captcha=${values.captcha}`,
            {
                username: values.username.trim(),
                password: values.password.trim(),
            }
        );
        const { success, message, data, token } = res.data;
        const cookie = res.headers['x-auth-token'];

        if (success) {
            userDispatch({ type: 'login', payload: data });
            token && localStorage.setItem('X-S-Token', token);
            cookie && window.parent.postMessage({ type: 'setCookie', cookie: cookie }, '*');
            if (values.password.includes('123456')) {
                AntdMessage.warning(t('login.weakPasswordWarning'), 1.5);
            } else {
                AntdMessage.success(t('login.welcomeMessage'), 0.6);
            }
            setTimeout(async () => {
                await getDefaultToken();
                navigate(data.role >= 100 ? '/channel' : '/token');
            }, 100);
        } else {
            AntdMessage.destroy();
            switch (message) {
                case t('login.captchaError'):
                    form.setFields([{ name: 'captcha', errors: [t('login.captchaError')], value: '' }]);
                    break;
                case t('login.credentialsError'):
                    form.setFields([
                        { name: 'username', errors: [''] },
                        {
                            name: 'password',
                            // @ts-ignore
                            errors: [<span>{t('login.credentialsError')} <Link to="/reset">{t('login.resetPassword')}</Link></span>],
                            value: '',
                        }
                    ]);
                    break;
                case t('login.captchaExpired'):
                    form.setFields([{ name: 'captcha', errors: [t('login.captchaExpired')], value: '' }]);
                    break;
                default:
                    AntdMessage.error(t('login.loginFailed', { message }), 0.8);
            }
            if (smsLoginCaptchaEnabled || passwordLoginCaptchaEnabled) {
                await getNewCaptcha(setCaptchaId, setPicPath);
            }
        }
    };

    const onGetSMS = async (mobile: string) => {
        if ((smsLoginCaptchaEnabled || passwordLoginCaptchaEnabled) && !form.getFieldValue('captcha')) {
            AntdMessage.warning(t('login.captchaRequired'));
            return;
        }
        const res = await sendSMS('sl', mobile, turnstileToken, captchaId, form.getFieldValue('captcha'));
        if (res.success) {
            AntdMessage.success(t('login.smsSent'), 0.8);
            setTiming(true);
            setTimingCount(60);
        } else {
            AntdMessage.error(res.message || t('login.smsSendFailed'), 2);
            if (smsLoginCaptchaEnabled || passwordLoginCaptchaEnabled) {
                await getNewCaptcha(setCaptchaId, setPicPath);
            }
            setTiming(false);
        }
    };

    const onPhoneLogin = async (values: { captcha: string; phone_number: string; sms_verification_code: string }) => {
        if (!await loginPreCheck()) return;
        const res = await API.post(
            `/api/user/login/sms?turnstile=${turnstileToken}&captchaId=${captchaId}&captcha=${values.captcha || ''}`,
            {
                phone_number: values.phone_number,
                sms_verification_code: values.sms_verification_code,
            }
        );
        const { success, message, data, token } = res.data;
        const cookie = res.headers['x-auth-token'];

        if (success) {
            userDispatch({ type: 'login', payload: data });
            token && localStorage.setItem('X-S-Token', token);
            cookie && window.parent.postMessage({ type: 'setCookie', cookie: cookie }, '*');
            AntdMessage.success(t('login.welcomeMessage'), 0.6);
            setTimeout(async () => {
                await getDefaultToken();
                navigate('/token');
            }, 100);
        } else {
            AntdMessage.error(t('login.loginFailed', { message }), 0.8);
            if (smsLoginCaptchaEnabled || passwordLoginCaptchaEnabled) {
                await getNewCaptcha(setCaptchaId, setPicPath);
            }
        }
    };

    const onSubmitWeChatVerificationCode = async (value: { wechat_verification_code: string }) => {
        if (!await loginPreCheck()) return;
        const res = await API.get(`/api/oauth/wechat?code=${value.wechat_verification_code}`);
        const { success, message, data } = res.data;
        if (success) {
            AntdMessage.success(t('login.loginSuccess'), 0.8);
            userDispatch({ type: 'login', payload: data });
            navigate('/token');
            setShowWeChatLoginModal(false);
        } else {
            AntdMessage.error(message);
        }
    };

    const handleTelegramLogin = async (response: { [x: string]: any }) => {
        const fields = ["id", "first_name", "last_name", "username", "photo_url", "auth_date", "hash", "lang"];
        const params = {};
        fields.forEach((field) => {
            if (response[field]) {
                params[field] = response[field];
            }
        });
        const res = await API.get(`/api/oauth/telegram/login/`, { params });
        const { success, message, data } = res.data;
        if (success) {
            AntdMessage.success(t('login.loginSuccess'), 0.8);
            userDispatch({ type: 'login', payload: data });
            navigate('/token');
        } else {
            showError(message);
        }
    };

    useEffect(() => {
        const updateStyles = () => {
            const devElements = document.querySelectorAll('.css-dev-only-do-not-override-191wglm') as NodeListOf<HTMLElement>;
            devElements.forEach(el => {
                if (isMobile()) {
                    el.style.paddingInline = '0';
                    el.style.paddingBlock = '0';
                }
            });

            const prodElements1 = document.querySelectorAll('.ant-pro-form-login-container, .css-341cqi');
            const prodElements2 = document.querySelectorAll(':where(.css-341cqi).ant-pro-form-login-container');
            const allProdElements = [...Array.from(prodElements1), ...Array.from(prodElements2)];
            allProdElements.forEach(el => {
                if (isMobile() && el instanceof HTMLElement) {
                    el.style.paddingInline = '0';
                    el.style.paddingBlock = '0';
                }
            });

            // 确保输入框和按钮宽度一致
            const formItems = document.querySelectorAll('.ant-pro-form-login-container .ant-form-item');
            formItems.forEach(el => {
                if (el instanceof HTMLElement) {
                    el.style.width = '100%';
                }
            });

            const inputs = document.querySelectorAll('.ant-pro-form-login-container .ant-input, .ant-pro-form-login-container .ant-input-password');
            inputs.forEach(el => {
                if (el instanceof HTMLElement) {
                    el.style.width = '100%';
                }
            });
        };

        window.addEventListener('resize', updateStyles);
        updateStyles();

        return () => window.removeEventListener('resize', updateStyles);
    }, []);

    return (
        <>
            <div style={{
                display: 'flex',
                justifyContent: 'center',
                paddingTop: '40px',
                paddingBottom: '20px',
                paddingLeft: '20px',
                paddingRight: '20px',
                boxSizing: 'border-box',
                overflow: 'auto',
            }}>
                <div style={{
                    width: '100%',
                    maxWidth: '400px',
                    margin: 'auto',
                    textAlign: 'center'
                }}>
                    {/* Page Title */}
                    <div style={{
                        fontSize: '32px',
                        fontWeight: 'bold',
                        marginBottom: '40px',
                        color: '#000'
                    }}>
                        {statusState.status.system_name}
                    </div>

                    {/* OAuth buttons */}
                    {(githubLoginEnabled || googleLoginEnabled || wechatLoginEnabled || telegramLoginEnabled) && (
                        <div style={{ marginBottom: '20px' }}>
                            <Space direction="vertical" style={{ width: '100%', gap: '10px' }}>
                                {googleLoginEnabled &&
                                    <Button
                                        block
                                        size="large"
                                        style={{
                                            backgroundColor: 'white',
                                            borderColor: '#dadce0',
                                            color: '#3c4043',
                                            height: '48px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            gap: '12px',
                                            borderRadius: '8px',
                                            fontWeight: '500',
                                            fontSize: '15px',
                                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                                            transition: 'all 0.2s ease'
                                        }}
                                        onMouseEnter={(e) => {
                                            e.currentTarget.style.backgroundColor = '#f8f9fa';
                                            e.currentTarget.style.transform = 'translateY(-1px)';
                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.currentTarget.style.backgroundColor = 'white';
                                            e.currentTarget.style.transform = 'translateY(0)';
                                            e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                                        }}
                                        onClick={async () => {
                                            if (!await loginPreCheck(true)) return;
                                            await onGoogleOAuthClicked(statusState.status.GoogleClientId)
                                        }}
                                    >
                                        <GoogleOutlined style={{ fontSize: '18px', color: '#4285f4' }} />
                                        Continue with Google
                                    </Button>
                                }
                                {githubLoginEnabled &&
                                    <Button
                                        block
                                        size="large"
                                        style={{
                                            backgroundColor: '#24292e',
                                            borderColor: '#24292e',
                                            color: 'white',
                                            height: '48px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            gap: '12px',
                                            borderRadius: '8px',
                                            fontWeight: '500',
                                            fontSize: '15px',
                                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                                            transition: 'all 0.2s ease'
                                        }}
                                        onMouseEnter={(e) => {
                                            e.currentTarget.style.backgroundColor = '#1c2128';
                                            e.currentTarget.style.transform = 'translateY(-1px)';
                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.currentTarget.style.backgroundColor = '#24292e';
                                            e.currentTarget.style.transform = 'translateY(0)';
                                            e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                                        }}
                                        onClick={async () => {
                                            if (!await loginPreCheck(true)) return;
                                            await onGitHubOAuthClicked(statusState.status.github_client_id)
                                        }}
                                    >
                                        <GithubFilled style={{ fontSize: '18px' }} />
                                        Continue with GitHub
                                    </Button>
                                }
                                {wechatLoginEnabled &&
                                    <Button
                                        block
                                        size="large"
                                        style={{
                                            backgroundColor: '#07c160',
                                            borderColor: '#07c160',
                                            color: 'white',
                                            height: '48px',
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            gap: '12px',
                                            borderRadius: '8px',
                                            fontWeight: '500',
                                            fontSize: '15px',
                                            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                                            transition: 'all 0.2s ease'
                                        }}
                                        onMouseEnter={(e) => {
                                            e.currentTarget.style.backgroundColor = '#06ad56';
                                            e.currentTarget.style.transform = 'translateY(-1px)';
                                            e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                                        }}
                                        onMouseLeave={(e) => {
                                            e.currentTarget.style.backgroundColor = '#07c160';
                                            e.currentTarget.style.transform = 'translateY(0)';
                                            e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                                        }}
                                        onClick={async () => {
                                            if (!await loginPreCheck(true)) return;
                                            setShowWeChatLoginModal(true)
                                        }}
                                    >
                                        <WechatFilled style={{ fontSize: '18px' }} />
                                        Continue with WeChat
                                    </Button>
                                }
                                {telegramLoginEnabled &&
                                    <Popover
                                        content={(
                                            <TelegramLoginButton
                                                botName={statusState.status.TelegramBotName}
                                                dataOnauth={handleTelegramLogin}
                                            />
                                        )}
                                        open={showTgLoginPopover}
                                        onOpenChange={(open) => setShowTgLoginPopover(open)}
                                    >
                                        <Button
                                            block
                                            size="large"
                                            style={{
                                                backgroundColor: '#0088cc',
                                                borderColor: '#0088cc',
                                                color: 'white',
                                                height: '48px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                justifyContent: 'center',
                                                gap: '12px',
                                                borderRadius: '8px',
                                                fontWeight: '500',
                                                fontSize: '15px',
                                                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
                                                transition: 'all 0.2s ease'
                                            }}
                                            onMouseEnter={(e) => {
                                                e.currentTarget.style.backgroundColor = '#0077b3';
                                                e.currentTarget.style.transform = 'translateY(-1px)';
                                                e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
                                            }}
                                            onMouseLeave={(e) => {
                                                e.currentTarget.style.backgroundColor = '#0088cc';
                                                e.currentTarget.style.transform = 'translateY(0)';
                                                e.currentTarget.style.boxShadow = '0 1px 3px rgba(0, 0, 0, 0.1)';
                                            }}
                                            onClick={() => setShowTgLoginPopover(true)}
                                        >
                                            <IconFont type="icon-telegram" style={{ fontSize: '18px' }} />
                                            Continue with Telegram
                                        </Button>
                                    </Popover>
                                }
                            </Space>

                            {/* Divider */}
                            <div style={{
                                display: 'flex',
                                alignItems: 'center',
                                margin: '20px 0 12px 0',
                                gap: '16px'
                            }}>
                                <div style={{
                                    flex: 1,
                                    height: '1px',
                                    backgroundColor: '#e8e8e8'
                                }} />
                                <span style={{
                                    color: '#999',
                                    fontSize: '14px',
                                    fontWeight: '400',
                                    padding: '0 8px'
                                }}>
                                    or
                                </span>
                                <div style={{
                                    flex: 1,
                                    height: '1px',
                                    backgroundColor: '#e8e8e8'
                                }} />
                            </div>
                        </div>
                    )}

                    {/* Login Form */}
                    <LoginForm
                        validateTrigger={'onBlur'}
                        form={form}
                        title=""
                        onFinish={loginType === 'account' ? onAccountLogin as any : onPhoneLogin}
                        style={{
                            width: '100%',
                            maxWidth: '400px'
                        }}
                        submitter={{
                            searchConfig: {
                                submitText: t('login.login')
                            },
                            submitButtonProps: {
                                size: 'large',
                                style: {
                                    width: '100%',
                                    height: '48px',
                                    borderRadius: '8px',
                                    fontSize: '15px',
                                    fontWeight: '500'
                                }
                            }
                        }}
                    >
                        {statusState.status.SMSVerificationEnabled &&
                            <Tabs
                                centered
                                activeKey={loginType}
                                onChange={(activeKey) => setLoginType(activeKey as 'account' | 'phone')}
                            >
                                <Tabs.TabPane key={'account'} tab={t('login.accountLogin')}/>
                                <Tabs.TabPane key={'phone'} tab={t('login.phoneLogin')}/>
                            </Tabs>
                        }

                    {loginType === 'account' && (
                        <>
                            <ProFormText
                                name="username"
                                fieldProps={{
                                    size: 'large',
                                    prefix: <UserOutlined style={loginIconStyles} />,
                                    style: { width: '100%' }
                                }}
                                placeholder={t('login.usernamePlaceholder')}
                                rules={[{required: true, message: t('login.usernameRequired')}]}
                                style={{ width: '100%' }}
                            />
                            <ProFormText.Password
                                name="password"
                                fieldProps={{
                                    size: 'large',
                                    prefix: <LockOutlined style={loginIconStyles} />,
                                    style: { width: '100%' }
                                }}
                                placeholder={t('login.passwordPlaceholder')}
                                rules={[
                                    {required: true, message: t('login.passwordRequired')},
                                    {max: 20, message: t('login.passwordMaxLength')}
                                ]}
                                style={{ width: '100%' }}
                            />
                            {passwordLoginCaptchaEnabled &&
                                <Row gutter={8}>
                                    <Col span={15}>
                                        <ProFormText
                                            name="captcha"
                                            fieldProps={{
                                                size: 'large',
                                                prefix: <SafetyCertificateOutlined style={loginIconStyles} />,
                                            }}
                                            placeholder={t('login.captchaPlaceholder')}
                                            rules={[{required: !debugEnabled, message: t('login.captchaRequired')}]}
                                        />
                                    </Col>
                                    <Col span={9}>
                                        <Popover content={<Image src={picPath}/>}>
                                            <Image
                                                preview={false}
                                                src={picPath}
                                                height={40}
                                                onClick={async () => {
                                                    await getNewCaptcha(setCaptchaId, setPicPath);
                                                }}
                                                placeholder={true}
                                            />
                                        </Popover>
                                    </Col>
                                </Row>
                            }
                        </>
                    )}

                    {loginType === 'phone' && (
                        <>
                            <ProFormText
                                fieldProps={{
                                    size: 'large',
                                    prefix: <MobileOutlined style={loginIconStyles} />,
                                    style: { width: '100%' }
                                }}
                                name="phone_number"
                                placeholder={t('login.phonePlaceholder')}
                                rules={[
                                    {required: true, message: t('login.phoneRequired')},
                                    {
                                        pattern: /^1\d{10}$/,
                                        message: t('login.phoneFormatError'),
                                        validateTrigger: 'onBlur',
                                    },
                                ]}
                                style={{ width: '100%' }}
                            />
                            {smsLoginCaptchaEnabled &&
                                <Row gutter={8}>
                                    <Col span={15}>
                                        <ProFormText
                                            name="captcha"
                                            fieldProps={{
                                                size: 'large',
                                                prefix: <SafetyCertificateOutlined style={loginIconStyles} />,
                                            }}
                                            placeholder={t('login.captchaPlaceholder')}
                                            rules={[{required: !debugEnabled, message: t('login.captchaRequired')}]}
                                        />
                                    </Col>
                                    <Col span={9}>
                                        <Popover content={<Image src={picPath}/>}>
                                            <Image
                                                preview={false}
                                                src={picPath}
                                                height={40}
                                                onClick={async () => {
                                                    await getNewCaptcha(setCaptchaId, setPicPath);
                                                }}
                                                placeholder={true}
                                            />
                                        </Popover>
                                    </Col>
                                </Row>
                            }
                            <ProFormCaptcha
                                name="sms_verification_code"
                                phoneName="phone_number"
                                onGetCaptcha={onGetSMS}
                                placeholder={t('login.smsCodePlaceholder')}
                                fieldProps={{
                                    size: 'large',
                                    prefix: <SafetyCertificateOutlined style={loginIconStyles} />,
                                    style: { width: '100%' }
                                }}
                                captchaTextRender={(timing, count) => {
                                    if (timing) {
                                        return t('login.smsCodeCountdown', { count: count });
                                    }
                                    return t('login.getSmsCode');
                                }}
                                captchaProps={{disabled: timing, size: 'large'}}
                                style={{ width: '100%' }}
                            />
                        </>
                    )}

                    {(turnstileEnabled) &&
                        <Turnstile
                            style={{marginBottom: '10px'}}
                            sitekey={statusState.status.turnstile_site_key}
                            onVerify={(token) => setTurnstileToken(token)}
                        />
                    }

                    <div style={{marginBottom: '24px'}}>
                        <Checkbox
                            defaultChecked={true}
                            onChange={e => setAgreementChecked(e.target.checked)}
                        >
                            {t('login.agreementText')} <Link to="/privacyPolicy">{t('login.privacyPolicy')}</Link> {t('login.and')} <Link
                            to="/serviceAgreement">{t('login.serviceAgreement')}</Link>
                        </Checkbox>
                    </div>
                </LoginForm>

                {/* Register Link - positioned outside the form, similar to the reference layout */}
                {RegisterEnabled && (
                    <div style={{
                        textAlign: 'center',
                        marginTop: '20px',
                        fontSize: '14px',
                        color: '#666'
                    }}>
                        <span>{t('login.noAccount')}</span>
                        <Link
                            to="/register"
                            style={{
                                color: '#1890ff',
                                textDecoration: 'none',
                                marginLeft: '4px',
                                fontWeight: '500'
                            }}
                        >
                            {t('login.register')}
                        </Link>
                    </div>
                )}
                </div>
            </div>

            <WeChatLoginModal
                showWeChatLoginModal={showWeChatLoginModal}
                setShowWeChatLoginModal={setShowWeChatLoginModal}
                statusState={statusState}
                onSubmitWeChatVerificationCode={onSubmitWeChatVerificationCode}
            />
        </>
    );
};

export default LoginPage;
export { LoginPage };
