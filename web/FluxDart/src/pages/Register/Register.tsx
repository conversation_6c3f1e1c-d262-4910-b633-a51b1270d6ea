import {A<PERSON>, But<PERSON>, Col, Form, Image, Input, Popover, Row, Space, Typography} from "antd";
import React, {useContext, useEffect, useState} from "react";
import {StatusContext} from "../../context/Status";
import {getNewCaptcha} from "../../helpers/api-request-module";
import Turnstile from "react-turnstile";
import {API, isMobile, showError} from "../../helpers";
import {Link, useNavigate} from "react-router-dom";
import {useTranslation} from "react-i18next";
import VerifyModal from "../../components/VerifyModal";

const MyPage = () => {
    const { t } = useTranslation();
    const navigate = useNavigate();
    const {message: AntdMessage, modal} = App.useApp();
    const [statusState,] = useContext(StatusContext);
    const [form] = Form.useForm();
    const RegisterInfo = statusState.status.RegisterInfo

    const EmailVerificationEnabled = statusState.status.email_verification

    const SMSVerificationEnabled = statusState.status.SMSVerificationEnabled && statusState.status.SMSRegisterEnabled

    // 这是针对提交注册表单时的验证码
    const CaptchaEnabled = statusState.status.CaptchaCheckEnabled && statusState.status.CustomVerificationTypesConfig.register.verificationTypes.includes('captcha')

    const [captchaId, setCaptchaId] = useState('');
    const [picPath, setPicPath] = useState('');
    const [inputCaptcha, setInputCaptcha] = useState('');

    // 只要turnstile_check开启，其他有一个开启了，就显示 Turnstile
    const showTurnstile = statusState.status.turnstile_check && (
        (EmailVerificationEnabled && statusState.status.CustomVerificationTypesConfig.verification.verificationTypes.includes('turnstile')) ||
        (SMSVerificationEnabled && statusState.status.CustomVerificationTypesConfig.send_sms.verificationTypes.includes('turnstile')) ||
        statusState.status.CustomVerificationTypesConfig.register.verificationTypes.includes('turnstile')
    );

    const TurnstileSiteKey = statusState.status.turnstile_site_key
    const [turnstileToken, setTurnstileToken] = useState('');

    const [verifyPopupVisibleType, setVerifyPopupVisibleType] = useState<"email" | "sms" | "none">('none');

    const [submitting, setSubmitting] = useState(false);

    const [sendingSMS, setSendingSMS] = useState(false);
    const [sendingEmail, setSendingEmail] = useState(false);

    const [countdownSMS, setCountdownSMS] = useState(0);
    const [countdownEmail, setCountdownEmail] = useState(0);

    const [verifyType, setVerifyType] = useState<"email" | "sms">('email');

    // 验证模态框状态
    const [isEmailVerifyModalVisible, setIsEmailVerifyModalVisible] = useState(false);
    const [isSmsVerifyModalVisible, setIsSmsVerifyModalVisible] = useState(false);
    const [isRegisterVerifyModalVisible, setIsRegisterVerifyModalVisible] = useState(false);
    
    // 验证码状态
    const [emailCaptchaId, setEmailCaptchaId] = useState('');
    const [emailPicPath, setEmailPicPath] = useState('');
    const [emailCaptcha, setEmailCaptcha] = useState('');
    
    const [smsCaptchaId, setSmsCaptchaId] = useState('');
    const [smsPicPath, setSmsPicPath] = useState('');
    const [smsCaptcha, setSmsCaptcha] = useState('');
    
    const [registerCaptchaId, setRegisterCaptchaId] = useState('');
    const [registerPicPath, setRegisterPicPath] = useState('');
    const [registerCaptcha, setRegisterCaptcha] = useState('');
    
    // Turnstile 令牌
    const [emailTurnstileToken, setEmailTurnstileToken] = useState('');
    const [smsTurnstileToken, setSmsTurnstileToken] = useState('');
    const [registerTurnstileToken, setRegisterTurnstileToken] = useState('');
    
    // 验证配置
    const EmailCaptchaEnabled = statusState.status.CaptchaCheckEnabled && 
        statusState.status.CustomVerificationTypesConfig.verification.verificationTypes.includes('captcha');
    const EmailTurnstileEnabled = statusState.status.turnstile_check && 
        statusState.status.CustomVerificationTypesConfig.verification.verificationTypes.includes('turnstile');
    
    const SmsCaptchaEnabled = statusState.status.CaptchaCheckEnabled && 
        statusState.status.CustomVerificationTypesConfig.send_sms.verificationTypes.includes('captcha');
    const SmsTurnstileEnabled = statusState.status.turnstile_check && 
        statusState.status.CustomVerificationTypesConfig.send_sms.verificationTypes.includes('turnstile');
    
    const RegisterCaptchaEnabled = statusState.status.CaptchaCheckEnabled && 
        statusState.status.CustomVerificationTypesConfig.register.verificationTypes.includes('captcha');
    const RegisterTurnstileEnabled = statusState.status.turnstile_check && 
        statusState.status.CustomVerificationTypesConfig.register.verificationTypes.includes('turnstile');

    // 从 params 获取 aff，然后填入表单
    useEffect(() => {
        const aff_code = new URLSearchParams(window.location.search).get('aff_code');
        if (aff_code) {
            form.setFieldsValue({aff_code})
            AntdMessage.info('检测到邀请码，已自动填入！', 0.8);
        }
    }, []);

    // Get new captcha
    useEffect(() => {
        const onMount = async () => {
            if (RegisterInfo) {
                modal.info({
                    title: '通知',
                    content: RegisterInfo,
                    getContainer: () => document.getElementById('globalModalContainer') as HTMLElement,
                });
            }
            if (CaptchaEnabled) {
                await getNewCaptcha(setCaptchaId, setPicPath, setInputCaptcha);
            }
        }
        onMount().then();
    }, []);

    useEffect(() => {
        if (countdownSMS > 0) {
            const timer = setInterval(() => {
                setCountdownSMS(prevCountdown => prevCountdown - 1);
            }, 1000);
            return () => clearInterval(timer);
        }
    }, [countdownSMS]);

    useEffect(() => {
        if (countdownEmail > 0) {
            const timer = setInterval(() => {
                setCountdownEmail(prevCountdown => prevCountdown - 1);
            }, 1000);
            return () => clearInterval(timer);
        }
    }, [countdownEmail]);

    const popoverContent = (
        <Space direction="vertical" style={{width: 200}}>
            <Image
                preview={false}
                src={picPath}
                onClick={async () => await getNewCaptcha(setCaptchaId, setPicPath, setInputCaptcha)}
                placeholder={true}
            />
            <Input
                value={inputCaptcha}
                onChange={(e) => setInputCaptcha(e.target.value)}
            />
            <Button block type={'primary'} onClick={() => {
                setVerifyPopupVisibleType('none')
                if (verifyType === 'email') {
                    sendVerificationEmailCode(false).then()
                } else {
                    sendVerificationSmsCode(false).then()
                }
            }}>{t('register.confirm')}</Button>
        </Space>
    );

    const preCheck = async (verifyType: "email" | "sms") => {
        setVerifyType(verifyType);
        const email = form.getFieldValue('email');
        const phone_number = form.getFieldValue('phone_number');
        
        switch (verifyType) {
            case 'email':
                if (typeof email !== 'string' || email === '') {
                    AntdMessage.warning('请输入邮箱地址！');
                    return false;
                }
                if (EmailCaptchaEnabled || EmailTurnstileEnabled) {
                    setIsEmailVerifyModalVisible(true);
                    return false;
                }
                break;
            case 'sms':
                if (typeof phone_number !== 'string' || phone_number === '') {
                    AntdMessage.warning('请输入手机号！');
                    return false;
                }
                if (SmsCaptchaEnabled || SmsTurnstileEnabled) {
                    setIsSmsVerifyModalVisible(true);
                    return false;
                }
                break;
        }
        return true;
    }

    const onFinish = async (values: { [key: string]: string }) => {
        if (submitting) return;
        
        // 检查是否需要验证
        if (RegisterCaptchaEnabled || RegisterTurnstileEnabled) {
            setIsRegisterVerifyModalVisible(true);
            return;
        }
        
        await submitForm(values);
    };

    const sendVerificationEmailCode = async (Check: boolean) => {
        if (Check && !await preCheck('email')) return;
        setSendingEmail(true);
        try {
            const email = form.getFieldValue('email');
            const res = await API.get(`/api/verification?email=${email}&turnstile=${emailTurnstileToken}&captchaId=${emailCaptchaId}&captcha=${emailCaptcha}`);
            const {success, message} = res.data;
            if (success) {
                AntdMessage.success(t('register.emailCodeSent'));
                setCountdownEmail(30);
                setIsEmailVerifyModalVisible(false);
            } else {
                AntdMessage.error(message ? message : t('register.failMessage'));
            }
        } catch (e) {
            showError(e);
        } finally {
            setSendingEmail(false);
            setEmailCaptcha(''); // clear captcha
        }
    }

    const sendVerificationSmsCode = async (Check: boolean) => {
        if (Check && !await preCheck('sms')) return;
        setSendingSMS(true);
        try {
            const phone_number = form.getFieldValue('phone_number');
            const res = await API.get(`/api/send_sms?phone_number=${phone_number}&purpose=sv&turnstile=${smsTurnstileToken}&captchaId=${smsCaptchaId}&captcha=${smsCaptcha}`);
            const {success, message} = res.data;
            if (success) {
                AntdMessage.success(t('register.smsCodeSent'));
                setCountdownSMS(60);
                setIsSmsVerifyModalVisible(false);
            } else {
                AntdMessage.error(message);
            }
        } catch (e) {
            showError(e);
        } finally {
            setSendingSMS(false);
            setSmsCaptcha(''); // clear captcha
        }
    };

    // 新增表单提交实际处理函数
    const submitForm = async (values: { [key: string]: string }) => {
        setSubmitting(true);
        try {
            delete values.password2;
            const res = await API.post(`/api/user/register?captchaId=${registerCaptchaId}&captcha=${registerCaptcha}&turnstile=${registerTurnstileToken}`, values);
            const {success, message} = res.data;
            if (success) {
                navigate('/login');
                AntdMessage.success({content: t('register.successMessage'), duration: 2});
                setIsRegisterVerifyModalVisible(false);
            } else {
                AntdMessage.error(message ? message : t('register.failMessage'));
            }
        } catch (e) {
            showError(e);
        } finally {
            setSubmitting(false);
        }
    };

    return (
        <Row style={{
            display: 'flex',
            justifyContent: 'center',
            paddingTop: '40px',
            paddingBottom: '20px',
            paddingLeft: '20px',
            paddingRight: '20px'
        }}>
            <Col span={isMobile() ? 24 : 12} style={{maxWidth: 330}}>
                <Typography.Title level={2} style={{textAlign: 'center', marginBottom: '20px', marginTop: '0'}}>{t('register.title')}</Typography.Title>
                <Form
                    form={form}
                    onFinish={onFinish}
                    scrollToFirstError={true}
                    autoComplete={"new-password"}
                    name={'shell-api-register-form'}
                    size="large"
                    validateTrigger={'onBlur'}
                >
                    {!SMSVerificationEnabled && <Form.Item name="username" rules={[
                        {required: true, message: t('register.usernameRequired')},
                        {pattern: /^[^@]+$/, message: t('register.usernameNoAt')},
                        {pattern: /^[^\u4e00-\u9fa5]+$/, message: t('register.usernameNoChinese')},
                        {min: 4, max: 12, message: t('register.usernameLength')},
                    ]}>
                        <Input placeholder={t('register.usernamePlaceholder')}/>
                    </Form.Item>}

                    <Form.Item name="password" rules={[
                        {required: true, message: t('register.passwordRequired')},
                        {min: 8, max: 20, message: t('register.passwordLength')},
                    ]} hasFeedback>
                        <Input.Password placeholder={t('register.passwordPlaceholder')}/>
                    </Form.Item>

                    <Form.Item name="password2" dependencies={['password']} rules={[
                        {required: true, message: t('register.confirmPasswordRequired')},
                        ({getFieldValue}) => ({
                            validator(_, value) {
                                if (!value || getFieldValue('password') === value) {
                                    return Promise.resolve();
                                }
                                return Promise.reject(new Error(t('register.passwordMismatch')));
                            },
                        }),
                    ]} hasFeedback>
                        <Input.Password placeholder={t('register.confirmPasswordPlaceholder')}/>
                    </Form.Item>

                    {EmailVerificationEnabled &&
                        <>
                            <Form.Item name="email" rules={[
                                {type: 'email', message: t('register.emailInvalid')},
                                {required: true, message: t('register.emailRequired')},]}>
                                <Input placeholder={t('register.emailPlaceholder')} type="email"/>
                            </Form.Item>
                            <Form.Item name="verification_code"
                                       rules={[{required: true, message: t('register.emailCodeRequired')},]}>
                                <Row style={{display: 'flex', alignItems: 'center'}}>
                                    <Input placeholder={t('register.emailCodePlaceholder')} style={{flex: 1, marginRight: '8px'}}/>
                                    <Button disabled={countdownEmail > 0}
                                            onClick={() => sendVerificationEmailCode(true)}
                                            loading={sendingEmail}>
                                        {countdownEmail > 0 ? t('register.resendEmailCode', {seconds: countdownEmail}) : t('register.getEmailCode')}
                                    </Button>
                                </Row>
                            </Form.Item>
                        </>}

                    {SMSVerificationEnabled &&
                        <>
                            <Form.Item name="phone_number" rules={[
                                {required: true, message: t('register.phoneRequired')},
                                {pattern: /^1\d{10}$/, message: t('register.phoneInvalid')}
                            ]}>
                                <Input placeholder={t('register.phonePlaceholder')} type="tel"/>
                            </Form.Item>
                            <Form.Item
                                name="sms_verification_code"
                                rules={[{required: true, message: t('register.smsCodeRequired')},]}
                            >
                                <Row style={{display: 'flex', alignItems: 'center'}}>
                                    <Input placeholder={t('register.smsCodePlaceholder')} style={{flex: 1, marginRight: '8px'}}/>
                                    <Button disabled={countdownSMS > 0}
                                            onClick={() => sendVerificationSmsCode(true)}
                                            loading={sendingSMS}>
                                        {countdownSMS > 0 ? t('register.resendSmsCode', {seconds: countdownSMS}) : t('register.getSmsCode')}
                                    </Button>
                                </Row>
                            </Form.Item>
                        </>}

                    <Form.Item name={'aff_code'}>
                        <Input placeholder={t('register.inviteCodePlaceholder')}/>
                    </Form.Item>

                    <Button type="primary" htmlType="submit" block loading={submitting}>{t('register.submit')}</Button>
                </Form>

                {/* Login Link - positioned outside the form */}
                <div style={{
                    textAlign: 'center',
                    marginTop: '20px',
                    fontSize: '14px',
                    color: '#666'
                }}>
                    <span>{t('register.hasAccount')}</span>
                    <Link
                        to="/login"
                        style={{
                            color: '#1890ff',
                            textDecoration: 'none',
                            marginLeft: '4px',
                            fontWeight: '500'
                        }}
                    >
                        {t('register.goLogin')}
                    </Link>
                </div>
            </Col>
            
            {/* 邮箱验证模态框 */}
            <VerifyModal
                title={t('register.emailVerifyTitle')}
                statusState={statusState}
                turnstileEnabled={EmailTurnstileEnabled}
                captchaEnabled={EmailCaptchaEnabled}
                onConfirm={() => sendVerificationEmailCode(false)}
                setTurnstileToken={setEmailTurnstileToken}
                setCaptchaId={setEmailCaptchaId}
                picPath={emailPicPath}
                setPicPath={setEmailPicPath}
                captcha={emailCaptcha}
                setCaptcha={setEmailCaptcha}
                isModalVisible={isEmailVerifyModalVisible}
                setIsModalVisible={setIsEmailVerifyModalVisible}
            />
            
            {/* 短信验证模态框 */}
            <VerifyModal
                title={t('register.smsVerifyTitle')}
                statusState={statusState}
                turnstileEnabled={SmsTurnstileEnabled}
                captchaEnabled={SmsCaptchaEnabled}
                onConfirm={() => sendVerificationSmsCode(false)}
                setTurnstileToken={setSmsTurnstileToken}
                setCaptchaId={setSmsCaptchaId}
                picPath={smsPicPath}
                setPicPath={setSmsPicPath}
                captcha={smsCaptcha}
                setCaptcha={setSmsCaptcha}
                isModalVisible={isSmsVerifyModalVisible}
                setIsModalVisible={setIsSmsVerifyModalVisible}
            />
            
            {/* 注册验证模态框 */}
            <VerifyModal
                title={t('register.registerVerifyTitle')}
                statusState={statusState}
                turnstileEnabled={RegisterTurnstileEnabled}
                captchaEnabled={RegisterCaptchaEnabled}
                onConfirm={() => submitForm(form.getFieldsValue())}
                setTurnstileToken={setRegisterTurnstileToken}
                setCaptchaId={setRegisterCaptchaId}
                picPath={registerPicPath}
                setPicPath={setRegisterPicPath}
                captcha={registerCaptcha}
                setCaptcha={setRegisterCaptcha}
                isModalVisible={isRegisterVerifyModalVisible}
                setIsModalVisible={setIsRegisterVerifyModalVisible}
            />
        </Row>
    )
};

export default MyPage;