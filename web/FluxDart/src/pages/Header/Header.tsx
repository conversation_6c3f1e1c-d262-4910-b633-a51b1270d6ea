import React, {lazy, useContext, useEffect, useState} from 'react';
import {
    API,
    filterRoutes,
    getAdminStatus,
    getChatLinkCompatibleWithOldFormats,
    hasPermission,
    isAdmin,
    isMobile,
    Permission,
    printLicense,
    safeParseJSON,
    showError
} from '../../helpers';
import {
    ApiOutlined,
    CarryOutOutlined,
    CompassFilled,
    ContactsFilled,
    ControlFilled,
    DatabaseOutlined,
    DollarCircleOutlined,
    ExperimentOutlined,
    FilePptOutlined,
    FileTextOutlined,
    FilterFilled,
    FolderOpenFilled,
    FullscreenOutlined,
    GiftOutlined,
    HomeFilled,
    IdcardFilled,
    IdcardOutlined,
    InteractionFilled,
    LockFilled,
    LoginOutlined,
    LogoutOutlined,
    MessageFilled,
    MoneyCollectFilled,
    MoonOutlined,
    NotificationOutlined,
    PictureFilled,
    PieChartFilled,
    PlayCircleOutlined,
    ProductFilled,
    PropertySafetyFilled,
    ReadFilled,
    SecurityScanFilled,
    SettingFilled,
    ShopFilled,
    ShopOutlined,
    ShoppingFilled,
    SlackCircleFilled,
    <PERSON>Outlined,
    <PERSON>Outlined,
    UsergroupAddOutlined,
    HistoryOutlined,
    SwapOutlined,
    TrophyOutlined,
    BellOutlined,
} from '@ant-design/icons';
import {PageContainer, ProLayout} from '@ant-design/pro-components';
import {App as AntdApp, Button, Dropdown, Image, Popconfirm, Menu, Modal, Tag} from 'antd';
import {UserContext} from '../../context/User';
import {StatusContext} from '../../context/Status';
import {Link, useLocation, useNavigate} from 'react-router-dom';
import '../../i18n'; // 引入i18n配置
import {useTranslation} from 'react-i18next';
import App from '../../App';
import {getNewCheckinCaptcha, updateStatusState, updateUserState} from "../../helpers/api-request-module";
import {RouteConfig, SYSTEM_NAME} from '../../constants';
import NoticeModal from "./NoticeModal";
import {Icon} from '@iconify/react';
import { GlobalOutlined } from '@ant-design/icons';
import { CloseOutlined } from '@ant-design/icons';

const Query = lazy(() => import('../Query'));
const About = lazy(() => import('../About'));
const CheckinPopover = lazy(() => import('./CheckinVerifyModal'));
const LicenseExpireAlert = lazy(() => import('./LicenseExpireAlert'));
const FloatButton = lazy(() => import('./FloatButton'));

function Header({darkMode, setDarkMode}) {
    const {t, i18n} = useTranslation();
    const {message: AntdMessage} = AntdApp.useApp();
    const [licenseExpireMessage, setLicenseExpireMessage] = useState<string>('');
    const [shouldShowAlert, setShouldShowAlert] = useState(false);
    const [userState, userDispatch] = useContext(UserContext);
    const [statusState, statusDispatch] = useContext(StatusContext);

    let TurnstileEnabled = statusState.status.turnstile_check && statusState.status.CustomVerificationTypesConfig.checkin.verificationTypes.includes('turnstile');
    let CaptchaEnabled = statusState.status.CaptchaCheckEnabled && statusState.status.CustomVerificationTypesConfig.checkin.verificationTypes.includes('captcha');

    const [checkinCaptcha, setCheckinCaptcha] = useState('');//用户输入的验证码--用于签到
    const [captcha, setCaptcha] = useState('');//用户输入的验证码--用于签到
    const [picPath, setPicPath] = useState('');//验证码 base64--用于签到
    const [checkinPicPath, setCheckinPicPath] = useState('');//验证码 base64--用于签到
    const [captchaId, setCaptchaId] = useState('');
    const [checkinCaptchaId, setCheckinCaptchaId] = useState('');

    const [turnstileToken, setTurnstileToken] = useState('');
    const [checkinPopoverOpen, setCheckinPopoverOpen] = useState(false);

    const theme = statusState.customConfig.CustomTheme;

    const navigate = useNavigate();
    const location = useLocation();

    const [iframeUrl, setIframeUrl] = useState('');
    const [iframeVisible, setIframeVisible] = useState(false);

    const showIframe = (url: string | undefined) => {
        if (typeof url === 'string') {
            setIframeUrl(url);
            setIframeVisible(true);
        } else {
            console.error('Invalid URL for iframe');
        }
    };

    const hideIframe = () => {
        setIframeVisible(false);
        setIframeUrl('');
    };

    // 添加这个 useEffect 来监听路由变化
    useEffect(() => {
        hideIframe();
    }, [location]);

    useEffect(() => {
        const handleStorageChange = (event: any) => {
            if (event.key === 'USER_STATE' && event.newValue === null) {// 如果USER_STATE被清除，则执行登出操作
                userDispatch({type: 'logout'});
            }
        };

        window.addEventListener('storage', handleStorageChange);

        return () => {
            window.removeEventListener('storage', handleStorageChange);
        };
    }, [userDispatch]);

    useEffect(() => {
        printLicense(statusState.status.version, statusState.status.instanceId);
    }, []);

    const checkLicenseExpire = async () => {
        try {
            const data = await getAdminStatus();
            //如果LastLicenseCheckedTime存在,与本地时间对比
            if ('LastLicenseCheckedTime' in data && 'LastLicenseCheckedTime' in data) {
                const currentTime = new Date().getTime();
                const timeDifference = currentTime - data.LastLicenseCheckedTime * 1000;
                const hours = Math.floor(timeDifference / 3600000);
                const minutes = Math.floor((timeDifference - hours * 3600000) / 60000);
                if (timeDifference >= 1800000) {//超过30分钟
                    setShouldShowAlert(true);
                    setLicenseExpireMessage(`系统授权已过期${hours}小时${minutes}分钟，请先检查服务器时间确定没问题后联系管理员！(可以清理缓存刷新页面观察是否持续存在,如果刷新后消失则不用过于担心)`);
                }
            }
        } catch (error) {
            showError(error as Error);
        }
    }

    // 签到之前的准备
    const handelPreCheckin = async () => {
        if (!CaptchaEnabled && !TurnstileEnabled) {
            await handelDoCheckin();
        } else {
            setCheckinPopoverOpen(true);
        }
    };

    // 带验证信息签到
    const handelDoCheckin = async () => {
        try {
            const res = await API.post(`/api/user/checkin?turnstile=${turnstileToken}&captchaId=${checkinCaptchaId}&captcha=${checkinCaptcha}`);
            const {success, message} = res.data;
            if (success) {
                AntdMessage.success({content: message, duration: 1, key: 'checkin'});
                setCheckinPopoverOpen(false);
            } else if (message.includes('验证码错误')) {
                AntdMessage.warning({content: message, duration: 1, key: 'checkin'});
                await getNewCheckinCaptcha(setCheckinCaptchaId, setCheckinPicPath, setCheckinCaptcha);
            } else {
                AntdMessage.warning({content: message ? message : "签到失败", duration: 1, key: 'checkin'});
                setCheckinCaptcha('');
            }
        } catch (e) {
            showError(e);
        }
    };

    // 组件挂载时，如果用户已登录，更新用户状态，如果用户是管理员，获取管理员状态
    useEffect(() => {
        updateStatusState(statusDispatch).then();
        if (userState.user.role >= 100) {
            checkLicenseExpire().then();
        }
        if (userState.user.id !== 0) {
            updateUserState(userDispatch).then();
        }
    }, []);

    //注销登录
    async function logout(displayMessage = true) {
        await API.get('/api/user/logout');
        userDispatch({type: 'logout'});
        navigate('/login');
        displayMessage && AntdMessage.success(t('message.logoutSuccess'));
    }

    //清除本地缓存并注销登录
    const removeLocalStorage = async () => {
        localStorage.clear();
        await logout(false);
        statusDispatch({type: 'removeAll'});
        AntdMessage.success(t('message.removeLocalStorage.success'), 0.6);
        navigate('/login');
    };

    //菜单点击
    const avatarPropsMenuClick = async ({key}) => {
        if (key === 'profile' && location.pathname !== '/account/profile') {
            navigate('/account/profile');
        }
        if (key === 'recharge' && location.pathname !== '/account/topup/recharge') {
            navigate('/account/topup/recharge');
        }
        if (key === 'login' && location.pathname !== '/login') {
            navigate('/login');
        }
        if (key === 'logout') {
            await logout();
        }
        if (key === 'darkMode') {
            setDarkMode(!darkMode);
            statusDispatch({type: 'updateCustomConfig', payload: {...statusState.customConfig, darkMode: !darkMode}});
        }
        if (key === 'checkin') {
            await handelPreCheckin();
        }
        if (key === 'fullScreen') {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen().catch((e) => {   // 请求全屏
                    console.error(`进入全屏模式失败: ${e.message}`);
                });
            } else {
                if (document.exitFullscreen) {// 退出全屏
                    await document.exitFullscreen();
                }
            }
        }
        if (key === 'agencyCenter') {
            navigate('/agencyCenter/profile');
        }
    };

    const chatLinkArr = getChatLinkCompatibleWithOldFormats();

    const ADMIN_ACCESS_FLAGS = userState.user.admin_access_flags;

    //后续visible改为与UserRoles比大小，而不是 boolean，字段名称也对应改为minAccessRole
    // enum UserRoles {
    //     Guest = 0,
    //     User = 1,
    //     Admin = 10,
    //     Root = 100
    // }

    const navExtMenusTmp = JSON.parse(statusState.status.NavExtMenus || '[]')
    let navExtMenus = []
    if (navExtMenusTmp && navExtMenusTmp.length){
        navExtMenus = navExtMenusTmp.map((item: any) => {
            return {
                ...item,
                visible: item.need_login ? Boolean(userState.user.role >= 1) as boolean : true,
                icon: <Icon icon={item.icon} />,
                useIframe: item.useIframe || false,
                path: item.path
            }
        })
    }

    //路由配置
    const routes: any[] = [
        {
            path: (chatLinkArr && chatLinkArr.length === 1) ? `/chat/${chatLinkArr[0].type.toString()}-${chatLinkArr[0].route}` : '/chat',
            name: t('header.routes.chat'),
            icon: <MessageFilled/>,
            visible: statusState.status.GuestChatPageEnabled ? (chatLinkArr && chatLinkArr.length === 1) : ((userState.user.role >= 1) && chatLinkArr && chatLinkArr.length === 1)
        },
        {
            path: '/chat',
            name: t('header.routes.chat'),
            icon: <MessageFilled/>,
            routes: chatLinkArr && chatLinkArr.map(item => ({
                path: `/chat/${item.type}-${item.route}`,
                name: `${item.label ? item.label : t('header.routes.chat')}`,
                icon: <MessageFilled/>
            })),
            visible: statusState.status.GuestChatPageEnabled ? (chatLinkArr && chatLinkArr.length > 1) : ((userState.user.role >= 1) && chatLinkArr && chatLinkArr.length > 1)
        },
        // 新增 PptGen 路由，放在对话旁边
        {
            path: '/pptGen',
            name: t('header.routes.pptGen'),
            icon: <FilePptOutlined />,  // 你可以选择一个更合适的图标
            visible: statusState.status.PptGenPageEnabled && Boolean(userState.user.role >= 1) // 根据需要调整可见性条件
        },
        {
            path: '/chart',
            name: t('header.routes.chart'),
            icon: <PieChartFilled/>,
            visible: userState.user.role === 100
        },
        {
            
            path: '/onlineTopupRecord',
            name: t('header.routes.onlineTopupRecord'),
            icon: <HistoryOutlined/>,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.Log))
        },
        {
            path: '/agency',
            name: t('header.routes.agency'),
            icon: <ShopOutlined />,
            visible: userState.user.role === 100 && Boolean(statusState.status.AgentMenuEnabled)
        },
        {
            path: '/channel',
            name: t('header.routes.channel'),
            icon: <ControlFilled/>,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.Channel))
        },
        {
            path: '/channelScores',
            name: t('header.routes.channelScores'),
            icon: <TrophyOutlined />,
            label: (
                <span>
                    {t('menu.channelScores')}
                    <Tag 
                        color="blue" 
                        style={{ 
                            marginLeft: '4px', 
                            fontSize: '10px', 
                            lineHeight: '14px',
                            padding: '0 4px',
                            position: 'relative',
                            top: '-1px'
                        }}
                    >
                        Beta
                    </Tag>
                </span>
            ),
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.Channel))
        },
        {
            path: '/ability',
            name: t('header.routes.ability'),
            icon: <ApiOutlined/>,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.Ability))
        },
        {
            path: '/channelGroup',
            name: t('header.routes.channelGroup'),
            icon: <SlackCircleFilled/>,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.Channel))
        },
        {
            path: '/token',
            name: t('header.routes.token'),
            icon: <LockFilled/>,
            visible: Boolean(userState.user.role >= 1) as boolean
        },
        {
            path: '/log',
            name: t('header.routes.log'),
            icon: <FileTextOutlined/>,
            visible: userState.user.role >= 1  // 所有登录用户都可以查看日志菜单（至少可以查看自己的日志）
        },
        {
            path: '/midjourney',
            name: t('header.routes.midjourney'),
            icon: <PictureFilled/>,
            visible: userState.user.role >= 1 && statusState.status.MidjourneyEnabled  // 所有登录用户都可以查看Midjourney菜单
        },
        {
            path: '/task',
            name: t('header.routes.task'),
            icon: <PlayCircleOutlined/>,
            visible: userState.user.role >= 1  // 所有登录用户都可以查看任务菜单
        },
        {
            path: '/redemption',
            name: t('header.routes.redemption'),
            icon: <GiftOutlined/>,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.Redemption))
        },
        {
            path: '/group',
            name: t('header.routes.group'),
            icon: <UsergroupAddOutlined />,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.User))
        },
        {
            path: '/user',
            name: t('header.routes.user'),
            icon: <ContactsFilled/>,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.User))
        },
        {
            path: '/agencyUser',
            name: t('header.routes.user'),
            icon: <ContactsFilled/>,
            visible: userState.user.role === 5
        },
        {
            path: '/packagePlanAdmin',
            name: t('header.routes.packagePlanAdmin'),
            icon: <ShopFilled/>,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.PackagePlan))
        },
        {
            path: '/config',
            name: t('header.routes.config'),
            icon: <SettingFilled/>,
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.Setting))
        },
        {
            path: '/account',
            name: t('header.routes.account.default'),
            icon: <IdcardFilled/>,
            routes: [
                {
                    path: 'profile',
                    name: t('header.routes.account.profile'),
                    icon: <IdcardFilled/>
                },
                {
                    path: 'packagePlan/list',
                    name: t('header.routes.account.packagePlan.list'),
                    icon: <ShoppingFilled/>,
                    visible: userState.user.ShowPackagePlan
                },
                {
                    path: 'topup/recharge',
                    name: t('header.routes.account.recharge'),
                    icon: <MoneyCollectFilled/>,
                },
                {
                    path: 'topup/transfer',
                    name: t('header.routes.account.balanceTransfer'),
                    icon: <InteractionFilled/>,
                    visible: statusState.status.TransferEnabled
                },
                {
                    path: 'pricing',
                    name: t('header.routes.account.pricing'),
                    icon: <PropertySafetyFilled/>
                },
                {
                    path: 'notificationSettings',
                    name: t('header.routes.account.notificationSettings'),
                    icon: <BellOutlined/>
                }
            ],
            visible: Boolean(userState.user.role >= 1) as boolean
        },
        {
            path: '/agencyCenter',
            name: t('header.dropdownMenu.agencyCenter'),
            icon: <ShopOutlined/>,
            routes: [
                {
                    path: 'profile',
                    name: t('header.dropdownMenu.agencyCenter'),
                    icon: <ShopOutlined/>
                }
            ],
            visible: false
        },
        {
            path: '/tools',
            name: t('header.routes.tools.default'),
            icon: <ProductFilled/>,
            routes: [
                {
                    path: '/tools/file',
                    icon: <FolderOpenFilled/>,
                    name: t('header.routes.tools.fileUpload')
                },
                {
                    path: '/tools/quicksk',
                    icon: <FilterFilled/>,
                    name: t('header.routes.tools.keyExtraction')
                },
                {
                    path: '/tools/multiplierCalculator',
                    icon: <FilterFilled/>,
                    name: t('header.routes.tools.multiplierCalculator')
                },
                {
                    path: '/tools/customPrompts',
                    icon: <ReadFilled/>,
                    name: t('header.routes.tools.customPrompts')
                },
                {
                    path: '/tools/redis',
                    icon: <DatabaseOutlined />,
                    name: t('header.routes.tools.redis'),
                    visible: userState.user.role === 100
                },
                {
                    path: '/tools/server-log',
                    icon: <FileTextOutlined />,
                    name: t('header.routes.tools.serverLog'),
                    visible: userState.user.role === 100  // 只有超级管理员可见
                },
                {
                    path: '/tools/ratioCompare',
                    icon: <SwapOutlined />,
                    name: t('header.routes.tools.ratioCompare')
                }
            ],
            visible: userState.user.role === 100 || (userState.user.role === 10 && hasPermission(ADMIN_ACCESS_FLAGS, Permission.Setting))
        },
        {
            path: '/query',
            name: t('header.routes.query'),
            icon: <MoneyCollectFilled/>,
            visible: Boolean(statusState.status.GuestQueryEnabled) as boolean
        },
        {
            path: '/agencyCenter/profile',
            name: t('header.routes.agencyJoin'),
            icon: <ShopOutlined/>,
            visible: Boolean(statusState.status.AgentMenuEnabled) as boolean
        },
        {
            path: `/status`,
            name: t('header.routes.status'),
            icon: <SecurityScanFilled/>,
            visible: statusState.status.StatusPageUrl !== ''
        },
        {
            path: '/dynamicRouter',
            name: t('header.routes.dynamicRouter'),
            icon: <ApiOutlined />,
            visible: userState.user.role === 100 // 只对超级管理员可见
        },
        ...navExtMenus,
    ].filter((item) => {
        // 过滤隐藏的路由（过滤原理：如果在CustomHideRoute中找不到当前路由的path，则显示）
        return !safeParseJSON(statusState.customConfig.CustomHideRoute, []).includes(item.path);
    });

    const finallyRoutes = filterRoutes(routes);


    const [notice, setNotice] = useState('' as string | null);
    const [openNoticeDrawer, setOpenNoticeDrawer] = useState(false);

    useEffect(() => {
        if (statusState.status.NoticeVersion !== '') {
            // 先检查是否在今天已经选择了不再提示
            const hideUntil = localStorage.getItem('hideNoticeUntil');
            if (hideUntil === new Date().toDateString()) {
                return;
            }
            
            if (statusState.status.NoticeVersion !== localStorage.getItem('LastNoticeVersion') || statusState.status.NoticeVersion === 'always') {
                showNotice().then();
            }
        }
    }, [statusState.status.NoticeVersion]);

    async function showNotice() {
        const res = await API.get('/api/notice');
        const {success, data} = res.data;
        if (success && data !== '') {
            setNotice(data as string);
            setOpenNoticeDrawer(true);
            localStorage.setItem('LastNoticeVersion', statusState.status.NoticeVersion);
        } else {
            AntdMessage.info(t('message.noNotice'));
        }
    }

    const handleNeverShow = () => {
        localStorage.setItem('hideNoticeUntil', new Date().toDateString());
        setOpenNoticeDrawer(false);
    };

    // 如果开启了限制访问，且当前页面是限制访问的页面，则显示对应的内容
    if (statusState.status.LimitedAccessConfigs) {
        try {
            const configs = JSON.parse(statusState.status.LimitedAccessConfigs);
            const matchedConfig = configs.find(config => config.url === window.location.host);
            if (matchedConfig) {
                switch (matchedConfig.type) {
                    case 'blank':
                        return null;
                    case 'query':
                        return (
                            <div style={{padding: 30}}>
                                <Query/>
                            </div>
                        )
                    case 'about':
                        return (
                            <div style={{padding: 30}}>
                                <About/>
                            </div>
                        )
                    default:
                        return null;
                }
            }
        } catch (e) {
            console.error('Failed to parse LimitedAccessConfigs:', e);
        }
    } else if (window.location.host === statusState.status.LimitedAccessURL) {
        switch (statusState.status.LimitedAccessType) {
            case 'blank':
                return null;
            case 'query':
                return (
                    <div style={{padding: 30}}>
                        <Query/>
                    </div>
                )
            case 'about':
                return (
                    <div style={{padding: 30}}>
                        <About/>
                    </div>
                )
            default:
                return null;
        }
    }

    const isHomeOrChat = location.pathname === '/' || location.pathname === '/status' || /^\/chat(\/.*)?$/.test(location.pathname);

    const languageOptions = [
        { key: 'en', label: 'English' },
        { key: 'zh-CN', label: '简体中文' },
        { key: 'zh-TW', label: '繁體中文' },
        { key: 'es', label: 'Español' },
        { key: 'fr', label: 'Français' },
        { key: 'de', label: 'Deutsch' },
        { key: 'jp', label: '日本語' },
        { key: 'kr', label: '한국어' },
        { key: 'ru', label: 'Русский' },
        { key: 'it', label: 'Italiano' },
        { key: 'pt', label: 'Português' },
        { key: 'ar', label: 'العربية' }
    ];

    const LanguageMenu = (
        <Menu onClick={({ key }) => i18n.changeLanguage(key)}>
            {languageOptions.map(option => (
                <Menu.Item key={option.key}>{option.label}</Menu.Item>
            ))}
        </Menu>
    );

    useEffect(() => {
        // 添加全局样式来修复移动端菜单背景
        const styleId = 'mobile-menu-style';
        if (!document.getElementById(styleId)) {
            const style = document.createElement('style');
            style.id = styleId;
            style.textContent = `
                .ant-pro-sider {
                    background: ${darkMode ? '#000000' : '#fff'} !important;
                }
                .ant-menu {
                    background: ${darkMode ? '#000000' : '#fff'} !important;
                }
                .ant-pro-sider-collapsed-button {
                    background: ${darkMode ? '#000000' : '#fff'} !important;
                }
            `;
            document.head.appendChild(style);
        }

        return () => {
            const existingStyle = document.getElementById(styleId);
            if (existingStyle) {
                existingStyle.remove();
            }
        };
    }, [darkMode]);

    return (
        <>
            <ProLayout
                siderWidth={185}
                pure={(statusState.status.PureHomePageEnabled && location.pathname === '/') || (location.pathname === '/independentQuery')}
                breadcrumbRender={false as any}
                token={{
                    bgLayout: darkMode ? '#000000' : '#fff',
                    pageContainer: {
                        paddingInlinePageContainerContent: isHomeOrChat ? 0 : isMobile() ? 12 : 36,
                        paddingBlockPageContainerContent: isHomeOrChat ? 0 : isMobile() ? 12 : 36,
                    }
                }}
                title={false}
                logo={<Image src={'/logo-openainext.png'} preview={false} style={{width: "auto", height: '32px'}}/>}
                layout={theme as 'side' | 'top' | 'mix'}
                defaultCollapsed={isMobile() ? undefined : statusState.customConfig.CustomMenuAutoCollapsed}
                breakpoint={isMobile() ? undefined : false}
                route={{routes: finallyRoutes} as any}
                appList={safeParseJSON(statusState.status.CustomAppList, [])}
                onMenuHeaderClick={() => navigate('/')}
                location={location}
                splitMenus={theme === 'mix'}
                fixedHeader={true}
                pageTitleRender={false}
                menuItemRender={(item, dom) => {
                    if (item.useIframe && item.path) {
                        return <a onClick={() => showIframe(item.path)}>{dom}</a>;
                    }
                    return <Link to={item.path || '/'}>{dom}</Link>;
                }}
                avatarProps={{
                    src: '/avatar.webp',
                    title: userState.user.username,
                    render: (_props, dom) => {
                        if (userState.user.id !== 0) {
                            return (
                                <Dropdown
                                    menu={{
                                        items: [
                                            statusState.status.CheckinEnabled ? {
                                                key: 'checkin',
                                                icon: <CarryOutOutlined />,
                                                label: t('header.dropdownMenu.checkin')
                                            } : null,
                                            {
                                                key: 'profile',
                                                icon: <IdcardOutlined />,
                                                label: t('header.dropdownMenu.profile')
                                            },
                                            {
                                                key: 'recharge',
                                                icon: <DollarCircleOutlined />,
                                                label: t('header.dropdownMenu.recharge')
                                            },
                                            statusState.status.AgentMenuEnabled ? {
                                                key: 'agencyCenter',
                                                icon: <ShopOutlined />,
                                                label: t('header.dropdownMenu.agencyCenter')
                                            } : null,
                                            {
                                                key: 'darkMode',
                                                icon: darkMode ? <SunOutlined /> : <MoonOutlined />,
                                                label: darkMode ? t('header.dropdownMenu.darkMode.disable') : t('header.dropdownMenu.darkMode.enable')
                                            },
                                            {
                                                key: 'fullScreen',
                                                icon: <FullscreenOutlined />,
                                                label: t('header.dropdownMenu.fullScreen.default')
                                            },
                                            {
                                                key: 'logout',
                                                icon: <LogoutOutlined />,
                                                label: t('header.dropdownMenu.logout')
                                            }
                                        ],
                                        onClick: avatarPropsMenuClick
                                    }}>
                                    {dom}
                                </Dropdown>
                            );
                        } else {
                            if (theme === 'side') {
                                return <Button type='text' onClick={() => navigate('/login')} icon={<LoginOutlined/>}
                                               size='large'/>
                            } else {
                                return <Button type='primary' onClick={() => navigate('/login')}
                                               icon={<LoginOutlined/>}>{t('header.avatarProps.login')}</Button>
                            }
                        }
                    }
                }}

                //授权过期提示
                footerRender={() => {
                    return isAdmin(userState) && shouldShowAlert &&
                        <LicenseExpireAlert licenseExpireMessage={licenseExpireMessage}/>;
                }}

                // 超链接栏，小图标（github 等）
                actionsRender={(props) => {
                    if (props.isMobile) return [];
                    return [
                        <>
                            {<Popconfirm key='rmLocalStorage' title={t('message.removeLocalStorage.confirm')}
                                         onConfirm={removeLocalStorage}><ExperimentOutlined
                                style={{margin: 5}}/></Popconfirm>}
                            {theme !== 'side' && (
                                <Dropdown overlay={LanguageMenu} placement="bottomRight">
                                    <GlobalOutlined style={{ margin: 5, cursor: 'pointer' }} />
                                </Dropdown>
                            )}
                            {theme !== 'side' && <NotificationOutlined onClick={showNotice} style={{margin: 5}}/>}
                        </>
                    ];
                }}
            >
                <PageContainer
                    childrenContentStyle={{padding: (isMobile() && isHomeOrChat) ? 0 : isMobile() ? 12 : undefined}}>
                    <App/>
                    {statusState.status.FloatButtonEnabled && !isMobile() && <FloatButton statusState={statusState}/>}
                </PageContainer>
            </ProLayout>
            <NoticeModal
                openNoticeDrawer={openNoticeDrawer}
                setOpenNoticeDrawer={setOpenNoticeDrawer}
                notice={notice as string}
                onNeverShow={handleNeverShow}
            />
            <CheckinPopover
                statusState={statusState}
                TurnstileEnabled={TurnstileEnabled}
                CaptchaEnabled={CaptchaEnabled}
                handelDoCheckin={handelDoCheckin}
                setTurnstileToken={setTurnstileToken}
                setCaptchaId={setCheckinCaptchaId}
                picPath={checkinPicPath}
                setPicPath={setCheckinPicPath}
                captcha={checkinCaptcha}
                setCaptcha={setCheckinCaptcha}
                isCheckinModalVisible={checkinPopoverOpen}
                setIsCheckinModalVisible={setCheckinPopoverOpen}
            />
            {iframeVisible && (
                <div style={{
                    position: 'fixed',
                    top: '64px', // 根据您的顶部菜单栏高度调整
                    left: 0,
                    right: 0,
                    bottom: 0,
                    zIndex: 1000
                }}>
                    <iframe
                        src={iframeUrl}
                        style={{
                            width: '100%',
                            height: '100%',
                            border: 'none'
                        }}
                        title="External Content"
                    />
                    <Button
                        onClick={hideIframe}
                        style={{
                            position: 'absolute',
                            top: '10px',
                            right: '10px',
                            zIndex: 1001
                        }}
                        icon={<CloseOutlined />}
                    >
                        关闭
                    </Button>
                </div>
            )}
        </>
    );
}

export default Header;
