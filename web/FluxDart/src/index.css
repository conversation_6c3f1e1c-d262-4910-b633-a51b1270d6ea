/* src/index.css */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

.flash-highlight {
    animation: flash-animation 1s ease-out;
}

@keyframes flash-animation {
    0% {
        background-color: rgba(255, 215, 0, 0.8);
    }
    100% {
        background-color: rgba(255, 215, 0, 0.1);
    }
}

.highlight-indicator {
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border: 2px solid #ffd700;
    border-radius: 8px;
    pointer-events: none;
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 215, 0, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 215, 0, 0);
    }
}

/* API端点轮播动画 - 从下往上连续滚动效果 */
.api-endpoint-container {
    position: relative;
    overflow: hidden;
    height: 1.2em;
    display: inline-block;
    min-width: 160px;
}

/* 静态显示状态 */
.api-endpoint-current {
    color: #3b82f6;
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
    text-align: right;
    height: 1.2em;
    line-height: 1.2em;
    margin: 0;
    padding: 0;
}

/* 滚动动画容器 */
.api-endpoint-scroll {
    position: relative;
    transition: transform 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transform: translateY(0);
}

.api-endpoint-text {
    display: block;
    height: 1.2em;
    line-height: 1.2em;
    color: #3b82f6;
    font-weight: 600;
    font-size: 14px;
    white-space: nowrap;
    text-align: right;
    margin: 0;
    padding: 0;
}

/* 滚动动画 - 从下往上移动 */
.api-endpoint-scroll.scrolling {
    transform: translateY(-1.2em);
}

/* 跳跃动画 - 只在静态状态下生效 */
@keyframes bounce-settle {
    0% { transform: translateY(0); }
    20% { transform: translateY(-3px); }
    40% { transform: translateY(-1px); }
    60% { transform: translateY(-2px); }
    80% { transform: translateY(-0.5px); }
    100% { transform: translateY(0); }
}

.api-endpoint-current.bounce-active {
    animation: bounce-settle 0.5s ease-out;
}

/* 移动端适配 */
@media (max-width: 768px) {
    .api-endpoint-container {
        min-width: auto;
        text-align: center;
        height: 1em;
    }

    .api-endpoint-current {
        font-size: 12px;
        text-align: center;
        height: 1em;
        line-height: 1em;
    }

    .api-endpoint-text {
        font-size: 12px;
        text-align: center;
        height: 1em;
        line-height: 1em;
    }

    .api-endpoint-scroll.scrolling {
        transform: translateY(-1em);
    }
}

