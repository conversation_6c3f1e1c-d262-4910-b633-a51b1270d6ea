import React from "react";

export interface RouteConfig {
    path: string;
    name: string;
    icon?: React.ReactNode; // 允许任何类型的 React 元素
    routes?: RouteConfig[]; // 可选的子路由数组
    visible?: boolean; // 可选的可见性标志
}

export interface UserType {
    id: number;
    // uid: string;
    username: string;
    password: string;
    display_name: string;
    role: number;
    status: number;
    email: string;
    github_id: string;
    google_id: string;
    wechat_id: string;
    telegram_id: string;
    verification_code: string;
    inviteUserNumber: number;
    access_token: string;
    quota: number;
    used_quota: number;
    invite_bonus_quota: number;
    aff_quota: number;
    aff_history_quota: number;
    request_count: number;
    group: string;
    group_display_name: string;
    aff_code: string;
    inviter_id: number;
    last_login_time: number;
    last_login_ip: string;
    rate_limit: number;
    rate_limit_exceeded_message: string;
    remark: string;
    quota_expire_time: number;
    TopupGroupMinLimit: number;
    ModelFixedPrice: any;
    phone_number: string;
    sms_verification_code: string;
    admin_access_flags: number;
    InviteBonusRatio:number;
    ShowPackagePlan:boolean;
    timezone: string;
    // Redis相关字段
    redis_quota?: number;
    redis_quota_expire_time?: number;
    quota_warning?: string;
    quota_diff?: number;
}

export interface StatusType {
    CaptchaCheckEnabled: boolean;
    CheckinEnabled: boolean;
    CheckinQuota: number;
    CustomAppList: string;
    CustomThemeConfig: string;
    CustomDarkThemeConfig: string;
    CustomVerificationTypesConfig: {
        checkin: {
            displayName: string;
            displayNameEn: string;
            verificationTypes: string[];
            apiEndpoints: string[];
            pageRoute: string;
        };
        login: {
            displayName: string;
            displayNameEn: string;
            verificationTypes: string[];
            apiEndpoints: string[];
            pageRoute: string;
        };
        register: {
            displayName: string;
            displayNameEn: string;
            verificationTypes: string[];
            apiEndpoints: string[];
            pageRoute: string;
        };
        reset_password: {
            displayName: string;
            displayNameEn: string;
            verificationTypes: string[];
            apiEndpoints: string[];
            pageRoute: string;
        };
        verification: {
            displayName: string;
            displayNameEn: string;
            verificationTypes: string[];
            apiEndpoints: string[];
            pageRoute: string;
        };
        send_sms: {
            displayName: string;
            displayNameEn: string;
            verificationTypes: string[];
            apiEndpoints: string[];
            pageRoute: string;
        };
    };
    DebugEnabled: boolean;
    DocumentInfo: string;
    FloatButtonEnabled: boolean;
    GuestQueryEnabled: boolean;
    GuestChatPageEnabled: boolean;
    PptGenPageEnabled: boolean;
    AgentMenuEnabled: boolean;
    LimitedAccessType: string;
    LimitedAccessURL: string;
    LimitedAccessConfigs: string;
    LogDetailConsumeEnabled: boolean;
    MaxTopUpLimit: number;
    MidjourneyEnabled: boolean;
    MidjourneyShowDerivedRatesEnabled: boolean;
    OnlineTopupEnabled: boolean;
    CustomAvailablePayMethods: number; //自定义可用支付方式，99：全部，1：仅支付宝，2：仅微信，3：仅QQ钱包（示例）
    CustomAvailablePayMethods2: number; //自定义可用支付方式，99：全部，1：仅支付宝，2：仅微信，3：仅QQ钱包（示例）
    
    // 只保留是否启用支付宝当面付的标志
    AlipayFaceToFaceEnabled: boolean; // 是否启用支付宝当面付
    // 随行付聚合支付启用标志
    SuixingpayEnabled: boolean; // 是否启用随行付聚合支付

    RegisterEnabled: boolean;
    PasswordRegisterEnabled: boolean;
    PureHomePageEnabled: boolean;
    QqInfo: string;
    QuotaExpireDays: number;
    QuotaExpireEnabled: boolean;
    RegisterInfo: string;
    SMSLoginEnabled: boolean;
    SMSRegisterEnabled: boolean;
    SMSVerificationEnabled: boolean;
    ShellApiLogOptimizerEnabled: boolean;
    SiteDescription: string;
    SwitchUIEnabled: boolean;
    TransferEnabled: boolean;
    UnsubscribeEnabled: boolean;
    UserLogViewEnabled: boolean;
    WechatInfo: string;
    chat_link: string;
    display_in_currency: boolean;
    email_verification: boolean;
    file_system_server_address: string;
    footer_html: string;
    github_client_id: string;
    github_oauth: boolean;
    GoogleOAuthEnabled: boolean;
    GoogleClientId: string;
    TelegramOAuthEnabled: boolean;
    TelegramBotName: string;
    header_script: string;
    instanceId: string;
    logo: string;
    price: number;
    quota_per_unit: number;
    server_address: string;
    start_time: number;
    system_name: string;
    top_up_link: string;
    turnstile_check: boolean;
    turnstile_site_key: string;
    version: string;
    wechat_login: boolean;
    wechat_qrcode: string;
    NoticeVersion: string;//公告标记，用于判断是否需要重新显示公告，如果和 localStorage 中的值相同或者为空，则不弹出公告
    StatusPageUrl: string;
    NavExtMenus:string;
    NewHomeConf:string;
    TokenGroupChangeEnabled:boolean;
    eise: boolean;
    DataExportEnabled: boolean;
    DataExportInterval: number;
    DataExportDefaultTime: string;
    DataExportDisplayEnabled: boolean;
}

export interface customConfigType {
    darkMode: boolean;
    CustomTheme: string;
    CustomItemPerPage: string;
    // CustomHideRoute: string[];
    CustomHideRoute: string;
    CustomAmount: string;
    CustomShareText: string;
    CustomMenuAutoCollapsed: boolean;
    CustomLogQueryDuration: number;
    CustomAutoGetLogStat: boolean;
    // 新增配置项：是否默认只显示当天的查询
    CustomDefaultTimeTodayOnly?: boolean;
    // 新增配置项：默认的使用情况统计时间单位（hour, day, week, month）
    CustomUsageStatsDefaultTimeUnit?: string;
}

export interface ChatLink {// 定义一个接口来描述 chatLinkObj 的结构
    link: string;
    type: number;
}

export interface CaptchaResponse {
    success: boolean;
    data?: {
        captchaId: string;
        picPath: string;
    };
    message?: string;
}

export interface DefaultResponse {
    success: boolean;
    message: string;
    data: any;
}

export interface FileInfo {
    name: string;
    url: string;
}

export interface UserInfoById {
    id: number;
    username: string;
    password: string;
    display_name: string;
    role: number;
    status: number;
    email: string;
    github_id: string;
    wechat_id: string;
    verification_code: string;
    inviteUserNumber: number;
    access_token: string;
    quota: number;
    used_quota: number;
    invite_bonus_quota: number;
    request_count: number;
    group: string;
    aff_code: string;
    inviter_id: number;
    last_login_time: number;
    last_login_ip: string;
    rate_limit: number;
    rate_limit_exceeded_message: string;
    remark: string;
    quota_expire_time: number;
    TopupGroupMinLimit: number;
    ModelFixedPrice: any;
    phone_number: string;
    sms_verification_code: string;
    admin_access_flags: number;
    model_ratio: string;
    model_fixed_price: string;
    completion_ratio: string;
    mj_sensitive_words_refund: number | null;
    group_discounts: string;
    route_discounts?: string;
    trust_upstream_stream_usage?: number;
    force_stream_option?: number;
    force_downstream_stream_usage?: number;
    say1_direct_success_mode?: number;
    mock_openai_complete_format?: number;
    extra_visible_groups?: string;
    // 日志相关字段
    log_detail_enabled?: number;
    log_downstream_error_enabled?: number;
    log_upstream_response_enabled?: number;
    log_full_response_enabled?: number;
    max_prompt_log_length?: number;
    // Redis相关字段
    redis_quota?: number;
    redis_quota_expire_time?: number;
    quota_warning?: string;
    quota_diff?: number;
}


// [
// {
//     "name": "v1.6.0",
//     "zipball_url": "https://api.github.com/repos/akl7777777/shell-api/zipball/refs/tags/v1.6.0",
//     "tarball_url": "https://api.github.com/repos/akl7777777/shell-api/tarball/refs/tags/v1.6.0",
//     "commit": {
//         "sha": "dbdadb7ba739b9e2f612169d261d63c2110f8320",
//         "url": "https://api.github.com/repos/akl7777777/shell-api/commits/dbdadb7ba739b9e2f612169d261d63c2110f8320"
//     },
//     "node_id": "REF_kwDOKLusBLByZWZzL3RhZ3MvdjEuNi4w"
// }
// ]

export interface GithubReleaseResponse {
    name: string;
    zipball_url: string;
    tarball_url: string;
    commit: {
        sha: string;
        url: string;
    };
    node_id: string;
}