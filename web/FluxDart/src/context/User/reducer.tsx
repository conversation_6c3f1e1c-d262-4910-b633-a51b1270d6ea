// reducer.ts
import {UserType} from "../../constants";
import {deepMergeObjects, safeParseJSON} from "../../helpers";

export interface UserState {
    user: UserType;
}

export type UserAction = { type: 'login', payload: UserType } | { type: 'logout' } | {
    type: 'update',
    payload: UserType
};

// 尝试从localStorage获取用户状态，如果不存在则使用默认初始状态
export const getInitialUserState = (): UserState => {
    const savedState = localStorage.getItem('USER_STATE');
    try {
        if (savedState) {
            const parsedState = safeParseJSON(savedState, initialState);
            const mergedState = JSON.parse(JSON.stringify(initialState)); // 创建initialState的深拷贝

            deepMergeObjects(mergedState, parsedState); // 使用导出的deepMergeObjects函数递归合并

            const mergedStateStr = JSON.stringify(mergedState); // 提前将mergedState转换为字符串

            // 比较mergedStateStr和parsedState是否不同，如果不同则保存到localStorage
            if (mergedStateStr !== JSON.stringify(parsedState)) {
                localStorage.setItem('USER_STATE', mergedStateStr);
            }
            return mergedState;
        }
    } catch (e) {
        console.error("Failed to parse user state from localStorage:", e);
    }
    return initialState;
}

export const initialState: UserState = {
    user: {
        id: 0,// 使用0作为未登录的标志
        // uid: '',
        username: '',
        password: '',
        display_name: '',
        role: 0,
        status: 0,
        email: '',
        github_id: '',
        google_id: '',
        wechat_id: '',
        telegram_id: '',
        verification_code: '',
        inviteUserNumber: 0,
        access_token: '',
        quota: 0,
        used_quota: 0,
        invite_bonus_quota: 0,
        aff_quota:0,
        aff_history_quota:0,
        request_count: 0,
        group: '',
        group_display_name: '',
        aff_code: '',
        inviter_id: 0,
        last_login_time: 0,
        last_login_ip: '',
        rate_limit: 0,
        rate_limit_exceeded_message: '',
        remark: '',
        quota_expire_time: 0,
        TopupGroupMinLimit: 0,
        ModelFixedPrice: {},
        phone_number: '',
        sms_verification_code: '',
        admin_access_flags: 0,
        InviteBonusRatio:0,
        ShowPackagePlan:false,
        timezone: 'UTC',
    }
};

export const reducer = (state: UserState, action: UserAction): UserState => {
    switch (action.type) {
        case 'login':
            try {
                localStorage.setItem('USER_STATE', JSON.stringify({user: action.payload}));
            } catch (e) {
                console.error("Failed to save user state to localStorage:", e);
            }
            return {
                ...state,
                user: action.payload
            };
        case 'logout':
            localStorage.removeItem('user');// 清空localStorage中的用户数据（旧版本使用的字段名）
            localStorage.removeItem('USER_STATE'); // 清空localStorage中的用户状态（新版本使用的字段名）
            return {
                ...state,
                user: initialState.user// 清空而不是读取localStorage，确保登出后不会残留任何用户数据。
            };
        case 'update':
            try {
                localStorage.setItem('USER_STATE', JSON.stringify({user: action.payload}));
            } catch (e) {
                console.error("Failed to save user state to localStorage:", e);
            }
            return {
                ...state,
                user: action.payload
            };
        default:
            return state;
    }
};
