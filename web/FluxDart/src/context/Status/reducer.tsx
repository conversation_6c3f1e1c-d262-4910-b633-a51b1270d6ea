// contexts/Status/reducer.ts
import {customConfigType, StatusType, SYSTEM_NAME} from '../../constants';
import {deepMergeObjects, safeParseJSON} from "../../helpers";

export interface StatusState {
    customConfig: customConfigType;// 本地存储的自定义配置
    status: StatusType;// 从服务器获取的状态信息
}

export type StatusAction =
    | { type: 'set'; payload: StatusType; }
    | { type: 'unset'; }
    | { type: 'updateCustomConfig'; payload: customConfigType; }
    | { type: 'resetCustomConfig'; }
    | { type: 'removeAll'; }

// 尝试从localStorage获取状态信息，如果不存在则使用默认初始状态
export const getInitialStatusState = (): StatusState => {
    const savedState = localStorage.getItem('STATUS_STATE');
    try {
        if (savedState) {
            const parsedState = safeParseJSON(savedState, initialState);
            const mergedState = JSON.parse(JSON.stringify(initialState)); // 创建initialState的深拷贝
            deepMergeObjects(mergedState, parsedState); // 递归合并parsedState到mergedState
            // 提前将mergedState转换为字符串
            const mergedStateStr = JSON.stringify(mergedState);
            if (mergedStateStr !== JSON.stringify(parsedState)) {
                localStorage.setItem('STATUS_STATE', mergedStateStr);// 如果mergedStateStr和parsedState不同则保存到localStorage
            }
            return mergedState;
        }
    } catch (e) {
        console.error("Failed to getInitialStatusState:", e);
    }
    return initialState;
}


export const initialState: StatusState = {
    customConfig: {
        darkMode: false,
        CustomTheme: 'mix',
        CustomItemPerPage: '10',
        CustomHideRoute: `["/about"]`,
        CustomAmount: '',
        CustomShareText: '接口：{base_url}\n密钥：{key}\n余额：${balance}\n支持模型：{models}',
        CustomMenuAutoCollapsed: false,
        CustomLogQueryDuration: 0,
        CustomAutoGetLogStat: true,
        CustomDefaultTimeTodayOnly: false,
        CustomUsageStatsDefaultTimeUnit: 'day',
    },
    status: {
        CaptchaCheckEnabled: false,
        CheckinEnabled: false,
        CheckinQuota: 0,
        CustomAppList: '',
        CustomThemeConfig: '',
        CustomDarkThemeConfig: '',
        CustomVerificationTypesConfig: {
            checkin: {
                displayName: '',
                displayNameEn: '',
                verificationTypes: [],
                apiEndpoints: [],
                pageRoute: '',
            },
            login: {
                displayName: '',
                displayNameEn: '',
                verificationTypes: [],
                apiEndpoints: [],
                pageRoute: '',
            },
            register: {
                displayName: '',
                displayNameEn: '',
                verificationTypes: [],
                apiEndpoints: [],
                pageRoute: '',
            },
            reset_password: {
                displayName: '',
                displayNameEn: '',
                verificationTypes: [],
                apiEndpoints: [],
                pageRoute: '',
            },
            verification: {
                displayName: '',
                displayNameEn: '',
                verificationTypes: [],
                apiEndpoints: [],
                pageRoute: '',
            },
            send_sms: {
                displayName: '',
                displayNameEn: '',
                verificationTypes: [],
                apiEndpoints: [],
                pageRoute: '',
            },
        },
        DebugEnabled: false,
        DocumentInfo: '',
        FloatButtonEnabled: false,
        GuestQueryEnabled: false,
        LimitedAccessType: '',
        LimitedAccessURL: '',
        LimitedAccessConfigs: '',
        LogDetailConsumeEnabled: false,
        MaxTopUpLimit: 0,
        MidjourneyEnabled: false,
        MidjourneyShowDerivedRatesEnabled: false,
        OnlineTopupEnabled: false,
        CustomAvailablePayMethods: 0,
        CustomAvailablePayMethods2: 0,
        
        // 只保留是否启用支付宝当面付的标志
        AlipayFaceToFaceEnabled: false,
        // 随行付聚合支付启用标志
        SuixingpayEnabled: false,
        
        GuestChatPageEnabled: true,
        PptGenPageEnabled: false,
        AgentMenuEnabled: false,
        RegisterEnabled: true,
        PasswordRegisterEnabled: false,
        PureHomePageEnabled: false,
        QqInfo: '',
        QuotaExpireDays: 0,
        QuotaExpireEnabled: false,
        RegisterInfo: '',
        SMSLoginEnabled: false,
        SMSRegisterEnabled: false,
        SMSVerificationEnabled: false,
        ShellApiLogOptimizerEnabled: false,
        SiteDescription: '',
        SwitchUIEnabled: false,
        TransferEnabled: false,
        UnsubscribeEnabled: false,
        UserLogViewEnabled: false,
        WechatInfo: '',
        chat_link: '',
        display_in_currency: false,
        email_verification: false,
        file_system_server_address: '',
        footer_html: '',
        github_client_id: '',
        github_oauth: false,
        GoogleOAuthEnabled: false,
        GoogleClientId: '',
        TelegramBotName: '',
        TelegramOAuthEnabled: false,
        header_script: '',
        instanceId: '',
        logo: '',
        price: 0,
        quota_per_unit: 0,
        server_address: '',
        start_time: 0,
        system_name: SYSTEM_NAME,
        top_up_link: '',
        turnstile_check: false,
        turnstile_site_key: '',
        version: 'v0.0.0-unset',
        wechat_login: false,
        wechat_qrcode: '',
        NoticeVersion: '',
        StatusPageUrl: '',
        NavExtMenus:'',
        NewHomeConf:'',
        TokenGroupChangeEnabled: false,
        eise: false,
        DataExportEnabled: false,
        DataExportInterval: 5,
        DataExportDefaultTime: 'hour',
        DataExportDisplayEnabled: false,
    },
};

export const reducer = (state: StatusState, action: StatusAction): StatusState => {
    switch (action.type) {
        case 'set':
            // localStorage.setItem('STATUS_STATE', JSON.stringify(action.payload));// 将状态信息保存到localStorage
            // 将整个状态信息保存到localStorage，而不是只保存status字段
            localStorage.setItem('STATUS_STATE', JSON.stringify({...state, status: action.payload}));
            return {
                ...state,
                status: action.payload,
            };
        case 'unset':
            localStorage.removeItem('status');// 从localStorage中移除状态信息（旧版本字段）
            // 更新状态信息，使用默认初始状态覆盖本地 localStorage 中的状态信息，但是保留自定义配置
            localStorage.setItem('STATUS_STATE', JSON.stringify({...initialState, customConfig: state.customConfig}));
            return {
                ...state,
                status: initialState.status,// 使用默认初始状态而不是从localStorage中获取的状态信息
            };
        case 'updateCustomConfig':// 更新自定义配置，保留原有的配置，只更新传入的配置
            localStorage.setItem('STATUS_STATE', JSON.stringify({
                ...state,
                customConfig: {...state.customConfig, ...action.payload}
            }));
            return {
                ...state,
                customConfig: {
                    ...state.customConfig,
                    ...action.payload
                }
            };
        case 'resetCustomConfig':
            return {...state, customConfig: initialState.customConfig};
        case 'removeAll':
            localStorage.removeItem('STATUS_STATE');// 从localStorage中移除状态信息
            return initialState;
        default:
            return state;
    }
};
